# Arien - Elite AI-Powered Software Engineering Assistant
# Copyright 2025 Arien LLC - MIT License

# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
arien
arien-*

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out
coverage.html

# Dependency directories
vendor/

# Go workspace file
go.work

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Configuration files with sensitive data
.env
.env.local
.env.*.local

# Arien specific
.arien/
.arien-memory.json
tasks.json
config.yaml

# Temporary files
tmp/
temp/
*.tmp

# Build artifacts
dist/
build/

# Docker
.dockerignore

# Node modules (if any frontend components added later)
node_modules/

# Python (if any Python tools added)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/

# Backup files
*.bak
*.backup
*~
