/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
)

// LsTool implements enhanced directory listing
type LsTool struct{}

// NewLsTool creates a new ls tool
func NewLsTool() *LsTool {
	return &LsTool{}
}

// Name returns the tool name
func (t *LsTool) Name() string {
	return "ls"
}

// Description returns the tool description
func (t *LsTool) Description() string {
	return "Enhanced directory listing with filtering, sorting, and formatting options"
}

// Parameters returns the tool parameter schema
func (t *LsTool) Parameters() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"path": StringParameter("Directory path to list (default: current directory)", false),
			"all": BoolParameter("Show hidden files and directories", false),
			"long": BoolParameter("Use long listing format with details", false),
			"recursive": BoolParameter("List directories recursively", false),
			"filter": StringParameter("Filter files by pattern (glob)", false),
			"sort": map[string]interface{}{
				"type": "string",
				"description": "Sort by: name, size, time, type",
				"enum": []string{"name", "size", "time", "type"},
				"default": "name",
			},
			"reverse": BoolParameter("Reverse sort order", false),
			"max_depth": IntParameter("Maximum recursion depth (default: 3)", 1, 10),
		},
		"required": []string{},
	}
}

// Execute executes the ls tool
func (t *LsTool) Execute(ctx context.Context, args map[string]interface{}) Result {
	// Parse arguments
	path := "."
	if p, ok := args["path"].(string); ok && p != "" {
		path = p
	}

	showAll := false
	if a, ok := args["all"].(bool); ok {
		showAll = a
	}

	longFormat := false
	if l, ok := args["long"].(bool); ok {
		longFormat = l
	}

	recursive := false
	if r, ok := args["recursive"].(bool); ok {
		recursive = r
	}

	filter := ""
	if f, ok := args["filter"].(string); ok {
		filter = f
	}

	sortBy := "name"
	if s, ok := args["sort"].(string); ok {
		sortBy = s
	}

	reverse := false
	if r, ok := args["reverse"].(bool); ok {
		reverse = r
	}

	maxDepth := 3
	if d, ok := args["max_depth"].(float64); ok {
		maxDepth = int(d)
	}

	// Execute listing
	output, err := t.listDirectory(path, showAll, longFormat, recursive, filter, sortBy, reverse, maxDepth, 0)
	if err != nil {
		return Result{Error: err}
	}

	return Result{
		Output: output,
		Metadata: map[string]interface{}{
			"path":      path,
			"recursive": recursive,
			"filter":    filter,
			"sort":      sortBy,
		},
	}
}

// Validate validates the tool arguments
func (t *LsTool) Validate(args map[string]interface{}) error {
	// Basic validation
	if path, ok := args["path"].(string); ok && path != "" {
		if _, err := os.Stat(path); err != nil {
			return fmt.Errorf("path does not exist: %s", path)
		}
	}

	if sort, ok := args["sort"].(string); ok {
		validSorts := []string{"name", "size", "time", "type"}
		valid := false
		for _, v := range validSorts {
			if sort == v {
				valid = true
				break
			}
		}
		if !valid {
			return fmt.Errorf("invalid sort option: %s", sort)
		}
	}

	return nil
}

// SupportsParallel returns true if the tool can be executed in parallel
func (t *LsTool) SupportsParallel() bool {
	return true
}

// listDirectory performs the actual directory listing
func (t *LsTool) listDirectory(path string, showAll, longFormat, recursive bool, filter, sortBy string, reverse bool, maxDepth, currentDepth int) (string, error) {
	if recursive && currentDepth >= maxDepth {
		return "", nil
	}

	entries, err := os.ReadDir(path)
	if err != nil {
		return "", fmt.Errorf("failed to read directory %s: %w", path, err)
	}

	// Filter entries
	var filteredEntries []os.DirEntry
	for _, entry := range entries {
		// Skip hidden files unless showAll is true
		if !showAll && strings.HasPrefix(entry.Name(), ".") {
			continue
		}

		// Apply filter if specified
		if filter != "" {
			matched, err := filepath.Match(filter, entry.Name())
			if err != nil {
				return "", fmt.Errorf("invalid filter pattern: %w", err)
			}
			if !matched {
				continue
			}
		}

		filteredEntries = append(filteredEntries, entry)
	}

	// Sort entries
	t.sortEntries(filteredEntries, path, sortBy, reverse)

	// Format output
	var output strings.Builder
	
	if recursive && currentDepth > 0 {
		output.WriteString(fmt.Sprintf("\n%s:\n", path))
	}

	for _, entry := range filteredEntries {
		entryPath := filepath.Join(path, entry.Name())
		
		if longFormat {
			info, err := entry.Info()
			if err != nil {
				output.WriteString(fmt.Sprintf("error: %s - %v\n", entry.Name(), err))
				continue
			}

			// Format: permissions size date name
			perms := info.Mode().String()
			size := info.Size()
			modTime := info.ModTime().Format("Jan 02 15:04")
			
			output.WriteString(fmt.Sprintf("%s %8d %s %s", perms, size, modTime, entry.Name()))
			
			if entry.IsDir() {
				output.WriteString("/")
			}
			output.WriteString("\n")
		} else {
			output.WriteString(entry.Name())
			if entry.IsDir() {
				output.WriteString("/")
			}
			output.WriteString("\n")
		}

		// Recursive listing for directories
		if recursive && entry.IsDir() {
			subOutput, err := t.listDirectory(entryPath, showAll, longFormat, recursive, filter, sortBy, reverse, maxDepth, currentDepth+1)
			if err != nil {
				output.WriteString(fmt.Sprintf("error listing %s: %v\n", entryPath, err))
			} else if subOutput != "" {
				output.WriteString(subOutput)
			}
		}
	}

	return output.String(), nil
}

// sortEntries sorts directory entries based on the specified criteria
func (t *LsTool) sortEntries(entries []os.DirEntry, basePath, sortBy string, reverse bool) {
	sort.Slice(entries, func(i, j int) bool {
		var less bool
		
		switch sortBy {
		case "size":
			infoI, errI := entries[i].Info()
			infoJ, errJ := entries[j].Info()
			if errI != nil || errJ != nil {
				less = entries[i].Name() < entries[j].Name()
			} else {
				less = infoI.Size() < infoJ.Size()
			}
		case "time":
			infoI, errI := entries[i].Info()
			infoJ, errJ := entries[j].Info()
			if errI != nil || errJ != nil {
				less = entries[i].Name() < entries[j].Name()
			} else {
				less = infoI.ModTime().Before(infoJ.ModTime())
			}
		case "type":
			// Sort by type (directories first, then by extension)
			if entries[i].IsDir() && !entries[j].IsDir() {
				less = true
			} else if !entries[i].IsDir() && entries[j].IsDir() {
				less = false
			} else {
				extI := filepath.Ext(entries[i].Name())
				extJ := filepath.Ext(entries[j].Name())
				if extI == extJ {
					less = entries[i].Name() < entries[j].Name()
				} else {
					less = extI < extJ
				}
			}
		default: // name
			less = entries[i].Name() < entries[j].Name()
		}

		if reverse {
			return !less
		}
		return less
	})
}
