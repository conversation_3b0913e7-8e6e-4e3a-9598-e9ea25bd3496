/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package openai

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"arien/internal/config"
	"arien/internal/interfaces"

	"github.com/charmbracelet/log"
	"golang.org/x/time/rate"
)

// Provider implements the OpenAI LLM provider
type Provider struct {
	config  config.ProviderConfig
	logger  *log.Logger
	client  *http.Client
	limiter *rate.Limiter
	baseURL string
}

// OpenAIRequest represents a request to OpenAI API
type OpenAIRequest struct {
	Model       string                   `json:"model"`
	Messages    []OpenAIMessage          `json:"messages"`
	Tools       []OpenAITool             `json:"tools,omitempty"`
	Temperature float64                  `json:"temperature,omitempty"`
	MaxTokens   int                      `json:"max_tokens,omitempty"`
	Stream      bool                     `json:"stream,omitempty"`
}

// OpenAIResponse represents a response from OpenAI API
type OpenAIResponse struct {
	ID      string                 `json:"id"`
	Object  string                 `json:"object"`
	Created int64                  `json:"created"`
	Model   string                 `json:"model"`
	Choices []OpenAIChoice         `json:"choices"`
	Usage   OpenAIUsage            `json:"usage"`
	Error   *OpenAIError           `json:"error,omitempty"`
}

// OpenAIMessage represents a message in OpenAI format
type OpenAIMessage struct {
	Role      string                 `json:"role"`
	Content   string                 `json:"content"`
	Name      string                 `json:"name,omitempty"`
	ToolCalls []OpenAIToolCall       `json:"tool_calls,omitempty"`
}

// OpenAIChoice represents a choice in OpenAI response
type OpenAIChoice struct {
	Index        int                    `json:"index"`
	Message      OpenAIMessage          `json:"message"`
	FinishReason string                 `json:"finish_reason"`
}

// OpenAITool represents a tool definition for OpenAI
type OpenAITool struct {
	Type     string                 `json:"type"`
	Function OpenAIToolFunction     `json:"function"`
}

// OpenAIToolFunction represents a function definition for OpenAI
type OpenAIToolFunction struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
}

// OpenAIToolCall represents a tool call from OpenAI
type OpenAIToolCall struct {
	ID       string                 `json:"id"`
	Type     string                 `json:"type"`
	Function OpenAIFunctionCall     `json:"function"`
}

// OpenAIFunctionCall represents a function call from OpenAI
type OpenAIFunctionCall struct {
	Name      string `json:"name"`
	Arguments string `json:"arguments"`
}

// OpenAIUsage represents usage information from OpenAI
type OpenAIUsage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// OpenAIError represents an error from OpenAI API
type OpenAIError struct {
	Message string `json:"message"`
	Type    string `json:"type"`
	Code    string `json:"code"`
}

// NewProvider creates a new OpenAI provider
func NewProvider(providerConfig config.ProviderConfig, logger *log.Logger) (*Provider, error) {
	if providerConfig.APIKey == ""  {
		return nil, fmt.Errorf("OpenAI API key is required")
	}

	baseURL := providerConfig.BaseURL
	if baseURL == "" {
		baseURL = "https://api.openai.com/v1"
	}

	// Set up rate limiting (OpenAI specific limits)
	rateLimit := providerConfig.RateLimit
	if rateLimit == 0 {
		rateLimit = 60 // Default: 60 requests per minute
	}

	return &Provider{
		config:  providerConfig,
		logger:  logger,
		client:  &http.Client{Timeout: 60 * time.Second},
		limiter: rate.NewLimiter(rate.Every(time.Minute/time.Duration(rateLimit)), 1),
		baseURL: baseURL,
	}, nil
}

// Name returns the provider name
func (p *Provider) Name() string {
	return "openai"
}

// Models returns available models for OpenAI
func (p *Provider) Models() []string {
	return []string{
		"gpt-4",
		"gpt-4-turbo",
		"gpt-3.5-turbo",
		"gpt-4o",
		"gpt-4o-mini",
	}
}

// ProcessMessage processes a message using OpenAI API
func (p *Provider) ProcessMessage(ctx context.Context, request *interfaces.Request) (*interfaces.Response, error) {
	// Rate limiting
	if err := p.limiter.Wait(ctx); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	// Convert request to OpenAI format
	openaiReq := p.convertRequest(request)

	// Make API call
	openaiResp, err := p.makeAPICall(ctx, openaiReq)
	if err != nil {
		return nil, fmt.Errorf("OpenAI API call failed: %w", err)
	}

	// Convert response to standard format
	response := p.convertResponse(openaiResp)
	return response, nil
}

// SupportsToolCalling returns true if OpenAI supports function calling
func (p *Provider) SupportsToolCalling() bool {
	return true
}

// SupportsStreaming returns true if OpenAI supports streaming
func (p *Provider) SupportsStreaming() bool {
	return true
}

// convertRequest converts standard request to OpenAI format
func (p *Provider) convertRequest(request *interfaces.Request) *OpenAIRequest {
	openaiReq := &OpenAIRequest{
		Model:       request.Model,
		Temperature: request.Temperature,
		MaxTokens:   request.MaxTokens,
		Stream:      request.Stream,
	}

	// Convert messages
	for _, msg := range request.Messages {
		openaiMsg := OpenAIMessage{
			Role:    msg.Role,
			Content: msg.Content,
			Name:    msg.Name,
		}

		// Convert tool calls
		for _, toolCall := range msg.ToolCalls {
			args, _ := json.Marshal(toolCall.Arguments)
			openaiMsg.ToolCalls = append(openaiMsg.ToolCalls, OpenAIToolCall{
				ID:   toolCall.ID,
				Type: toolCall.Type,
				Function: OpenAIFunctionCall{
					Name:      toolCall.Name,
					Arguments: string(args),
				},
			})
		}

		openaiReq.Messages = append(openaiReq.Messages, openaiMsg)
	}

	// Convert tools
	for _, tool := range request.Tools {
		openaiReq.Tools = append(openaiReq.Tools, OpenAITool{
			Type: tool.Type,
			Function: OpenAIToolFunction{
				Name:        tool.Function.Name,
				Description: tool.Function.Description,
				Parameters:  tool.Function.Parameters,
			},
		})
	}

	return openaiReq
}

// convertResponse converts OpenAI response to standard format
func (p *Provider) convertResponse(openaiResp *OpenAIResponse) *interfaces.Response {
	response := &interfaces.Response{
		Model:     openaiResp.Model,
		Provider:  p.Name(),
		Timestamp: time.Now(),
		Usage: interfaces.Usage{
			PromptTokens:     openaiResp.Usage.PromptTokens,
			CompletionTokens: openaiResp.Usage.CompletionTokens,
			TotalTokens:      openaiResp.Usage.TotalTokens,
		},
	}

	if openaiResp.Error != nil {
		response.Error = fmt.Errorf("OpenAI API error: %s", openaiResp.Error.Message)
		return response
	}

	if len(openaiResp.Choices) > 0 {
		choice := openaiResp.Choices[0]
		response.Content = choice.Message.Content

		// Convert tool calls
		for _, toolCall := range choice.Message.ToolCalls {
			var args map[string]interface{}
			json.Unmarshal([]byte(toolCall.Function.Arguments), &args)

			response.ToolCalls = append(response.ToolCalls, interfaces.ToolCall{
				ID:        toolCall.ID,
				Type:      toolCall.Type,
				Name:      toolCall.Function.Name,
				Arguments: args,
			})
		}
	}

	return response
}

// makeAPICall makes the actual API call to OpenAI
func (p *Provider) makeAPICall(ctx context.Context, request *OpenAIRequest) (*OpenAIResponse, error) {
	// Serialize request
	reqBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	url := fmt.Sprintf("%s/chat/completions", p.baseURL)
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", p.config.APIKey))

	p.logger.Debug("Making OpenAI API call", "url", url, "model", request.Model)

	// Make request
	resp, err := p.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Check status code
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	// Parse response
	var openaiResp OpenAIResponse
	if err := json.Unmarshal(respBody, &openaiResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &openaiResp, nil
}

// Shutdown gracefully shuts down the provider
func (p *Provider) Shutdown(ctx context.Context) error {
	p.logger.Info("Shutting down OpenAI provider...")
	// No specific cleanup needed for HTTP client
	return nil
}
