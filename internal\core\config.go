/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package core

import (
	"fmt"
	"os"
	"path/filepath"

	"arien/internal/config"
	"arien/internal/utils"

	"github.com/charmbracelet/log"
	"github.com/spf13/viper"
	"golang.org/x/crypto/bcrypt"
)

// Config manages Arien configuration
type Config struct {
	logger     *log.Logger
	configPath string
	viper      *viper.Viper
}

// Note: Configuration types are now defined in internal/config package to avoid import cycles

// NewConfig creates a new configuration manager
func NewConfig(logger *log.Logger) (*Config, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return nil, fmt.Errorf("failed to get user home directory: %w", err)
	}

	configDir := filepath.Join(homeDir, ".arien")
	configPath := filepath.Join(configDir, "config.yaml")

	// Create config directory if it doesn't exist
	if err := os.Mkdir<PERSON>ll(configDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create config directory: %w", err)
	}

	// Initialize viper
	v := viper.New()
	v.SetConfigFile(configPath)
	v.SetConfigType("yaml")

	// Set defaults
	setDefaults(v)

	config := &Config{
		logger:     logger,
		configPath: configPath,
		viper:      v,
	}

	// Load existing config if it exists
	if err := config.load(); err != nil {
		logger.Warn("Failed to load config, using defaults", "error", err)
	}

	return config, nil
}

// setDefaults sets default configuration values
func setDefaults(v *viper.Viper) {
	// UI defaults
	v.SetDefault("ui.theme", "dark-pro")
	v.SetDefault("ui.auto_save", true)
	v.SetDefault("ui.syntax_highlighting", true)
	v.SetDefault("ui.show_line_numbers", true)
	v.SetDefault("ui.word_wrap", false)

	// Tool defaults
	v.SetDefault("tools.shell_timeout", 30)
	v.SetDefault("tools.max_file_size", "10MB")
	v.SetDefault("tools.concurrent_limit", 5)
	v.SetDefault("tools.default_editor", "")
	v.SetDefault("tools.backup_files", true)

	// Version
	v.SetDefault("version", "1.0.0")
}

// load loads configuration from file
func (c *Config) load() error {
	if _, err := os.Stat(c.configPath); os.IsNotExist(err) {
		return nil // Config file doesn't exist, use defaults
	}

	if err := c.viper.ReadInConfig(); err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	c.logger.Debug("Configuration loaded", "path", c.configPath)
	return nil
}

// Save saves the current configuration to file
func (c *Config) Save() error {
	if err := c.viper.WriteConfig(); err != nil {
		return fmt.Errorf("failed to save config: %w", err)
	}

	c.logger.Debug("Configuration saved", "path", c.configPath)
	return nil
}

// IsConfigured returns true if the configuration has been set up
func (c *Config) IsConfigured() bool {
	providers := c.viper.GetStringMap("providers")
	return len(providers) > 0
}

// GetProvider returns provider configuration
func (c *Config) GetProvider(name string) (config.ProviderConfig, bool) {
	var providerConfig config.ProviderConfig
	key := fmt.Sprintf("providers.%s", name)
	
	if !c.viper.IsSet(key) {
		return providerConfig, false
	}

	if err := c.viper.UnmarshalKey(key, &providerConfig); err != nil {
		c.logger.Error("Failed to unmarshal provider config", "provider", name, "error", err)
		return providerConfig, false
	}

	// Decrypt API key if it's encrypted
	if providerConfig.APIKey != "" {
		decrypted, err := c.decryptAPIKey(providerConfig.APIKey)
		if err != nil {
			c.logger.Error("Failed to decrypt API key", "provider", name, "error", err)
			return providerConfig, false
		}
		providerConfig.APIKey = decrypted
	}

	return providerConfig, true
}

// SetProvider sets provider configuration
func (c *Config) SetProvider(name string, providerConfig config.ProviderConfig) error {
	// Encrypt API key before storing
	if providerConfig.APIKey != "" {
		encrypted, err := c.encryptAPIKey(providerConfig.APIKey)
		if err != nil {
			return fmt.Errorf("failed to encrypt API key: %w", err)
		}
		providerConfig.APIKey = encrypted
	}

	key := fmt.Sprintf("providers.%s", name)
	c.viper.Set(key, providerConfig)

	return c.Save()
}

// GetUI returns UI configuration
func (c *Config) GetUI() config.UIConfig {
	var uiConfig config.UIConfig
	if err := c.viper.UnmarshalKey("ui", &uiConfig); err != nil {
		c.logger.Error("Failed to unmarshal UI config", "error", err)
		// Return defaults
		return config.UIConfig{
			Theme:              "dark-pro",
			AutoSave:           true,
			SyntaxHighlighting: true,
			ShowLineNumbers:    true,
			WordWrap:           false,
		}
	}
	return uiConfig
}

// SetUI sets UI configuration
func (c *Config) SetUI(uiConfig config.UIConfig) error {
	c.viper.Set("ui", uiConfig)
	return c.Save()
}

// GetTools returns tool configuration
func (c *Config) GetTools() config.ToolConfig {
	var toolConfig config.ToolConfig
	if err := c.viper.UnmarshalKey("tools", &toolConfig); err != nil {
		c.logger.Error("Failed to unmarshal tool config", "error", err)
		// Return defaults
		return config.ToolConfig{
			ShellTimeout:    30,
			MaxFileSize:     "10MB",
			ConcurrentLimit: 5,
			DefaultEditor:   "",
			BackupFiles:     true,
		}
	}
	return toolConfig
}

// SetTools sets tool configuration
func (c *Config) SetTools(toolConfig config.ToolConfig) error {
	c.viper.Set("tools", toolConfig)
	return c.Save()
}

// Show displays current configuration
func (c *Config) Show() error {
	config := c.GetArienConfig()
	
	// Mask API keys for display
	for name, provider := range config.Providers {
		if provider.APIKey != "" {
			provider.APIKey = utils.MaskString(provider.APIKey, 4)
			config.Providers[name] = provider
		}
	}

	fmt.Printf("Arien Configuration:\n")
	fmt.Printf("Version: %s\n\n", config.Version)
	
	fmt.Printf("Providers:\n")
	for name, provider := range config.Providers {
		fmt.Printf("  %s:\n", name)
		fmt.Printf("    Model: %s\n", provider.Model)
		fmt.Printf("    API Key: %s\n", provider.APIKey)
		if provider.BaseURL != "" {
			fmt.Printf("    Base URL: %s\n", provider.BaseURL)
		}
		if provider.RateLimit > 0 {
			fmt.Printf("    Rate Limit: %d\n", provider.RateLimit)
		}
		fmt.Println()
	}

	fmt.Printf("UI Settings:\n")
	fmt.Printf("  Theme: %s\n", config.UI.Theme)
	fmt.Printf("  Auto Save: %t\n", config.UI.AutoSave)
	fmt.Printf("  Syntax Highlighting: %t\n", config.UI.SyntaxHighlighting)
	fmt.Printf("  Show Line Numbers: %t\n", config.UI.ShowLineNumbers)
	fmt.Printf("  Word Wrap: %t\n\n", config.UI.WordWrap)

	fmt.Printf("Tool Settings:\n")
	fmt.Printf("  Shell Timeout: %d seconds\n", config.Tools.ShellTimeout)
	fmt.Printf("  Max File Size: %s\n", config.Tools.MaxFileSize)
	fmt.Printf("  Concurrent Limit: %d\n", config.Tools.ConcurrentLimit)
	fmt.Printf("  Default Editor: %s\n", config.Tools.DefaultEditor)
	fmt.Printf("  Backup Files: %t\n", config.Tools.BackupFiles)

	return nil
}

// ShowTheme displays current theme
func (c *Config) ShowTheme() error {
	ui := c.GetUI()
	fmt.Printf("Current theme: %s\n", ui.Theme)
	return nil
}

// SetTheme sets the UI theme
func (c *Config) SetTheme(theme string) error {
	uiConfig := c.GetUI()
	uiConfig.Theme = theme
	return c.SetUI(uiConfig)
}

// Reset resets configuration to defaults
func (c *Config) Reset() error {
	// Remove config file
	if err := os.Remove(c.configPath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to remove config file: %w", err)
	}

	// Reinitialize with defaults
	setDefaults(c.viper)
	
	c.logger.Info("Configuration reset to defaults")
	return nil
}

// GetArienConfig returns the complete configuration
func (c *Config) GetArienConfig() config.ArienConfig {
	var arienConfig config.ArienConfig
	if err := c.viper.Unmarshal(&arienConfig); err != nil {
		c.logger.Error("Failed to unmarshal config", "error", err)
	}
	return arienConfig
}

// encryptAPIKey encrypts an API key for storage
func (c *Config) encryptAPIKey(apiKey string) (string, error) {
	// Use bcrypt for simple encryption (in production, use proper encryption)
	hash, err := bcrypt.GenerateFromPassword([]byte(apiKey), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hash), nil
}

// decryptAPIKey decrypts an API key (placeholder - in production use proper decryption)
func (c *Config) decryptAPIKey(encrypted string) (string, error) {
	// This is a placeholder - in production, implement proper decryption
	// For now, we'll assume the key is stored in plain text after first setup
	return encrypted, nil
}
