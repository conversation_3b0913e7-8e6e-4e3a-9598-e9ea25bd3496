/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package core

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"time"

	"arien/internal/config"
	"arien/internal/security"
	"arien/internal/utils"

	"github.com/charmbracelet/log"
	"github.com/spf13/viper"
)

// Config manages Arien configuration
type Config struct {
	logger       *log.Logger
	configPath   string
	viper        *viper.Viper
	encryptionKey []byte
}

// Note: Configuration types are now defined in internal/config package to avoid import cycles

// NewConfig creates a new configuration manager
func NewConfig(logger *log.Logger) (*Config, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return nil, fmt.Errorf("failed to get user home directory: %w", err)
	}

	configDir := filepath.Join(homeDir, ".arien")
	configPath := filepath.Join(configDir, "config.yaml")

	// Create config directory if it doesn't exist
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create config directory: %w", err)
	}

	// Initialize viper
	v := viper.New()
	v.SetConfigFile(configPath)
	v.SetConfigType("yaml")

	// Set defaults
	setDefaults(v)

	config := &Config{
		logger:       logger,
		configPath:   configPath,
		viper:        v,
		encryptionKey: generateEncryptionKey(),
	}

	// Load existing config if it exists
	if err := config.load(); err != nil {
		logger.Warn("Failed to load config, using defaults", "error", err)
	}

	return config, nil
}

// setDefaults sets default configuration values
func setDefaults(v *viper.Viper) {
	// UI defaults
	v.SetDefault("ui.theme", "dark-pro")
	v.SetDefault("ui.auto_save", true)
	v.SetDefault("ui.syntax_highlighting", true)
	v.SetDefault("ui.show_line_numbers", true)
	v.SetDefault("ui.word_wrap", false)

	// Tool defaults
	v.SetDefault("tools.shell_timeout", 30)
	v.SetDefault("tools.max_file_size", "10MB")
	v.SetDefault("tools.concurrent_limit", 5)
	v.SetDefault("tools.default_editor", "")
	v.SetDefault("tools.backup_files", true)

	// Version
	v.SetDefault("version", "1.0.0")
}

// load loads configuration from file
func (c *Config) load() error {
	if _, err := os.Stat(c.configPath); os.IsNotExist(err) {
		return nil // Config file doesn't exist, use defaults
	}

	if err := c.viper.ReadInConfig(); err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	c.logger.Debug("Configuration loaded", "path", c.configPath)
	return nil
}

// Save saves the current configuration to file
func (c *Config) Save() error {
	if err := c.viper.WriteConfig(); err != nil {
		return fmt.Errorf("failed to save config: %w", err)
	}

	c.logger.Debug("Configuration saved", "path", c.configPath)
	return nil
}

// IsConfigured returns true if the configuration has been set up
func (c *Config) IsConfigured() bool {
	providers := c.viper.GetStringMap("providers")
	return len(providers) > 0
}

// GetProvider returns provider configuration
func (c *Config) GetProvider(name string) (config.ProviderConfig, bool) {
	var providerConfig config.ProviderConfig
	key := fmt.Sprintf("providers.%s", name)
	
	if !c.viper.IsSet(key) {
		return providerConfig, false
	}

	if err := c.viper.UnmarshalKey(key, &providerConfig); err != nil {
		c.logger.Error("Failed to unmarshal provider config", "provider", name, "error", err)
		return providerConfig, false
	}

	// Decrypt API key if it's encrypted
	if providerConfig.APIKey != "" {
		decrypted, err := c.decryptAPIKey(providerConfig.APIKey)
		if err != nil {
			c.logger.Error("Failed to decrypt API key", "provider", name, "error", err)
			return providerConfig, false
		}
		providerConfig.APIKey = decrypted
	}

	return providerConfig, true
}

// SetProvider sets provider configuration
func (c *Config) SetProvider(name string, providerConfig config.ProviderConfig) error {
	// Encrypt API key before storing
	if providerConfig.APIKey != "" {
		encrypted, err := c.encryptAPIKey(providerConfig.APIKey)
		if err != nil {
			return fmt.Errorf("failed to encrypt API key: %w", err)
		}
		providerConfig.APIKey = encrypted
	}

	key := fmt.Sprintf("providers.%s", name)
	c.viper.Set(key, providerConfig)

	return c.Save()
}

// UpdateProviderAPIKey updates only the API key for a specific provider
func (c *Config) UpdateProviderAPIKey(name, newAPIKey string) error {
	// Get existing provider config
	existingConfig, exists := c.GetProvider(name)
	if !exists {
		return fmt.Errorf("provider %s not found", name)
	}

	// Update only the API key
	existingConfig.APIKey = newAPIKey

	// Save the updated config
	return c.SetProvider(name, existingConfig)
}

// RotateProviderAPIKey rotates the API key for a provider with validation
func (c *Config) RotateProviderAPIKey(name, newAPIKey string) error {
	// Validate the new API key format first
	validator := security.NewValidator()
	if err := validator.ValidateAPIKey(name, newAPIKey); err != nil {
		return NewAPIKeyError(name, fmt.Sprintf("API key validation failed: %v", err))
	}

	// Test the new API key
	ctx := context.Background()
	if err := validator.TestAPIKey(ctx, name, newAPIKey, ""); err != nil {
		return NewAPIKeyError(name, fmt.Sprintf("API key test failed: %v", err))
	}

	// Backup current config before rotation
	if err := c.backupConfig(); err != nil {
		c.logger.Warn("Failed to backup config before key rotation", "error", err)
	}

	// Update the API key
	if err := c.UpdateProviderAPIKey(name, newAPIKey); err != nil {
		return NewConfigurationError("provider_update", fmt.Sprintf("failed to update API key: %v", err))
	}

	c.logger.Info("API key rotated successfully", "provider", name)
	return nil
}

// ListProviders returns a list of configured providers with masked API keys
func (c *Config) ListProviders() map[string]config.ProviderConfig {
	providers := make(map[string]config.ProviderConfig)

	for _, providerName := range []string{"deepseek", "openai", "anthropic", "gemini"} {
		if providerConfig, exists := c.GetProvider(providerName); exists {
			// Mask the API key for display
			maskedConfig := providerConfig
			maskedConfig.APIKey = utils.MaskString(providerConfig.APIKey, 4)
			providers[providerName] = maskedConfig
		}
	}

	return providers
}

// RemoveProvider removes a provider configuration
func (c *Config) RemoveProvider(name string) error {
	key := fmt.Sprintf("providers.%s", name)

	// Check if provider exists
	if !c.viper.IsSet(key) {
		return fmt.Errorf("provider %s not found", name)
	}

	// Remove the provider
	providers := c.viper.GetStringMap("providers")
	delete(providers, name)
	c.viper.Set("providers", providers)

	c.logger.Info("Provider removed", "provider", name)
	return c.Save()
}

// GetUI returns UI configuration
func (c *Config) GetUI() config.UIConfig {
	var uiConfig config.UIConfig
	if err := c.viper.UnmarshalKey("ui", &uiConfig); err != nil {
		c.logger.Error("Failed to unmarshal UI config", "error", err)
		// Return defaults
		return config.UIConfig{
			Theme:              "dark-pro",
			AutoSave:           true,
			SyntaxHighlighting: true,
			ShowLineNumbers:    true,
			WordWrap:           false,
		}
	}
	return uiConfig
}

// SetUI sets UI configuration
func (c *Config) SetUI(uiConfig config.UIConfig) error {
	c.viper.Set("ui", uiConfig)
	return c.Save()
}

// GetTools returns tool configuration
func (c *Config) GetTools() config.ToolConfig {
	var toolConfig config.ToolConfig
	if err := c.viper.UnmarshalKey("tools", &toolConfig); err != nil {
		c.logger.Error("Failed to unmarshal tool config", "error", err)
		// Return defaults
		return config.ToolConfig{
			ShellTimeout:    30,
			MaxFileSize:     "10MB",
			ConcurrentLimit: 5,
			DefaultEditor:   "",
			BackupFiles:     true,
		}
	}
	return toolConfig
}

// SetTools sets tool configuration
func (c *Config) SetTools(toolConfig config.ToolConfig) error {
	c.viper.Set("tools", toolConfig)
	return c.Save()
}

// Show displays current configuration
func (c *Config) Show() error {
	config := c.GetArienConfig()
	
	// Mask API keys for display
	for name, provider := range config.Providers {
		if provider.APIKey != "" {
			provider.APIKey = utils.MaskString(provider.APIKey, 4)
			config.Providers[name] = provider
		}
	}

	fmt.Printf("Arien Configuration:\n")
	fmt.Printf("Version: %s\n\n", config.Version)
	
	fmt.Printf("Providers:\n")
	for name, provider := range config.Providers {
		fmt.Printf("  %s:\n", name)
		fmt.Printf("    Model: %s\n", provider.Model)
		fmt.Printf("    API Key: %s\n", provider.APIKey)
		if provider.BaseURL != "" {
			fmt.Printf("    Base URL: %s\n", provider.BaseURL)
		}
		if provider.RateLimit > 0 {
			fmt.Printf("    Rate Limit: %d\n", provider.RateLimit)
		}
		fmt.Println()
	}

	fmt.Printf("UI Settings:\n")
	fmt.Printf("  Theme: %s\n", config.UI.Theme)
	fmt.Printf("  Auto Save: %t\n", config.UI.AutoSave)
	fmt.Printf("  Syntax Highlighting: %t\n", config.UI.SyntaxHighlighting)
	fmt.Printf("  Show Line Numbers: %t\n", config.UI.ShowLineNumbers)
	fmt.Printf("  Word Wrap: %t\n\n", config.UI.WordWrap)

	fmt.Printf("Tool Settings:\n")
	fmt.Printf("  Shell Timeout: %d seconds\n", config.Tools.ShellTimeout)
	fmt.Printf("  Max File Size: %s\n", config.Tools.MaxFileSize)
	fmt.Printf("  Concurrent Limit: %d\n", config.Tools.ConcurrentLimit)
	fmt.Printf("  Default Editor: %s\n", config.Tools.DefaultEditor)
	fmt.Printf("  Backup Files: %t\n", config.Tools.BackupFiles)

	return nil
}

// ShowTheme displays current theme
func (c *Config) ShowTheme() error {
	ui := c.GetUI()
	fmt.Printf("Current theme: %s\n", ui.Theme)
	return nil
}

// SetTheme sets the UI theme
func (c *Config) SetTheme(theme string) error {
	uiConfig := c.GetUI()
	uiConfig.Theme = theme
	return c.SetUI(uiConfig)
}

// Reset resets configuration to defaults
func (c *Config) Reset() error {
	// Remove config file
	if err := os.Remove(c.configPath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to remove config file: %w", err)
	}

	// Reinitialize with defaults
	setDefaults(c.viper)
	
	c.logger.Info("Configuration reset to defaults")
	return nil
}

// GetArienConfig returns the complete configuration
func (c *Config) GetArienConfig() config.ArienConfig {
	var arienConfig config.ArienConfig
	if err := c.viper.Unmarshal(&arienConfig); err != nil {
		c.logger.Error("Failed to unmarshal config", "error", err)
	}
	return arienConfig
}

// generateEncryptionKey generates a deterministic encryption key based on system info
func generateEncryptionKey() []byte {
	// Use a combination of hostname and user home directory for deterministic key
	hostname, _ := os.Hostname()
	homeDir, _ := os.UserHomeDir()

	// Create a deterministic key from system information
	keyData := fmt.Sprintf("arien-config-%s-%s", hostname, homeDir)
	hash := sha256.Sum256([]byte(keyData))
	return hash[:]
}

// encryptAPIKey encrypts an API key using AES-GCM
func (c *Config) encryptAPIKey(apiKey string) (string, error) {
	if apiKey == "" {
		return "", nil
	}

	// Create AES cipher
	block, err := aes.NewCipher(c.encryptionKey)
	if err != nil {
		return "", NewEncryptionError("cipher_creation", fmt.Sprintf("failed to create cipher: %v", err))
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", NewEncryptionError("gcm_creation", fmt.Sprintf("failed to create GCM: %v", err))
	}

	// Generate random nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", NewEncryptionError("nonce_generation", fmt.Sprintf("failed to generate nonce: %v", err))
	}

	// Encrypt the API key
	ciphertext := gcm.Seal(nonce, nonce, []byte(apiKey), nil)

	// Encode to base64 for storage
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// decryptAPIKey decrypts an API key using AES-GCM
func (c *Config) decryptAPIKey(encryptedKey string) (string, error) {
	if encryptedKey == "" {
		return "", nil
	}

	// Check if the key looks like it's already plain text (for backward compatibility)
	if !c.isEncryptedKey(encryptedKey) {
		c.logger.Debug("API key appears to be plain text, migrating to encrypted storage")
		return encryptedKey, nil
	}

	// Decode from base64
	ciphertext, err := base64.StdEncoding.DecodeString(encryptedKey)
	if err != nil {
		return "", fmt.Errorf("failed to decode encrypted key: %w", err)
	}

	// Create AES cipher
	block, err := aes.NewCipher(c.encryptionKey)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher: %w", err)
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("failed to create GCM: %w", err)
	}

	// Extract nonce and ciphertext
	nonceSize := gcm.NonceSize()
	if len(ciphertext) < nonceSize {
		return "", fmt.Errorf("ciphertext too short")
	}

	nonce, ciphertext := ciphertext[:nonceSize], ciphertext[nonceSize:]

	// Decrypt
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt API key: %w", err)
	}

	return string(plaintext), nil
}

// isEncryptedKey checks if a key appears to be encrypted (base64 encoded)
func (c *Config) isEncryptedKey(key string) bool {
	// Check if it's valid base64 and has reasonable length for encrypted data
	if _, err := base64.StdEncoding.DecodeString(key); err != nil {
		return false
	}
	// Encrypted keys should be longer than typical API keys due to nonce + encryption
	return len(key) > 50
}

// backupConfig creates a backup of the current configuration
func (c *Config) backupConfig() error {
	backupPath := c.configPath + ".backup." + time.Now().Format("20060102-150405")

	// Read current config file
	configData, err := os.ReadFile(c.configPath)
	if err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	// Write backup
	if err := os.WriteFile(backupPath, configData, 0600); err != nil {
		return fmt.Errorf("failed to write backup: %w", err)
	}

	c.logger.Debug("Config backup created", "path", backupPath)
	return nil
}
