/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package chat

import (
	"fmt"
	"sync"
	"time"
)

// EventHandlerImpl implements the EventHandler interface
type EventHandlerImpl struct {
	deps      *Dependencies
	listeners map[string][]EventListener
	mu        sync.RWMutex
}

// EventListener represents a function that handles events
type EventListener func(event *Event)

// Event represents a chat event
type Event struct {
	Type      string                 `json:"type"`
	Timestamp time.Time              `json:"timestamp"`
	Data      map[string]interface{} `json:"data"`
	Source    string                 `json:"source"`
}

// Event types
const (
	EventMessageSent      = "message_sent"
	EventMessageReceived  = "message_received"
	EventError           = "error"
	EventSessionChanged  = "session_changed"
	EventStreamingStarted = "streaming_started"
	EventStreamingChunk  = "streaming_chunk"
	EventStreamingCompleted = "streaming_completed"
	EventAttachmentAdded = "attachment_added"
	EventAttachmentRemoved = "attachment_removed"
	EventConfigChanged   = "config_changed"
	EventProviderChanged = "provider_changed"
	EventModelChanged    = "model_changed"
)

// NewEventHandler creates a new event handler
func NewEventHandler(deps *Dependencies) EventHandler {
	return &EventHandlerImpl{
		deps:      deps,
		listeners: make(map[string][]EventListener),
	}
}

// OnMessageSent handles message sent events
func (eh *EventHandlerImpl) OnMessageSent(message *Message) {
	eh.deps.Logger.Debug("Message sent event", "id", message.ID, "role", message.Role)
	
	event := &Event{
		Type:      EventMessageSent,
		Timestamp: time.Now(),
		Source:    "chat",
		Data: map[string]interface{}{
			"message_id": message.ID,
			"role":       message.Role,
			"content":    message.Content,
			"timestamp":  message.Timestamp,
		},
	}
	
	eh.emitEvent(event)
	
	// Update metrics
	eh.updateMessageMetrics(message, "sent")
}

// OnMessageReceived handles message received events
func (eh *EventHandlerImpl) OnMessageReceived(message *Message) {
	eh.deps.Logger.Debug("Message received event", "id", message.ID, "role", message.Role)
	
	event := &Event{
		Type:      EventMessageReceived,
		Timestamp: time.Now(),
		Source:    "chat",
		Data: map[string]interface{}{
			"message_id": message.ID,
			"role":       message.Role,
			"content":    message.Content,
			"timestamp":  message.Timestamp,
			"status":     message.Status,
		},
	}
	
	// Add tool results if present
	if len(message.ToolResults) > 0 {
		event.Data["tool_results_count"] = len(message.ToolResults)
	}
	
	// Add token usage if available
	if tokenCount, ok := message.Metadata["tokens_used"]; ok {
		event.Data["tokens_used"] = tokenCount
	}
	
	eh.emitEvent(event)
	
	// Update metrics
	eh.updateMessageMetrics(message, "received")
}

// OnError handles error events
func (eh *EventHandlerImpl) OnError(err error) {
	eh.deps.Logger.Error("Error event", "error", err)
	
	event := &Event{
		Type:      EventError,
		Timestamp: time.Now(),
		Source:    "chat",
		Data: map[string]interface{}{
			"error":   err.Error(),
			"type":    fmt.Sprintf("%T", err),
		},
	}
	
	eh.emitEvent(event)
	
	// Update error metrics
	eh.updateErrorMetrics(err)
}

// OnSessionChanged handles session change events
func (eh *EventHandlerImpl) OnSessionChanged(session *ChatSession) {
	eh.deps.Logger.Debug("Session changed event", "id", session.ID, "name", session.Name)
	
	event := &Event{
		Type:      EventSessionChanged,
		Timestamp: time.Now(),
		Source:    "chat",
		Data: map[string]interface{}{
			"session_id":     session.ID,
			"session_name":   session.Name,
			"message_count":  len(session.Messages),
			"provider":       session.Provider,
			"model":          session.Model,
			"created":        session.Created,
			"updated":        session.Updated,
		},
	}
	
	eh.emitEvent(event)
}

// OnStreamingStarted handles streaming start events
func (eh *EventHandlerImpl) OnStreamingStarted() {
	eh.deps.Logger.Debug("Streaming started event")
	
	event := &Event{
		Type:      EventStreamingStarted,
		Timestamp: time.Now(),
		Source:    "chat",
		Data:      map[string]interface{}{},
	}
	
	eh.emitEvent(event)
}

// OnStreamingChunk handles streaming chunk events
func (eh *EventHandlerImpl) OnStreamingChunk(chunk string) {
	eh.deps.Logger.Debug("Streaming chunk event", "chunk_length", len(chunk))
	
	event := &Event{
		Type:      EventStreamingChunk,
		Timestamp: time.Now(),
		Source:    "chat",
		Data: map[string]interface{}{
			"chunk":        chunk,
			"chunk_length": len(chunk),
		},
	}
	
	eh.emitEvent(event)
}

// OnStreamingCompleted handles streaming completion events
func (eh *EventHandlerImpl) OnStreamingCompleted() {
	eh.deps.Logger.Debug("Streaming completed event")
	
	event := &Event{
		Type:      EventStreamingCompleted,
		Timestamp: time.Now(),
		Source:    "chat",
		Data:      map[string]interface{}{},
	}
	
	eh.emitEvent(event)
}

// Event listener management

// AddListener adds an event listener for a specific event type
func (eh *EventHandlerImpl) AddListener(eventType string, listener EventListener) {
	eh.mu.Lock()
	defer eh.mu.Unlock()
	
	if eh.listeners[eventType] == nil {
		eh.listeners[eventType] = make([]EventListener, 0)
	}
	
	eh.listeners[eventType] = append(eh.listeners[eventType], listener)
	eh.deps.Logger.Debug("Event listener added", "event_type", eventType)
}

// RemoveListener removes an event listener (note: this is a simplified implementation)
func (eh *EventHandlerImpl) RemoveListener(eventType string) {
	eh.mu.Lock()
	defer eh.mu.Unlock()
	
	delete(eh.listeners, eventType)
	eh.deps.Logger.Debug("Event listeners removed", "event_type", eventType)
}

// emitEvent emits an event to all registered listeners
func (eh *EventHandlerImpl) emitEvent(event *Event) {
	eh.mu.RLock()
	listeners := eh.listeners[event.Type]
	eh.mu.RUnlock()
	
	// Call listeners in separate goroutines to avoid blocking
	for _, listener := range listeners {
		go func(l EventListener) {
			defer func() {
				if r := recover(); r != nil {
					eh.deps.Logger.Error("Event listener panic", "event_type", event.Type, "panic", r)
				}
			}()
			l(event)
		}(listener)
	}
}

// Metrics and analytics

// updateMessageMetrics updates message-related metrics
func (eh *EventHandlerImpl) updateMessageMetrics(message *Message, direction string) {
	// This would integrate with the metrics system
	// For now, we'll just log the metrics
	
	metrics := map[string]interface{}{
		"direction":      direction,
		"role":           message.Role,
		"content_length": len(message.Content),
		"timestamp":      message.Timestamp,
	}
	
	if len(message.ToolResults) > 0 {
		metrics["tool_results_count"] = len(message.ToolResults)
	}
	
	if tokenCount, ok := message.Metadata["tokens_used"]; ok {
		metrics["tokens_used"] = tokenCount
	}
	
	eh.deps.Logger.Debug("Message metrics", "metrics", metrics)
}

// updateErrorMetrics updates error-related metrics
func (eh *EventHandlerImpl) updateErrorMetrics(err error) {
	metrics := map[string]interface{}{
		"error_type":    fmt.Sprintf("%T", err),
		"error_message": err.Error(),
		"timestamp":     time.Now(),
	}
	
	eh.deps.Logger.Debug("Error metrics", "metrics", metrics)
}

// Event history and replay

// EventHistory stores recent events for debugging and replay
type EventHistory struct {
	events    []*Event
	maxEvents int
	mu        sync.RWMutex
}

// NewEventHistory creates a new event history
func NewEventHistory(maxEvents int) *EventHistory {
	return &EventHistory{
		events:    make([]*Event, 0),
		maxEvents: maxEvents,
	}
}

// AddEvent adds an event to the history
func (eh *EventHistory) AddEvent(event *Event) {
	eh.mu.Lock()
	defer eh.mu.Unlock()
	
	eh.events = append(eh.events, event)
	
	// Trim to max size
	if len(eh.events) > eh.maxEvents {
		eh.events = eh.events[len(eh.events)-eh.maxEvents:]
	}
}

// GetEvents returns all events in the history
func (eh *EventHistory) GetEvents() []*Event {
	eh.mu.RLock()
	defer eh.mu.RUnlock()
	
	// Return a copy to avoid race conditions
	events := make([]*Event, len(eh.events))
	copy(events, eh.events)
	return events
}

// GetEventsByType returns events of a specific type
func (eh *EventHistory) GetEventsByType(eventType string) []*Event {
	eh.mu.RLock()
	defer eh.mu.RUnlock()
	
	var filtered []*Event
	for _, event := range eh.events {
		if event.Type == eventType {
			filtered = append(filtered, event)
		}
	}
	return filtered
}

// GetEventsAfter returns events after a specific timestamp
func (eh *EventHistory) GetEventsAfter(timestamp time.Time) []*Event {
	eh.mu.RLock()
	defer eh.mu.RUnlock()
	
	var filtered []*Event
	for _, event := range eh.events {
		if event.Timestamp.After(timestamp) {
			filtered = append(filtered, event)
		}
	}
	return filtered
}

// Clear clears all events from the history
func (eh *EventHistory) Clear() {
	eh.mu.Lock()
	defer eh.mu.Unlock()
	
	eh.events = eh.events[:0]
}

// Event analytics

// GetEventStats returns statistics about events
func (eh *EventHistory) GetEventStats() map[string]interface{} {
	eh.mu.RLock()
	defer eh.mu.RUnlock()
	
	stats := map[string]interface{}{
		"total_events": len(eh.events),
		"event_types":  make(map[string]int),
	}
	
	eventTypes := stats["event_types"].(map[string]int)
	
	for _, event := range eh.events {
		eventTypes[event.Type]++
	}
	
	// Calculate time range
	if len(eh.events) > 0 {
		stats["first_event"] = eh.events[0].Timestamp
		stats["last_event"] = eh.events[len(eh.events)-1].Timestamp
		stats["time_span"] = eh.events[len(eh.events)-1].Timestamp.Sub(eh.events[0].Timestamp)
	}
	
	return stats
}

// Event filtering and search

// EventFilter represents a filter for events
type EventFilter struct {
	Type      string
	Source    string
	After     *time.Time
	Before    *time.Time
	DataMatch map[string]interface{}
}

// FilterEvents filters events based on criteria
func (eh *EventHistory) FilterEvents(filter *EventFilter) []*Event {
	eh.mu.RLock()
	defer eh.mu.RUnlock()
	
	var filtered []*Event
	
	for _, event := range eh.events {
		if eh.matchesFilter(event, filter) {
			filtered = append(filtered, event)
		}
	}
	
	return filtered
}

// matchesFilter checks if an event matches the filter criteria
func (eh *EventHistory) matchesFilter(event *Event, filter *EventFilter) bool {
	// Check type
	if filter.Type != "" && event.Type != filter.Type {
		return false
	}
	
	// Check source
	if filter.Source != "" && event.Source != filter.Source {
		return false
	}
	
	// Check time range
	if filter.After != nil && event.Timestamp.Before(*filter.After) {
		return false
	}
	
	if filter.Before != nil && event.Timestamp.After(*filter.Before) {
		return false
	}
	
	// Check data matches
	if filter.DataMatch != nil {
		for key, expectedValue := range filter.DataMatch {
			if actualValue, exists := event.Data[key]; !exists || actualValue != expectedValue {
				return false
			}
		}
	}
	
	return true
}
