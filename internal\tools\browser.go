/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/playwright-community/playwright-go"
)

// BrowserTool implements browser automation with Playwright integration
type BrowserTool struct {
	playwright *playwright.Playwright
	browser    playwright.Browser
	page       playwright.Page
	timeout    time.Duration
}

// NewBrowserTool creates a new browser tool
func NewBrowserTool() *BrowserTool {
	return &BrowserTool{
		timeout: 30 * time.Second,
	}
}

// Name returns the tool name
func (t *BrowserTool) Name() string {
	return "browser"
}

// Description returns the tool description
func (t *BrowserTool) Description() string {
	return "Browser automation with Playwright integration for web interactions"
}

// Parameters returns the tool parameter schema
func (t *BrowserTool) Parameters() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"action": map[string]interface{}{
				"type":        "string",
				"description": "Browser action to perform",
				"enum":        []string{"navigate", "click", "type", "screenshot", "get_content", "wait", "close", "evaluate"},
			},
			"url": StringParameter("URL to navigate to (for navigate action)", false),
			"selector": StringParameter("CSS selector for element (for click, type actions)", false),
			"text": StringParameter("Text to type (for type action)", false),
			"script": StringParameter("JavaScript to evaluate (for evaluate action)", false),
			"wait_for": StringParameter("Element selector or timeout to wait for", false),
			"timeout": IntParameter("Timeout in seconds", 1, 120),
			"headless": BoolParameter("Run browser in headless mode", true),
			"browser_type": map[string]interface{}{
				"type":        "string",
				"description": "Browser type to use",
				"enum":        []string{"chromium", "firefox", "webkit"},
				"default":     "chromium",
			},
			"viewport": map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"width":  IntParameter("Viewport width", 100, 3840),
					"height": IntParameter("Viewport height", 100, 2160),
				},
			},
			"screenshot_path": StringParameter("Path to save screenshot (for screenshot action)", false),
			"full_page": BoolParameter("Take full page screenshot", false),
		},
		"required": []string{"action"},
	}
}

// BrowserResult represents the result of a browser operation
type BrowserResult struct {
	Action      string        `json:"action"`
	URL         string        `json:"url,omitempty"`
	Success     bool          `json:"success"`
	Content     string        `json:"content,omitempty"`
	Screenshot  string        `json:"screenshot,omitempty"`
	Error       string        `json:"error,omitempty"`
	Duration    time.Duration `json:"duration"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// Execute performs the browser automation operation
func (t *BrowserTool) Execute(ctx context.Context, args map[string]interface{}) Result {
	start := time.Now()
	
	// Parse arguments
	action := getStringArg(args, "action", "")
	url := getStringArg(args, "url", "")
	selector := getStringArg(args, "selector", "")
	text := getStringArg(args, "text", "")
	script := getStringArg(args, "script", "")
	waitFor := getStringArg(args, "wait_for", "")
	timeout := getIntArg(args, "timeout", 30)
	headless := getBoolArg(args, "headless", true)
	browserType := getStringArg(args, "browser_type", "chromium")
	screenshotPath := getStringArg(args, "screenshot_path", "")
	fullPage := getBoolArg(args, "full_page", false)
	
	t.timeout = time.Duration(timeout) * time.Second
	
	// Initialize browser if needed
	if t.playwright == nil {
		if err := t.initializeBrowser(browserType, headless, args); err != nil {
			return Result{Error: fmt.Errorf("failed to initialize browser: %w", err)}
		}
	}
	
	// Perform the requested action
	result, err := t.performAction(ctx, action, url, selector, text, script, waitFor, screenshotPath, fullPage)
	if err != nil {
		return Result{Error: err}
	}
	
	result.Duration = time.Since(start)
	
	// Format output
	output := t.formatBrowserResult(result)
	
	return Result{
		Output: output,
		Data:   result,
	}
}

// initializeBrowser initializes the Playwright browser
func (t *BrowserTool) initializeBrowser(browserType string, headless bool, args map[string]interface{}) error {
	// Install Playwright if needed
	err := playwright.Install()
	if err != nil {
		return fmt.Errorf("failed to install Playwright: %w", err)
	}
	
	// Start Playwright
	pw, err := playwright.Run()
	if err != nil {
		return fmt.Errorf("failed to start Playwright: %w", err)
	}
	t.playwright = pw
	
	// Launch browser
	var browser playwright.Browser
	launchOptions := playwright.BrowserTypeLaunchOptions{
		Headless: &headless,
	}
	
	switch browserType {
	case "chromium":
		browser, err = pw.Chromium.Launch(launchOptions)
	case "firefox":
		browser, err = pw.Firefox.Launch(launchOptions)
	case "webkit":
		browser, err = pw.WebKit.Launch(launchOptions)
	default:
		return fmt.Errorf("unsupported browser type: %s", browserType)
	}
	
	if err != nil {
		return fmt.Errorf("failed to launch browser: %w", err)
	}
	t.browser = browser
	
	// Create new page
	page, err := browser.NewPage()
	if err != nil {
		return fmt.Errorf("failed to create new page: %w", err)
	}
	t.page = page
	
	// Set viewport if specified
	if viewport, ok := args["viewport"].(map[string]interface{}); ok {
		width := getIntArg(viewport, "width", 1280)
		height := getIntArg(viewport, "height", 720)
		
		err = page.SetViewportSize(width, height)
		if err != nil {
			return fmt.Errorf("failed to set viewport: %w", err)
		}
	}
	
	return nil
}

// performAction performs the specified browser action
func (t *BrowserTool) performAction(ctx context.Context, action, url, selector, text, script, waitFor, screenshotPath string, fullPage bool) (*BrowserResult, error) {
	result := &BrowserResult{
		Action:  action,
		Success: false,
	}
	
	switch action {
	case "navigate":
		if url == "" {
			return nil, fmt.Errorf("url is required for navigate action")
		}
		
		timeoutMs := float64(t.timeout.Milliseconds())
		_, err := t.page.Goto(url, playwright.PageGotoOptions{
			Timeout: &timeoutMs,
		})
		if err != nil {
			result.Error = err.Error()
			return result, nil
		}
		
		result.URL = url
		result.Success = true
		
	case "click":
		if selector == "" {
			return nil, fmt.Errorf("selector is required for click action")
		}
		
		timeoutMs := float64(t.timeout.Milliseconds())
		err := t.page.Click(selector, playwright.PageClickOptions{
			Timeout: &timeoutMs,
		})
		if err != nil {
			result.Error = err.Error()
			return result, nil
		}
		
		result.Success = true
		
	case "type":
		if selector == "" || text == "" {
			return nil, fmt.Errorf("selector and text are required for type action")
		}
		
		timeoutMs := float64(t.timeout.Milliseconds())
		err := t.page.Fill(selector, text, playwright.PageFillOptions{
			Timeout: &timeoutMs,
		})
		if err != nil {
			result.Error = err.Error()
			return result, nil
		}
		
		result.Success = true
		
	case "screenshot":
		options := playwright.PageScreenshotOptions{
			FullPage: &fullPage,
		}
		
		if screenshotPath != "" {
			options.Path = &screenshotPath
		}
		
		screenshot, err := t.page.Screenshot(options)
		if err != nil {
			result.Error = err.Error()
			return result, nil
		}
		
		if screenshotPath != "" {
			result.Screenshot = screenshotPath
		} else {
			// Return base64 encoded screenshot
			result.Screenshot = fmt.Sprintf("data:image/png;base64,%s", screenshot)
		}
		result.Success = true
		
	case "get_content":
		content, err := t.page.Content()
		if err != nil {
			result.Error = err.Error()
			return result, nil
		}
		
		result.Content = content
		result.Success = true
		
	case "wait":
		if waitFor == "" {
			return nil, fmt.Errorf("wait_for is required for wait action")
		}
		
		// Try to parse as timeout first
		if duration, err := time.ParseDuration(waitFor); err == nil {
			t.page.WaitForTimeout(float64(duration.Milliseconds()))
			result.Success = true
		} else {
			// Treat as selector
			timeoutMs := float64(t.timeout.Milliseconds())
			_, err := t.page.WaitForSelector(waitFor, playwright.PageWaitForSelectorOptions{
				Timeout: &timeoutMs,
			})
			if err != nil {
				result.Error = err.Error()
				return result, nil
			}
			result.Success = true
		}
		
	case "evaluate":
		if script == "" {
			return nil, fmt.Errorf("script is required for evaluate action")
		}
		
		evalResult, err := t.page.Evaluate(script)
		if err != nil {
			result.Error = err.Error()
			return result, nil
		}
		
		result.Content = fmt.Sprintf("%v", evalResult)
		result.Success = true
		
	case "close":
		if t.page != nil {
			err := t.page.Close()
			if err != nil {
				result.Error = err.Error()
				return result, nil
			}
		}
		
		if t.browser != nil {
			err := t.browser.Close()
			if err != nil {
				result.Error = err.Error()
				return result, nil
			}
		}
		
		if t.playwright != nil {
			err := t.playwright.Stop()
			if err != nil {
				result.Error = err.Error()
				return result, nil
			}
		}
		
		// Reset instances
		t.page = nil
		t.browser = nil
		t.playwright = nil
		
		result.Success = true
		
	default:
		return nil, fmt.Errorf("unsupported action: %s", action)
	}
	
	// Add current URL to metadata if page is available
	if t.page != nil && result.Success {
		if currentURL := t.page.URL(); currentURL != "" {
			result.URL = currentURL
		}
		
		// Add page title
		if title, err := t.page.Title(); err == nil {
			if result.Metadata == nil {
				result.Metadata = make(map[string]interface{})
			}
			result.Metadata["title"] = title
		}
	}
	
	return result, nil
}

// formatBrowserResult formats the browser result for display
func (t *BrowserTool) formatBrowserResult(result *BrowserResult) string {
	var output strings.Builder
	
	output.WriteString(fmt.Sprintf("🌐 Browser Automation Result\n"))
	output.WriteString(fmt.Sprintf("Action: %s | Success: %t | Duration: %v\n", 
		result.Action, result.Success, result.Duration))
	
	if result.URL != "" {
		output.WriteString(fmt.Sprintf("URL: %s\n", result.URL))
	}
	
	if result.Error != "" {
		output.WriteString(fmt.Sprintf("❌ Error: %s\n", result.Error))
	}
	
	if result.Success {
		output.WriteString("✅ Action completed successfully\n")
		
		if result.Content != "" {
			output.WriteString(fmt.Sprintf("\n📄 Content:\n%s\n", result.Content))
		}
		
		if result.Screenshot != "" {
			output.WriteString(fmt.Sprintf("\n📸 Screenshot: %s\n", result.Screenshot))
		}
		
		if result.Metadata != nil {
			output.WriteString("\n📊 Metadata:\n")
			for key, value := range result.Metadata {
				output.WriteString(fmt.Sprintf("  %s: %v\n", key, value))
			}
		}
	}
	
	return output.String()
}

// Validate validates the tool arguments
func (t *BrowserTool) Validate(args map[string]interface{}) error {
	action := getStringArg(args, "action", "")
	if action == "" {
		return fmt.Errorf("action is required")
	}
	
	validActions := []string{"navigate", "click", "type", "screenshot", "get_content", "wait", "close", "evaluate"}
	valid := false
	for _, a := range validActions {
		if action == a {
			valid = true
			break
		}
	}
	if !valid {
		return fmt.Errorf("action must be one of: %s", strings.Join(validActions, ", "))
	}
	
	// Validate action-specific requirements
	switch action {
	case "navigate":
		if getStringArg(args, "url", "") == "" {
			return fmt.Errorf("url is required for navigate action")
		}
	case "click":
		if getStringArg(args, "selector", "") == "" {
			return fmt.Errorf("selector is required for click action")
		}
	case "type":
		if getStringArg(args, "selector", "") == "" {
			return fmt.Errorf("selector is required for type action")
		}
		if getStringArg(args, "text", "") == "" {
			return fmt.Errorf("text is required for type action")
		}
	case "wait":
		if getStringArg(args, "wait_for", "") == "" {
			return fmt.Errorf("wait_for is required for wait action")
		}
	case "evaluate":
		if getStringArg(args, "script", "") == "" {
			return fmt.Errorf("script is required for evaluate action")
		}
	}
	
	// Validate browser type
	browserType := getStringArg(args, "browser_type", "chromium")
	validBrowsers := []string{"chromium", "firefox", "webkit"}
	valid = false
	for _, b := range validBrowsers {
		if browserType == b {
			valid = true
			break
		}
	}
	if !valid {
		return fmt.Errorf("browser_type must be one of: %s", strings.Join(validBrowsers, ", "))
	}
	
	// Validate timeout
	timeout := getIntArg(args, "timeout", 30)
	if timeout < 1 || timeout > 120 {
		return fmt.Errorf("timeout must be between 1 and 120 seconds")
	}
	
	return nil
}

// SupportsParallel returns whether this tool supports parallel execution
func (t *BrowserTool) SupportsParallel() bool {
	return false // Browser automation should be sequential
}

// Cleanup cleans up browser resources
func (t *BrowserTool) Cleanup() error {
	if t.page != nil {
		if err := t.page.Close(); err != nil {
			return err
		}
		t.page = nil
	}
	
	if t.browser != nil {
		if err := t.browser.Close(); err != nil {
			return err
		}
		t.browser = nil
	}
	
	if t.playwright != nil {
		if err := t.playwright.Stop(); err != nil {
			return err
		}
		t.playwright = nil
	}
	
	return nil
}
