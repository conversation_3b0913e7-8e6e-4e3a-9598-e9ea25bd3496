# Arien - Elite AI-Powered Software Engineering Assistant
# Copyright 2025 Arien LLC - MIT License

# Build stage
FROM golang:1.24-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git make

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN make build

# Runtime stage
FROM alpine:latest

# Install runtime dependencies
RUN apk add --no-cache ca-certificates tzdata

# Create non-root user
RUN addgroup -g 1001 -S arien && \
    adduser -u 1001 -S arien -G arien

# Set working directory
WORKDIR /home/<USER>

# Copy binary from builder stage
COPY --from=builder /app/arien /usr/local/bin/arien

# Change ownership
RUN chown -R arien:arien /home/<USER>

# Switch to non-root user
USER arien

# Create config directory
RUN mkdir -p /home/<USER>/.arien

# Expose any necessary ports (if needed for web interface in future)
# EXPOSE 8080

# Set entrypoint
ENTRYPOINT ["arien"]

# Default command
CMD ["--help"]
