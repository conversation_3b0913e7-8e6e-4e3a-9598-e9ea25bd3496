/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package ui

import (
	"fmt"
	"strings"

	"arien/internal/ui/themes"

	"github.com/charmbracelet/bubbles/textinput"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// AuthScreen represents the authentication setup screen
type AuthScreen struct {
	theme         themes.Theme
	width         int
	height        int
	step          int
	maxSteps      int
	
	// Provider selection
	providers     []string
	selectedProvider int
	
	// API key input
	apiKeyInput   textinput.Model
	
	// Model selection
	models        []string
	selectedModel int
	
	// State
	completed     bool
	error         string
}

// NewAuthScreen creates a new authentication screen
func NewAuthScreen() *AuthScreen {
	apiKeyInput := textinput.New()
	apiKeyInput.Placeholder = "Enter your API key..."
	apiKeyInput.EchoMode = textinput.EchoPassword
	apiKeyInput.EchoCharacter = '•'
	apiKeyInput.Focus()

	return &AuthScreen{
		theme:         themes.GetDefaultTheme(),
		step:          0,
		maxSteps:      3,
		providers:     []string{"DeepSeek", "OpenAI", "Google Gemini", "Anthropic Claude"},
		selectedProvider: 0,
		apiKeyInput:   apiKeyInput,
		models:        []string{"deepseek-chat", "deepseek-reasoner"},
		selectedModel: 0,
	}
}

// Init initializes the auth screen
func (as *AuthScreen) Init() tea.Cmd {
	return textinput.Blink
}

// Update handles messages
func (as *AuthScreen) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmd tea.Cmd

	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		as.width = msg.Width
		as.height = msg.Height
		return as, nil

	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q":
			return as, tea.Quit

		case "enter":
			return as.handleEnter()

		case "up", "k":
			return as.handleUp()

		case "down", "j":
			return as.handleDown()

		case "left", "h":
			return as.handleLeft()

		case "right", "l":
			return as.handleRight()

		case "tab":
			return as.handleTab()

		case "shift+tab":
			return as.handleShiftTab()
		}

		// Handle input for API key step
		if as.step == 1 {
			as.apiKeyInput, cmd = as.apiKeyInput.Update(msg)
			return as, cmd
		}
	}

	return as, nil
}

// View renders the auth screen
func (as *AuthScreen) View() string {
	if as.width == 0 || as.height == 0 {
		return "Loading..."
	}

	// Create main container
	container := lipgloss.NewStyle().
		Width(as.width).
		Height(as.height).
		Background(as.theme.Colors.Background).
		Foreground(as.theme.Colors.Text).
		Padding(2)

	// Header
	header := as.renderHeader()
	
	// Progress indicator
	progress := as.renderProgress()
	
	// Main content based on current step
	var content string
	switch as.step {
	case 0:
		content = as.renderProviderSelection()
	case 1:
		content = as.renderAPIKeyInput()
	case 2:
		content = as.renderModelSelection()
	default:
		content = as.renderCompletion()
	}
	
	// Footer
	footer := as.renderFooter()
	
	// Error message if any
	errorMsg := ""
	if as.error != "" {
		errorStyle := as.theme.Styles.Error.
			Width(as.width - 8).
			Align(lipgloss.Center).
			Border(lipgloss.RoundedBorder()).
			Padding(1).
			Margin(1)
		errorMsg = errorStyle.Render("❌ " + as.error)
	}
	
	// Combine all sections
	fullContent := lipgloss.JoinVertical(
		lipgloss.Center,
		header,
		progress,
		content,
		errorMsg,
		footer,
	)
	
	return container.Render(fullContent)
}

// renderHeader renders the header section
func (as *AuthScreen) renderHeader() string {
	title := as.theme.Styles.Title.
		Width(as.width - 4).
		Align(lipgloss.Center).
		Render("🚀 Welcome to Arien")
	
	subtitle := as.theme.Styles.Subtitle.
		Width(as.width - 4).
		Align(lipgloss.Center).
		Render("Elite AI-Powered Software Engineering Assistant")
	
	description := as.theme.Styles.Body.
		Width(as.width - 4).
		Align(lipgloss.Center).
		Render("Let's set up your AI provider to get started")
	
	return lipgloss.JoinVertical(
		lipgloss.Center,
		title,
		subtitle,
		"",
		description,
		"",
	)
}

// renderProgress renders the progress indicator
func (as *AuthScreen) renderProgress() string {
	progressStyle := as.theme.Styles.Info
	
	steps := []string{"Provider", "API Key", "Model", "Complete"}
	var progressItems []string
	
	for i, stepName := range steps {
		if i < as.step {
			// Completed step
			progressItems = append(progressItems, as.theme.Styles.Success.Render("✓ "+stepName))
		} else if i == as.step {
			// Current step
			progressItems = append(progressItems, as.theme.Styles.Highlight.Render("● "+stepName))
		} else {
			// Future step
			progressItems = append(progressItems, as.theme.Styles.Caption.Render("○ "+stepName))
		}
	}
	
	progressBar := strings.Join(progressItems, "  ")
	
	return progressStyle.
		Width(as.width - 4).
		Align(lipgloss.Center).
		Render(progressBar) + "\n"
}

// renderProviderSelection renders the provider selection step
func (as *AuthScreen) renderProviderSelection() string {
	content := as.theme.Styles.Subtitle.
		Width(as.width - 4).
		Align(lipgloss.Center).
		Render("Step 1: Choose your AI Provider") + "\n\n"
	
	for i, provider := range as.providers {
		style := as.theme.Styles.Body
		prefix := "  "
		
		if i == as.selectedProvider {
			style = as.theme.Styles.Selection
			prefix = "▶ "
		}
		
		providerLine := style.
			Width(as.width - 8).
			Align(lipgloss.Left).
			Render(prefix + provider)
		
		content += providerLine + "\n"
	}
	
	return content
}

// renderAPIKeyInput renders the API key input step
func (as *AuthScreen) renderAPIKeyInput() string {
	providerName := as.providers[as.selectedProvider]
	
	content := as.theme.Styles.Subtitle.
		Width(as.width - 4).
		Align(lipgloss.Center).
		Render(fmt.Sprintf("Step 2: Enter %s API Key", providerName)) + "\n\n"
	
	instructions := as.theme.Styles.Body.
		Width(as.width - 4).
		Align(lipgloss.Center).
		Render(fmt.Sprintf("Please enter your %s API key:", providerName)) + "\n\n"
	
	inputStyle := as.theme.Styles.InputFocus.
		Width(as.width - 8).
		Align(lipgloss.Center)
	
	apiKeyField := inputStyle.Render(as.apiKeyInput.View()) + "\n\n"
	
	helpText := as.theme.Styles.Caption.
		Width(as.width - 4).
		Align(lipgloss.Center).
		Render("Your API key will be encrypted and stored securely")
	
	return content + instructions + apiKeyField + helpText
}

// renderModelSelection renders the model selection step
func (as *AuthScreen) renderModelSelection() string {
	providerName := as.providers[as.selectedProvider]
	
	// Update models based on selected provider
	as.updateModelsForProvider()
	
	content := as.theme.Styles.Subtitle.
		Width(as.width - 4).
		Align(lipgloss.Center).
		Render(fmt.Sprintf("Step 3: Choose %s Model", providerName)) + "\n\n"
	
	for i, model := range as.models {
		style := as.theme.Styles.Body
		prefix := "  "
		
		if i == as.selectedModel {
			style = as.theme.Styles.Selection
			prefix = "▶ "
		}
		
		modelLine := style.
			Width(as.width - 8).
			Align(lipgloss.Left).
			Render(prefix + model)
		
		content += modelLine + "\n"
	}
	
	return content
}

// renderCompletion renders the completion step
func (as *AuthScreen) renderCompletion() string {
	content := as.theme.Styles.Success.
		Width(as.width - 4).
		Align(lipgloss.Center).
		Render("🎉 Setup Complete!") + "\n\n"
	
	summary := fmt.Sprintf("Provider: %s\nModel: %s\nAPI Key: Configured ✓",
		as.providers[as.selectedProvider],
		as.models[as.selectedModel])
	
	summaryText := as.theme.Styles.Body.
		Width(as.width - 4).
		Align(lipgloss.Center).
		Render(summary) + "\n\n"
	
	nextSteps := as.theme.Styles.Info.
		Width(as.width - 4).
		Align(lipgloss.Center).
		Render("Press Enter to start using Arien!")
	
	return content + summaryText + nextSteps
}

// renderFooter renders the footer with controls
func (as *AuthScreen) renderFooter() string {
	var controls []string
	
	if as.step < as.maxSteps {
		controls = append(controls, "↑/↓ Navigate", "Enter Confirm", "Tab Next")
	} else {
		controls = append(controls, "Enter Start")
	}
	
	controls = append(controls, "Ctrl+C Quit")
	
	return as.theme.Styles.Footer.
		Width(as.width - 4).
		Align(lipgloss.Center).
		Render(strings.Join(controls, " • "))
}

// Event handlers

func (as *AuthScreen) handleEnter() (tea.Model, tea.Cmd) {
	as.error = ""
	
	switch as.step {
	case 0: // Provider selection
		as.step++
		as.apiKeyInput.Focus()
		return as, nil
	case 1: // API key input
		if as.apiKeyInput.Value() == "" {
			as.error = "API key cannot be empty"
			return as, nil
		}
		as.step++
		as.apiKeyInput.Blur()
		return as, nil
	case 2: // Model selection
		as.step++
		as.completed = true
		return as, nil
	default: // Completion
		return as, tea.Quit
	}
}

func (as *AuthScreen) handleUp() (tea.Model, tea.Cmd) {
	switch as.step {
	case 0: // Provider selection
		if as.selectedProvider > 0 {
			as.selectedProvider--
		}
	case 2: // Model selection
		if as.selectedModel > 0 {
			as.selectedModel--
		}
	}
	return as, nil
}

func (as *AuthScreen) handleDown() (tea.Model, tea.Cmd) {
	switch as.step {
	case 0: // Provider selection
		if as.selectedProvider < len(as.providers)-1 {
			as.selectedProvider++
		}
	case 2: // Model selection
		if as.selectedModel < len(as.models)-1 {
			as.selectedModel++
		}
	}
	return as, nil
}

func (as *AuthScreen) handleLeft() (tea.Model, tea.Cmd) {
	if as.step > 0 {
		as.step--
		if as.step == 1 {
			as.apiKeyInput.Focus()
		}
	}
	return as, nil
}

func (as *AuthScreen) handleRight() (tea.Model, tea.Cmd) {
	if as.step < as.maxSteps {
		as.step++
		if as.step == 1 {
			as.apiKeyInput.Focus()
		} else {
			as.apiKeyInput.Blur()
		}
	}
	return as, nil
}

func (as *AuthScreen) handleTab() (tea.Model, tea.Cmd) {
	return as.handleRight()
}

func (as *AuthScreen) handleShiftTab() (tea.Model, tea.Cmd) {
	return as.handleLeft()
}

// updateModelsForProvider updates the available models based on selected provider
func (as *AuthScreen) updateModelsForProvider() {
	switch as.selectedProvider {
	case 0: // DeepSeek
		as.models = []string{"deepseek-chat", "deepseek-reasoner"}
	case 1: // OpenAI
		as.models = []string{"gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"}
	case 2: // Google Gemini
		as.models = []string{"gemini-pro", "gemini-pro-vision"}
	case 3: // Anthropic
		as.models = []string{"claude-3-opus", "claude-3-sonnet", "claude-3-haiku"}
	}
	
	// Reset model selection
	as.selectedModel = 0
}
