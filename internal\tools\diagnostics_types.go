/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"fmt"
	"path/filepath"
	"strings"
	"time"
)

// Issue represents a code issue found during analysis
type Issue struct {
	File        string `json:"file"`
	Line        int    `json:"line"`
	Column      int    `json:"column"`
	Severity    string `json:"severity"`
	Type        string `json:"type"`
	Message     string `json:"message"`
	Rule        string `json:"rule,omitempty"`
	Suggestion  string `json:"suggestion,omitempty"`
}

// FormatIssue represents a formatting issue
type FormatIssue struct {
	Line        int    `json:"line"`
	Column      int    `json:"column"`
	Type        string `json:"type"`
	Description string `json:"description"`
	Severity    string `json:"severity"`
}

// CodeAnalysis represents comprehensive code analysis results
type CodeAnalysis struct {
	Path        string            `json:"path"`
	Language    string            `json:"language"`
	Timestamp   time.Time         `json:"timestamp"`
	Metrics     map[string]int    `json:"metrics"`
	Issues      []Issue           `json:"issues"`
	Suggestions []string          `json:"suggestions"`
}

// LinterResults represents linter execution results
type LinterResults struct {
	Language    string    `json:"language"`
	Path        string    `json:"path"`
	LinterUsed  string    `json:"linter_used"`
	Issues      []Issue   `json:"issues"`
	Timestamp   time.Time `json:"timestamp"`
}

// Helper methods for DiagnosticsTool

// detectLanguage detects programming language from file extension
func (t *DiagnosticsTool) detectLanguage(filePath string) string {
	ext := strings.ToLower(filepath.Ext(filePath))
	
	languageMap := map[string]string{
		".go":   "go",
		".py":   "python",
		".js":   "javascript",
		".ts":   "typescript",
		".java": "java",
		".c":    "c",
		".cpp":  "cpp",
		".cc":   "cpp",
		".cxx":  "cpp",
		".rs":   "rust",
		".php":  "php",
		".rb":   "ruby",
		".cs":   "csharp",
		".kt":   "kotlin",
		".swift": "swift",
	}
	
	return languageMap[ext]
}

// analyzeFile analyzes a single file for issues
func (t *DiagnosticsTool) analyzeFile(filePath, language, severity string) []Issue {
	issues := []Issue{}
	
	// Basic file analysis - this would be replaced with real analysis
	issues = append(issues, Issue{
		File:     filePath,
		Line:     1,
		Column:   1,
		Severity: "info",
		Type:     "analysis",
		Message:  fmt.Sprintf("File analyzed for %s", language),
		Rule:     "basic_analysis",
	})
	
	return issues
}

// calculateMetrics calculates code metrics
func (t *DiagnosticsTool) calculateMetrics(path, language string) map[string]int {
	metrics := map[string]int{
		"lines_of_code":     100,
		"cyclomatic_complexity": 5,
		"functions":         10,
		"classes":           2,
		"test_coverage":     85,
	}
	
	return metrics
}

// analyzeDirectory analyzes an entire directory
func (t *DiagnosticsTool) analyzeDirectory(path, language string, includeSuggestions bool) CodeAnalysis {
	analysis := CodeAnalysis{
		Path:        path,
		Language:    language,
		Timestamp:   time.Now(),
		Metrics:     t.calculateMetrics(path, language),
		Issues:      []Issue{},
		Suggestions: []string{},
	}
	
	if includeSuggestions {
		analysis.Suggestions = []string{
			"Consider adding more unit tests",
			"Review code complexity in large functions",
			"Add documentation for public APIs",
		}
	}
	
	return analysis
}

// analyzeFileDetailed performs detailed analysis of a single file
func (t *DiagnosticsTool) analyzeFileDetailed(path, language string, includeSuggestions bool) CodeAnalysis {
	analysis := CodeAnalysis{
		Path:        path,
		Language:    language,
		Timestamp:   time.Now(),
		Metrics:     t.calculateMetrics(path, language),
		Issues:      t.analyzeFile(path, language, "info"),
		Suggestions: []string{},
	}
	
	if includeSuggestions {
		analysis.Suggestions = []string{
			"Consider refactoring long functions",
			"Add error handling",
			"Improve variable naming",
		}
	}
	
	return analysis
}

// countIssuesBySeverity counts issues by severity level
func (t *DiagnosticsTool) countIssuesBySeverity(issues []Issue, severity string) int {
	count := 0
	for _, issue := range issues {
		if issue.Severity == severity {
			count++
		}
	}
	return count
}

// formatIssues formats issues for output
func (t *DiagnosticsTool) formatIssues(issues []Issue, format string) string {
	switch format {
	case "json":
		return t.formatIssuesJSON(issues)
	case "markdown":
		return t.formatIssuesMarkdown(issues)
	default:
		return t.formatIssuesText(issues)
	}
}

// formatIssuesText formats issues as plain text
func (t *DiagnosticsTool) formatIssuesText(issues []Issue) string {
	if len(issues) == 0 {
		return "✅ No issues found!"
	}
	
	var output strings.Builder
	output.WriteString(fmt.Sprintf("📋 Found %d issues:\n\n", len(issues)))
	
	for i, issue := range issues {
		output.WriteString(fmt.Sprintf("%d. %s:%d:%d [%s] %s\n", 
			i+1, issue.File, issue.Line, issue.Column, issue.Severity, issue.Message))
		if issue.Suggestion != "" {
			output.WriteString(fmt.Sprintf("   💡 Suggestion: %s\n", issue.Suggestion))
		}
		output.WriteString("\n")
	}
	
	return output.String()
}

// formatIssuesJSON formats issues as JSON
func (t *DiagnosticsTool) formatIssuesJSON(issues []Issue) string {
	// Simple JSON formatting - in production use json.Marshal
	var output strings.Builder
	output.WriteString("{\n")
	output.WriteString(fmt.Sprintf("  \"total_issues\": %d,\n", len(issues)))
	output.WriteString("  \"issues\": [\n")
	
	for i, issue := range issues {
		output.WriteString("    {\n")
		output.WriteString(fmt.Sprintf("      \"file\": \"%s\",\n", issue.File))
		output.WriteString(fmt.Sprintf("      \"line\": %d,\n", issue.Line))
		output.WriteString(fmt.Sprintf("      \"column\": %d,\n", issue.Column))
		output.WriteString(fmt.Sprintf("      \"severity\": \"%s\",\n", issue.Severity))
		output.WriteString(fmt.Sprintf("      \"message\": \"%s\"\n", issue.Message))
		output.WriteString("    }")
		if i < len(issues)-1 {
			output.WriteString(",")
		}
		output.WriteString("\n")
	}
	
	output.WriteString("  ]\n")
	output.WriteString("}\n")
	
	return output.String()
}

// formatIssuesMarkdown formats issues as Markdown
func (t *DiagnosticsTool) formatIssuesMarkdown(issues []Issue) string {
	var output strings.Builder
	output.WriteString("# Code Analysis Results\n\n")
	
	if len(issues) == 0 {
		output.WriteString("✅ **No issues found!**\n")
		return output.String()
	}
	
	output.WriteString(fmt.Sprintf("Found **%d issues**:\n\n", len(issues)))
	
	// Group by severity
	severityGroups := map[string][]Issue{
		"error":   {},
		"warning": {},
		"info":    {},
	}
	
	for _, issue := range issues {
		severityGroups[issue.Severity] = append(severityGroups[issue.Severity], issue)
	}
	
	for severity, groupIssues := range severityGroups {
		if len(groupIssues) == 0 {
			continue
		}
		
		output.WriteString(fmt.Sprintf("## %s Issues (%d)\n\n", 
			strings.Title(severity), len(groupIssues)))
		
		for _, issue := range groupIssues {
			output.WriteString(fmt.Sprintf("- **%s:%d:%d** - %s\n", 
				issue.File, issue.Line, issue.Column, issue.Message))
			if issue.Suggestion != "" {
				output.WriteString(fmt.Sprintf("  > 💡 %s\n", issue.Suggestion))
			}
		}
		output.WriteString("\n")
	}
	
	return output.String()
}

// formatAnalysis formats code analysis results
func (t *DiagnosticsTool) formatAnalysis(analysis CodeAnalysis, format string) string {
	var output strings.Builder
	
	output.WriteString(fmt.Sprintf("📊 Code Analysis Results for: %s\n", analysis.Path))
	output.WriteString(strings.Repeat("=", 60) + "\n\n")
	
	output.WriteString("📈 Metrics:\n")
	for metric, value := range analysis.Metrics {
		output.WriteString(fmt.Sprintf("  %s: %d\n", strings.ReplaceAll(metric, "_", " "), value))
	}
	output.WriteString("\n")
	
	if len(analysis.Issues) > 0 {
		output.WriteString(t.formatIssues(analysis.Issues, format))
	}
	
	if len(analysis.Suggestions) > 0 {
		output.WriteString("💡 Suggestions:\n")
		for i, suggestion := range analysis.Suggestions {
			output.WriteString(fmt.Sprintf("  %d. %s\n", i+1, suggestion))
		}
	}
	
	return output.String()
}

// formatLinterResults formats linter results
func (t *DiagnosticsTool) formatLinterResults(results LinterResults, format string) string {
	var output strings.Builder
	
	output.WriteString(fmt.Sprintf("🔍 Linter Results (%s)\n", results.LinterUsed))
	output.WriteString(strings.Repeat("=", 50) + "\n\n")
	
	output.WriteString(fmt.Sprintf("📁 Path: %s\n", results.Path))
	output.WriteString(fmt.Sprintf("🔧 Language: %s\n", results.Language))
	output.WriteString(fmt.Sprintf("⏰ Analyzed: %s\n\n", results.Timestamp.Format("2006-01-02 15:04:05")))
	
	if len(results.Issues) > 0 {
		output.WriteString(t.formatIssues(results.Issues, format))
	} else {
		output.WriteString("✅ No linting issues found!\n")
	}
	
	return output.String()
}

// formatFormatIssues formats formatting issues
func (t *DiagnosticsTool) formatFormatIssues(issues []FormatIssue, format string) string {
	var output strings.Builder
	
	output.WriteString("🎨 Code Formatting Issues\n")
	output.WriteString(strings.Repeat("=", 40) + "\n\n")
	
	if len(issues) == 0 {
		output.WriteString("✅ No formatting issues found!\n")
		return output.String()
	}
	
	output.WriteString(fmt.Sprintf("Found %d formatting issues:\n\n", len(issues)))
	
	for i, issue := range issues {
		output.WriteString(fmt.Sprintf("%d. Line %d:%d [%s] %s\n", 
			i+1, issue.Line, issue.Column, issue.Severity, issue.Description))
	}
	
	return output.String()
}
