/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package auth

import (
	"context"
	"fmt"
	"strings"

	"arien/internal/config"
	"arien/internal/core"
	"arien/internal/security"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/charmbracelet/log"
)

// AuthApp handles the authentication setup flow
type AuthApp struct {
	engine *core.Engine
	logger *log.Logger
}

// authModel represents the authentication UI model
type authModel struct {
	engine    *core.Engine
	logger    *log.Logger
	validator *security.Validator
	step      authStep
	provider  string
	apiKey    string
	model     string
	cursor    int
	err       error
	done      bool
	validating bool
	validationResult string
}

type authStep int

const (
	stepProvider authStep = iota
	stepAPIKey
	stepValidation
	stepModel
	stepComplete
)

// NewAuthApp creates a new authentication app
func NewAuthApp(engine *core.Engine, logger *log.Logger) *AuthApp {
	return &AuthApp{
		engine: engine,
		logger: logger,
	}
}

// Run starts the authentication flow
func (a *AuthApp) Run(ctx context.Context) error {
	model := authModel{
		engine:    a.engine,
		logger:    a.logger,
		validator: security.NewValidator(),
		step:      stepProvider,
	}

	p := tea.NewProgram(model, tea.WithAltScreen())
	
	if _, err := p.Run(); err != nil {
		return fmt.Errorf("authentication UI failed: %w", err)
	}

	return nil
}

// Init initializes the model
func (m authModel) Init() tea.Cmd {
	return nil
}

// Update handles messages
func (m authModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q":
			return m, tea.Quit
		case "enter":
			return m.handleEnter()
		case "up", "k":
			if m.cursor > 0 {
				m.cursor--
			}
		case "down", "j":
			m.cursor++
		default:
			if m.step == stepAPIKey && !m.validating {
				// Handle API key input
				switch msg.String() {
				case "backspace":
					if len(m.apiKey) > 0 {
						m.apiKey = m.apiKey[:len(m.apiKey)-1]
					}
				default:
					if len(msg.String()) == 1 {
						m.apiKey += msg.String()
					}
				}
			}
		}
	case validationResultMsg:
		m.validating = false
		if msg.err != nil {
			m.err = msg.err
			m.step = stepAPIKey // Go back to API key input
		} else {
			m.validationResult = "✅ API key validated successfully!"
			m.step = stepModel
			m.cursor = 0
		}
	}

	return m, nil
}

// View renders the UI
func (m authModel) View() string {
	if m.done {
		return m.renderComplete()
	}

	switch m.step {
	case stepProvider:
		return m.renderProviderSelection()
	case stepAPIKey:
		return m.renderAPIKeyInput()
	case stepValidation:
		return m.renderValidation()
	case stepModel:
		return m.renderModelSelection()
	default:
		return "Unknown step"
	}
}

// handleEnter processes enter key based on current step
func (m authModel) handleEnter() (authModel, tea.Cmd) {
	switch m.step {
	case stepProvider:
		providers := []string{"deepseek", "openai", "gemini", "anthropic"}
		if m.cursor < len(providers) {
			m.provider = providers[m.cursor]
			m.step = stepAPIKey
			m.cursor = 0
		}
	case stepAPIKey:
		if m.apiKey != "" {
			// Start API key validation
			m.validating = true
			m.step = stepValidation
			m.err = nil
			return m, m.validateAPIKey()
		}
	case stepModel:
		models := m.getModelsForProvider()
		if m.cursor < len(models) {
			m.model = models[m.cursor]
			if err := m.saveConfiguration(); err != nil {
				m.err = err
			} else {
				m.done = true
				return m, tea.Quit
			}
		}
	}
	return m, nil
}

// validationResultMsg represents the result of API key validation
type validationResultMsg struct {
	err error
}

// validateAPIKey validates the API key asynchronously
func (m authModel) validateAPIKey() tea.Cmd {
	return func() tea.Msg {
		ctx := context.Background()

		// First validate the format
		if err := m.validator.ValidateAPIKey(m.provider, m.apiKey); err != nil {
			return validationResultMsg{err: fmt.Errorf("format validation failed: %w", err)}
		}

		// Then test the API key with a real call
		if err := m.validator.TestAPIKey(ctx, m.provider, m.apiKey, ""); err != nil {
			return validationResultMsg{err: fmt.Errorf("API key test failed: %w", err)}
		}

		return validationResultMsg{err: nil}
	}
}

// renderProviderSelection renders the provider selection screen
func (m authModel) renderProviderSelection() string {
	var s strings.Builder
	
	s.WriteString(titleStyle.Render("🤖 Arien - AI Assistant Setup"))
	s.WriteString("\n\n")
	s.WriteString("Welcome to Arien! Let's set up your AI provider.\n\n")
	s.WriteString("Select an AI provider:\n\n")

	providers := []string{"DeepSeek", "OpenAI", "Google Gemini", "Anthropic Claude"}
	descriptions := []string{
		"DeepSeek - Advanced reasoning models",
		"OpenAI - GPT-4 and GPT-3.5 models",
		"Google Gemini - Gemini Pro models",
		"Anthropic - Claude models",
	}

	for i := range providers {
		cursor := " "
		if m.cursor == i {
			cursor = ">"
			s.WriteString(selectedStyle.Render(fmt.Sprintf("%s %s", cursor, descriptions[i])))
		} else {
			s.WriteString(fmt.Sprintf("%s %s", cursor, descriptions[i]))
		}
		s.WriteString("\n")
	}

	s.WriteString("\n")
	s.WriteString(helpStyle.Render("↑/↓: navigate • enter: select • q: quit"))

	return s.String()
}

// renderAPIKeyInput renders the API key input screen
func (m authModel) renderAPIKeyInput() string {
	var s strings.Builder
	
	s.WriteString(titleStyle.Render("🔑 API Key Setup"))
	s.WriteString("\n\n")
	s.WriteString(fmt.Sprintf("Provider: %s\n\n", m.provider))
	s.WriteString("Please enter your API key:\n\n")

	// Mask the API key for display
	maskedKey := strings.Repeat("*", len(m.apiKey))
	if len(m.apiKey) > 0 {
		s.WriteString(inputStyle.Render(maskedKey))
	} else {
		s.WriteString(placeholderStyle.Render("Enter your API key..."))
	}

	s.WriteString("\n\n")
	s.WriteString(helpStyle.Render("Type your API key • enter: continue • q: quit"))

	if m.err != nil {
		s.WriteString("\n\n")
		s.WriteString(errorStyle.Render(fmt.Sprintf("Error: %v", m.err)))
	}

	return s.String()
}

// renderValidation renders the API key validation screen
func (m authModel) renderValidation() string {
	var s strings.Builder

	s.WriteString(titleStyle.Render("🔍 Validating API Key"))
	s.WriteString("\n\n")
	s.WriteString(fmt.Sprintf("Provider: %s\n", m.provider))
	s.WriteString("API Key: " + strings.Repeat("*", len(m.apiKey)) + "\n\n")

	if m.validating {
		s.WriteString(loadingStyle.Render("🔄 Validating API key..."))
		s.WriteString("\n\n")
		s.WriteString("This may take a few seconds as we test your API key with the provider.")
	} else if m.validationResult != "" {
		s.WriteString(successStyle.Render(m.validationResult))
		s.WriteString("\n\n")
		s.WriteString(helpStyle.Render("Press enter to continue"))
	}

	if m.err != nil {
		s.WriteString("\n\n")
		s.WriteString(errorStyle.Render(fmt.Sprintf("❌ %v", m.err)))
		s.WriteString("\n\n")
		s.WriteString(helpStyle.Render("Press enter to try again"))
	}

	return s.String()
}

// renderModelSelection renders the model selection screen
func (m authModel) renderModelSelection() string {
	var s strings.Builder
	
	s.WriteString(titleStyle.Render("🎯 Model Selection"))
	s.WriteString("\n\n")
	s.WriteString(fmt.Sprintf("Provider: %s\n\n", m.provider))
	s.WriteString("Select a model:\n\n")

	models := m.getModelsForProvider()
	for i, model := range models {
		cursor := " "
		if m.cursor == i {
			cursor = ">"
			s.WriteString(selectedStyle.Render(fmt.Sprintf("%s %s", cursor, model)))
		} else {
			s.WriteString(fmt.Sprintf("%s %s", cursor, model))
		}
		s.WriteString("\n")
	}

	s.WriteString("\n")
	s.WriteString(helpStyle.Render("↑/↓: navigate • enter: select • q: quit"))

	return s.String()
}

// renderComplete renders the completion screen
func (m authModel) renderComplete() string {
	var s strings.Builder
	
	s.WriteString(titleStyle.Render("✅ Setup Complete!"))
	s.WriteString("\n\n")
	s.WriteString("Arien has been configured successfully:\n\n")
	s.WriteString(fmt.Sprintf("Provider: %s\n", m.provider))
	s.WriteString(fmt.Sprintf("Model: %s\n", m.model))
	s.WriteString("\n")
	s.WriteString("You can now start using Arien!\n\n")
	s.WriteString(helpStyle.Render("Press any key to continue..."))

	return s.String()
}

// getModelsForProvider returns available models for the selected provider
func (m authModel) getModelsForProvider() []string {
	switch m.provider {
	case "deepseek":
		return []string{"deepseek-chat", "deepseek-reasoner"}
	case "openai":
		return []string{"gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"}
	case "gemini":
		return []string{"gemini-pro", "gemini-pro-vision"}
	case "anthropic":
		return []string{"claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"}
	default:
		return []string{}
	}
}

// saveConfiguration saves the configuration
func (m authModel) saveConfiguration() error {
	providerConfig := config.ProviderConfig{
		APIKey: m.apiKey,
		Model:  m.model,
	}

	return m.engine.Config().SetProvider(m.provider, providerConfig)
}

// Styles
var (
	titleStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#7C3AED")).
			Bold(true).
			Padding(1, 2)

	selectedStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#7C3AED")).
			Bold(true)

	inputStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#10B981")).
			Background(lipgloss.Color("#1F2937")).
			Padding(0, 1)

	placeholderStyle = lipgloss.NewStyle().
				Foreground(lipgloss.Color("#6B7280")).
				Italic(true)

	helpStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#6B7280")).
			Italic(true)

	errorStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#EF4444")).
			Bold(true)

	loadingStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#F59E0B")).
			Italic(true)

	successStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#10B981")).
			Bold(true)
)
