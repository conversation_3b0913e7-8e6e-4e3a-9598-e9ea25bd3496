/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package llm

import (
	"context"
	"fmt"
	"sync"
	"time"

	"arien/internal/config"
	"arien/internal/interfaces"
	"arien/internal/llm/anthropic"
	"arien/internal/llm/deepseek"
	"arien/internal/llm/gemini"
	"arien/internal/llm/openai"

	"github.com/charmbracelet/log"
)

// ConfigProvider defines the interface for configuration access
type ConfigProvider interface {
	GetProvider(name string) (config.ProviderConfig, bool)
}

// Manager manages multiple LLM providers
type Manager struct {
	config    ConfigProvider
	logger    *log.Logger
	providers map[string]interfaces.Provider
	mu        sync.RWMutex
}

// NewManager creates a new LLM manager
func NewManager(config ConfigProvider, logger *log.Logger) (*Manager, error) {
	manager := &Manager{
		config:    config,
		logger:    logger,
		providers: make(map[string]interfaces.Provider),
	}

	if err := manager.initializeProviders(); err != nil {
		return nil, fmt.Errorf("failed to initialize providers: %w", err)
	}

	return manager, nil
}

// initializeProviders initializes all configured LLM providers
func (m *Manager) initializeProviders() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// Initialize DeepSeek provider
	if config, exists := m.config.GetProvider("deepseek"); exists {
		provider, err := deepseek.NewProvider(config, m.logger)
		if err != nil {
			m.logger.Error("Failed to initialize DeepSeek provider", "error", err)
		} else {
			m.providers["deepseek"] = provider
			m.logger.Info("DeepSeek provider initialized")
		}
	}

	// Initialize OpenAI provider
	if config, exists := m.config.GetProvider("openai"); exists {
		provider, err := openai.NewProvider(config, m.logger)
		if err != nil {
			m.logger.Error("Failed to initialize OpenAI provider", "error", err)
		} else {
			m.providers["openai"] = provider
			m.logger.Info("OpenAI provider initialized")
		}
	}

	// Initialize Gemini provider
	if config, exists := m.config.GetProvider("gemini"); exists {
		provider, err := gemini.NewProvider(config, m.logger)
		if err != nil {
			m.logger.Error("Failed to initialize Gemini provider", "error", err)
		} else {
			m.providers["gemini"] = provider
			m.logger.Info("Gemini provider initialized")
		}
	}

	// Initialize Anthropic provider
	if config, exists := m.config.GetProvider("anthropic"); exists {
		provider, err := anthropic.NewProvider(config, m.logger)
		if err != nil {
			m.logger.Error("Failed to initialize Anthropic provider", "error", err)
		} else {
			m.providers["anthropic"] = provider
			m.logger.Info("Anthropic provider initialized")
		}
	}

	if len(m.providers) == 0 {
		m.logger.Warn("No LLM providers configured - some features will be limited")
	}

	m.logger.Info("LLM manager initialized", "providers", len(m.providers))
	return nil
}

// GetProvider returns a specific provider by name
func (m *Manager) GetProvider(name string) (interfaces.Provider, bool) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	provider, exists := m.providers[name]
	return provider, exists
}

// GetProviders returns all available providers
func (m *Manager) GetProviders() map[string]interfaces.Provider {
	m.mu.RLock()
	defer m.mu.RUnlock()

	providers := make(map[string]interfaces.Provider)
	for name, provider := range m.providers {
		providers[name] = provider
	}
	return providers
}

// GetDefaultProvider returns the best available provider based on priority
func (m *Manager) GetDefaultProvider() (interfaces.Provider, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if len(m.providers) == 0 {
		return nil, fmt.Errorf("no providers available")
	}

	// Provider priority order (most preferred first)
	providerPriority := []string{
		"deepseek",   // Fast and cost-effective
		"openai",     // Reliable and well-tested
		"anthropic",  // High quality responses
		"gemini",     // Good for specific use cases
	}

	// Try to find providers in priority order
	for _, preferredName := range providerPriority {
		if provider, exists := m.providers[preferredName]; exists {
			m.logger.Debug("Selected default provider", "provider", preferredName)
			return provider, nil
		}
	}

	// If no preferred provider found, return any available provider
	for name, provider := range m.providers {
		m.logger.Debug("Using fallback provider", "provider", name)
		return provider, nil
	}

	return nil, fmt.Errorf("no providers available")
}

// ProcessMessage processes a message using the default or specified provider
func (m *Manager) ProcessMessage(ctx context.Context, message string, tools []interfaces.Tool) (*interfaces.Response, error) {
	provider, err := m.GetDefaultProvider()
	if err != nil {
		return nil, fmt.Errorf("no provider available: %w", err)
	}

	return m.ProcessMessageWithProvider(ctx, provider.Name(), message, tools)
}

// ProcessMessageWithProvider processes a message using a specific provider
func (m *Manager) ProcessMessageWithProvider(ctx context.Context, providerName, message string, tools []interfaces.Tool) (*interfaces.Response, error) {
	provider, exists := m.GetProvider(providerName)
	if !exists {
		return nil, fmt.Errorf("provider %s not found", providerName)
	}

	// Get provider configuration
	config, exists := m.config.GetProvider(providerName)
	if !exists {
		return nil, fmt.Errorf("configuration for provider %s not found", providerName)
	}

	// Create request
	request := &interfaces.Request{
		Messages: []interfaces.Message{
			interfaces.NewUserMessage(message),
		},
		Model:       config.Model,
		Tools:       tools,
		Temperature: 0.7,
		MaxTokens:   4000,
		Metadata: map[string]interface{}{
			"timestamp": time.Now(),
		},
	}

	// Add system message for Arien context
	systemMessage := interfaces.NewSystemMessage(m.getSystemPrompt())
	request.Messages = append([]interfaces.Message{systemMessage}, request.Messages...)

	m.logger.Debug("Processing message", "provider", providerName, "model", config.Model)

	// Process the request
	response, err := provider.ProcessMessage(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("provider %s failed to process message: %w", providerName, err)
	}

	response.Provider = providerName
	response.Timestamp = time.Now()

	return response, nil
}

// getSystemPrompt returns the system prompt for Arien
func (m *Manager) getSystemPrompt() string {
	return `You are Arien, an elite AI-powered software engineering assistant with advanced reasoning capabilities, comprehensive tool orchestration, and deep expertise across all programming languages, frameworks, and software engineering disciplines.

Your mission is to provide intelligent, proactive, and context-aware assistance for the complete spectrum of software engineering tasks including:
- Development and coding
- Debugging and troubleshooting  
- Testing and quality assurance
- Documentation and architecture
- Performance optimization
- Security analysis
- Workflow automation

You have access to a comprehensive set of built-in tools for file operations, search, shell integration, memory management, web tools, and task management. Use these tools effectively to provide the best assistance possible.

Always be:
- Precise and accurate in your responses
- Proactive in suggesting improvements
- Context-aware of the current project and user needs
- Systematic in your problem-solving approach
- Clear in your explanations and recommendations

When using tools, explain what you're doing and why. When encountering errors, provide clear troubleshooting steps and alternative approaches.`
}

// AddProvider adds a new provider to the manager
func (m *Manager) AddProvider(name string, provider interfaces.Provider) {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.providers[name] = provider
	m.logger.Info("Provider added", "name", name)
}

// RemoveProvider removes a provider from the manager
func (m *Manager) RemoveProvider(name string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if provider, exists := m.providers[name]; exists {
		// Attempt graceful shutdown
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		
		if err := provider.Shutdown(ctx); err != nil {
			m.logger.Error("Failed to shutdown provider", "name", name, "error", err)
		}
		
		delete(m.providers, name)
		m.logger.Info("Provider removed", "name", name)
	}
}

// Shutdown gracefully shuts down all providers
func (m *Manager) Shutdown(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.logger.Info("Shutting down LLM manager...")

	var errors []error
	for name, provider := range m.providers {
		if err := provider.Shutdown(ctx); err != nil {
			errors = append(errors, fmt.Errorf("failed to shutdown provider %s: %w", name, err))
		}
	}

	m.providers = make(map[string]interfaces.Provider)

	if len(errors) > 0 {
		return fmt.Errorf("shutdown errors: %v", errors)
	}

	m.logger.Info("LLM manager shutdown complete")
	return nil
}
