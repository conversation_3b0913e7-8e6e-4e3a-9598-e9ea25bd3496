/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// EditTool implements interactive file editing with AI suggestions and diff preview
type EditTool struct {
	backupEnabled bool
}

// NewEditTool creates a new edit tool
func NewEditTool() *EditTool {
	return &EditTool{
		backupEnabled: true,
	}
}

// Name returns the tool name
func (t *EditTool) Name() string {
	return "edit"
}

// Description returns the tool description
func (t *EditTool) Description() string {
	return "Interactive file editing with AI-powered suggestions and diff preview"
}

// Parameters returns the tool parameter schema
func (t *EditTool) Parameters() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"file": StringParameter("File path to edit", true),
			"action": map[string]interface{}{
				"type": "string",
				"description": "Edit action to perform",
				"enum": []string{"replace", "insert", "append", "prepend", "delete", "suggest", "diff"},
			},
			"content": StringParameter("New content or content to insert", false),
			"line_number": IntParameter("Line number for insert/delete operations", 1, 0),
			"start_line": IntParameter("Start line for range operations", 1, 0),
			"end_line": IntParameter("End line for range operations", 1, 0),
			"search_pattern": StringParameter("Pattern to search and replace", false),
			"replacement": StringParameter("Replacement text for search pattern", false),
			"create_backup": BoolParameter("Create backup before editing", true),
			"show_diff": BoolParameter("Show diff preview before applying", true),
			"ai_suggestions": BoolParameter("Enable AI-powered suggestions", true),
			"preserve_formatting": BoolParameter("Preserve existing code formatting", true),
		},
		"required": []string{"file", "action"},
	}
}

// Execute executes the edit tool
func (t *EditTool) Execute(ctx context.Context, args map[string]interface{}) Result {
	filePath, ok := args["file"].(string)
	if !ok || filePath == "" {
		return Result{Error: fmt.Errorf("file path is required")}
	}

	action, ok := args["action"].(string)
	if !ok || action == "" {
		return Result{Error: fmt.Errorf("action is required")}
	}

	// Get optional parameters
	content, _ := args["content"].(string)
	lineNumber := int(getFloatArg(args, "line_number", 1))
	startLine := int(getFloatArg(args, "start_line", 1))
	endLine := int(getFloatArg(args, "end_line", 1))
	searchPattern, _ := args["search_pattern"].(string)
	replacement, _ := args["replacement"].(string)
	createBackup := getBoolArg(args, "create_backup", true)
	showDiff := getBoolArg(args, "show_diff", true)
	aiSuggestions := getBoolArg(args, "ai_suggestions", true)
	preserveFormatting := getBoolArg(args, "preserve_formatting", true)

	// Execute the edit action
	switch action {
	case "replace":
		return t.replaceContent(filePath, content, createBackup, showDiff)
	case "insert":
		return t.insertContent(filePath, content, lineNumber, createBackup, showDiff)
	case "append":
		return t.appendContent(filePath, content, createBackup, showDiff)
	case "prepend":
		return t.prependContent(filePath, content, createBackup, showDiff)
	case "delete":
		return t.deleteLines(filePath, startLine, endLine, createBackup, showDiff)
	case "suggest":
		return t.generateSuggestions(filePath, aiSuggestions, preserveFormatting)
	case "diff":
		return t.showDiff(filePath, content)
	default:
		if searchPattern != "" && replacement != "" {
			return t.searchAndReplace(filePath, searchPattern, replacement, createBackup, showDiff)
		}
		return Result{Error: fmt.Errorf("unknown action: %s", action)}
	}
}

// Validate validates the tool arguments
func (t *EditTool) Validate(args map[string]interface{}) error {
	filePath, ok := args["file"].(string)
	if !ok || filePath == "" {
		return fmt.Errorf("file path is required")
	}

	action, ok := args["action"].(string)
	if !ok || action == "" {
		return fmt.Errorf("action is required")
	}

	validActions := []string{"replace", "insert", "append", "prepend", "delete", "suggest", "diff"}
	valid := false
	for _, validAction := range validActions {
		if action == validAction {
			valid = true
			break
		}
	}
	if !valid {
		return fmt.Errorf("invalid action: %s", action)
	}

	// Validate file exists for most operations
	if action != "suggest" {
		if _, err := os.Stat(filePath); os.IsNotExist(err) && action != "replace" {
			return fmt.Errorf("file does not exist: %s", filePath)
		}
	}

	return nil
}

// SupportsParallel returns true if the tool can be executed in parallel
func (t *EditTool) SupportsParallel() bool {
	return false // File editing should be sequential to avoid conflicts
}

// replaceContent replaces entire file content
func (t *EditTool) replaceContent(filePath, content string, createBackup, showDiff bool) Result {
	var originalContent string
	
	// Read original content if file exists
	if data, err := os.ReadFile(filePath); err == nil {
		originalContent = string(data)
	}

	// Create backup if requested
	if createBackup && originalContent != "" {
		if err := t.createBackup(filePath, originalContent); err != nil {
			return Result{Error: fmt.Errorf("failed to create backup: %w", err)}
		}
	}

	// Show diff if requested
	var diffOutput string
	if showDiff && originalContent != "" {
		diffOutput = t.generateDiff(originalContent, content)
	}

	// Write new content
	if err := os.WriteFile(filePath, []byte(content), 0644); err != nil {
		return Result{Error: fmt.Errorf("failed to write file: %w", err)}
	}

	output := fmt.Sprintf("File '%s' successfully replaced\n", filePath)
	if diffOutput != "" {
		output += "\nDiff Preview:\n" + diffOutput
	}

	metadata := map[string]interface{}{
		"action":           "replace",
		"file":             filePath,
		"original_size":    len(originalContent),
		"new_size":         len(content),
		"backup_created":   createBackup && originalContent != "",
		"diff_shown":       showDiff && originalContent != "",
	}

	return Result{
		Output:   output,
		Metadata: metadata,
	}
}

// insertContent inserts content at specified line
func (t *EditTool) insertContent(filePath, content string, lineNumber int, createBackup, showDiff bool) Result {
	// Read original content
	data, err := os.ReadFile(filePath)
	if err != nil {
		return Result{Error: fmt.Errorf("failed to read file: %w", err)}
	}

	originalContent := string(data)
	lines := strings.Split(originalContent, "\n")

	// Validate line number
	if lineNumber < 1 || lineNumber > len(lines)+1 {
		return Result{Error: fmt.Errorf("invalid line number: %d (file has %d lines)", lineNumber, len(lines))}
	}

	// Insert content
	insertLines := strings.Split(content, "\n")
	newLines := make([]string, 0, len(lines)+len(insertLines))
	
	// Add lines before insertion point
	newLines = append(newLines, lines[:lineNumber-1]...)
	// Add new content
	newLines = append(newLines, insertLines...)
	// Add remaining lines
	newLines = append(newLines, lines[lineNumber-1:]...)

	newContent := strings.Join(newLines, "\n")

	// Create backup if requested
	if createBackup {
		if err := t.createBackup(filePath, originalContent); err != nil {
			return Result{Error: fmt.Errorf("failed to create backup: %w", err)}
		}
	}

	// Show diff if requested
	var diffOutput string
	if showDiff {
		diffOutput = t.generateDiff(originalContent, newContent)
	}

	// Write modified content
	if err := os.WriteFile(filePath, []byte(newContent), 0644); err != nil {
		return Result{Error: fmt.Errorf("failed to write file: %w", err)}
	}

	output := fmt.Sprintf("Content inserted at line %d in '%s'\n", lineNumber, filePath)
	if diffOutput != "" {
		output += "\nDiff Preview:\n" + diffOutput
	}

	metadata := map[string]interface{}{
		"action":         "insert",
		"file":           filePath,
		"line_number":    lineNumber,
		"lines_inserted": len(insertLines),
		"backup_created": createBackup,
		"diff_shown":     showDiff,
	}

	return Result{
		Output:   output,
		Metadata: metadata,
	}
}

// appendContent appends content to end of file
func (t *EditTool) appendContent(filePath, content string, createBackup, showDiff bool) Result {
	// Read original content
	data, err := os.ReadFile(filePath)
	if err != nil {
		return Result{Error: fmt.Errorf("failed to read file: %w", err)}
	}

	originalContent := string(data)
	newContent := originalContent + "\n" + content

	// Create backup if requested
	if createBackup {
		if err := t.createBackup(filePath, originalContent); err != nil {
			return Result{Error: fmt.Errorf("failed to create backup: %w", err)}
		}
	}

	// Show diff if requested
	var diffOutput string
	if showDiff {
		diffOutput = t.generateDiff(originalContent, newContent)
	}

	// Write modified content
	if err := os.WriteFile(filePath, []byte(newContent), 0644); err != nil {
		return Result{Error: fmt.Errorf("failed to write file: %w", err)}
	}

	output := fmt.Sprintf("Content appended to '%s'\n", filePath)
	if diffOutput != "" {
		output += "\nDiff Preview:\n" + diffOutput
	}

	metadata := map[string]interface{}{
		"action":         "append",
		"file":           filePath,
		"content_added":  len(content),
		"backup_created": createBackup,
		"diff_shown":     showDiff,
	}

	return Result{
		Output:   output,
		Metadata: metadata,
	}
}

// prependContent prepends content to beginning of file
func (t *EditTool) prependContent(filePath, content string, createBackup, showDiff bool) Result {
	// Read original content
	data, err := os.ReadFile(filePath)
	if err != nil {
		return Result{Error: fmt.Errorf("failed to read file: %w", err)}
	}

	originalContent := string(data)
	newContent := content + "\n" + originalContent

	// Create backup if requested
	if createBackup {
		if err := t.createBackup(filePath, originalContent); err != nil {
			return Result{Error: fmt.Errorf("failed to create backup: %w", err)}
		}
	}

	// Show diff if requested
	var diffOutput string
	if showDiff {
		diffOutput = t.generateDiff(originalContent, newContent)
	}

	// Write modified content
	if err := os.WriteFile(filePath, []byte(newContent), 0644); err != nil {
		return Result{Error: fmt.Errorf("failed to write file: %w", err)}
	}

	output := fmt.Sprintf("Content prepended to '%s'\n", filePath)
	if diffOutput != "" {
		output += "\nDiff Preview:\n" + diffOutput
	}

	metadata := map[string]interface{}{
		"action":         "prepend",
		"file":           filePath,
		"content_added":  len(content),
		"backup_created": createBackup,
		"diff_shown":     showDiff,
	}

	return Result{
		Output:   output,
		Metadata: metadata,
	}
}

// deleteLines deletes specified line range
func (t *EditTool) deleteLines(filePath string, startLine, endLine int, createBackup, showDiff bool) Result {
	// Read original content
	data, err := os.ReadFile(filePath)
	if err != nil {
		return Result{Error: fmt.Errorf("failed to read file: %w", err)}
	}

	originalContent := string(data)
	lines := strings.Split(originalContent, "\n")

	// Validate line numbers
	if startLine < 1 || endLine < 1 || startLine > len(lines) || endLine > len(lines) || startLine > endLine {
		return Result{Error: fmt.Errorf("invalid line range: %d-%d (file has %d lines)", startLine, endLine, len(lines))}
	}

	// Delete lines
	newLines := make([]string, 0, len(lines)-(endLine-startLine+1))
	newLines = append(newLines, lines[:startLine-1]...)
	newLines = append(newLines, lines[endLine:]...)

	newContent := strings.Join(newLines, "\n")

	// Create backup if requested
	if createBackup {
		if err := t.createBackup(filePath, originalContent); err != nil {
			return Result{Error: fmt.Errorf("failed to create backup: %w", err)}
		}
	}

	// Show diff if requested
	var diffOutput string
	if showDiff {
		diffOutput = t.generateDiff(originalContent, newContent)
	}

	// Write modified content
	if err := os.WriteFile(filePath, []byte(newContent), 0644); err != nil {
		return Result{Error: fmt.Errorf("failed to write file: %w", err)}
	}

	output := fmt.Sprintf("Lines %d-%d deleted from '%s'\n", startLine, endLine, filePath)
	if diffOutput != "" {
		output += "\nDiff Preview:\n" + diffOutput
	}

	metadata := map[string]interface{}{
		"action":         "delete",
		"file":           filePath,
		"start_line":     startLine,
		"end_line":       endLine,
		"lines_deleted":  endLine - startLine + 1,
		"backup_created": createBackup,
		"diff_shown":     showDiff,
	}

	return Result{
		Output:   output,
		Metadata: metadata,
	}
}

// searchAndReplace performs search and replace operation
func (t *EditTool) searchAndReplace(filePath, searchPattern, replacement string, createBackup, showDiff bool) Result {
	// Read original content
	data, err := os.ReadFile(filePath)
	if err != nil {
		return Result{Error: fmt.Errorf("failed to read file: %w", err)}
	}

	originalContent := string(data)
	newContent := strings.ReplaceAll(originalContent, searchPattern, replacement)

	// Count replacements
	replacements := strings.Count(originalContent, searchPattern)
	if replacements == 0 {
		return Result{
			Output: fmt.Sprintf("No matches found for pattern '%s' in '%s'\n", searchPattern, filePath),
			Metadata: map[string]interface{}{
				"action":       "search_replace",
				"file":         filePath,
				"pattern":      searchPattern,
				"replacements": 0,
			},
		}
	}

	// Create backup if requested
	if createBackup {
		if err := t.createBackup(filePath, originalContent); err != nil {
			return Result{Error: fmt.Errorf("failed to create backup: %w", err)}
		}
	}

	// Show diff if requested
	var diffOutput string
	if showDiff {
		diffOutput = t.generateDiff(originalContent, newContent)
	}

	// Write modified content
	if err := os.WriteFile(filePath, []byte(newContent), 0644); err != nil {
		return Result{Error: fmt.Errorf("failed to write file: %w", err)}
	}

	output := fmt.Sprintf("Replaced %d occurrences of '%s' with '%s' in '%s'\n", replacements, searchPattern, replacement, filePath)
	if diffOutput != "" {
		output += "\nDiff Preview:\n" + diffOutput
	}

	metadata := map[string]interface{}{
		"action":         "search_replace",
		"file":           filePath,
		"pattern":        searchPattern,
		"replacement":    replacement,
		"replacements":   replacements,
		"backup_created": createBackup,
		"diff_shown":     showDiff,
	}

	return Result{
		Output:   output,
		Metadata: metadata,
	}
}

// generateSuggestions generates AI-powered suggestions for file improvement
func (t *EditTool) generateSuggestions(filePath string, aiSuggestions, preserveFormatting bool) Result {
	// Read file content
	data, err := os.ReadFile(filePath)
	if err != nil {
		return Result{Error: fmt.Errorf("failed to read file: %w", err)}
	}

	content := string(data)
	ext := strings.ToLower(filepath.Ext(filePath))

	suggestions := []string{}

	// Basic code analysis and suggestions
	if isCodeFile(ext) {
		suggestions = append(suggestions, t.analyzeCode(content, ext)...)
	}

	// General file suggestions
	suggestions = append(suggestions, t.analyzeGeneral(content)...)

	output := fmt.Sprintf("AI Suggestions for '%s':\n", filePath)
	output += strings.Repeat("=", 50) + "\n\n"

	if len(suggestions) == 0 {
		output += "No suggestions found. The file looks good!\n"
	} else {
		for i, suggestion := range suggestions {
			output += fmt.Sprintf("%d. %s\n", i+1, suggestion)
		}
	}

	metadata := map[string]interface{}{
		"action":              "suggest",
		"file":                filePath,
		"suggestions_count":   len(suggestions),
		"ai_enabled":          aiSuggestions,
		"preserve_formatting": preserveFormatting,
		"file_type":           ext,
	}

	return Result{
		Output:   output,
		Metadata: metadata,
	}
}

// showDiff shows diff between current file and provided content
func (t *EditTool) showDiff(filePath, newContent string) Result {
	// Read current content
	data, err := os.ReadFile(filePath)
	if err != nil {
		return Result{Error: fmt.Errorf("failed to read file: %w", err)}
	}

	originalContent := string(data)
	diffOutput := t.generateDiff(originalContent, newContent)

	output := fmt.Sprintf("Diff for '%s':\n", filePath)
	output += strings.Repeat("=", 50) + "\n"
	output += diffOutput

	metadata := map[string]interface{}{
		"action":        "diff",
		"file":          filePath,
		"original_size": len(originalContent),
		"new_size":      len(newContent),
		"has_changes":   originalContent != newContent,
	}

	return Result{
		Output:   output,
		Metadata: metadata,
	}
}

// Helper functions

// createBackup creates a backup of the original file
func (t *EditTool) createBackup(filePath, content string) error {
	backupPath := filePath + ".backup." + time.Now().Format("20060102-150405")
	return os.WriteFile(backupPath, []byte(content), 0644)
}

// generateDiff generates a simple human-readable diff
func (t *EditTool) generateDiff(original, new string) string {
	if original == new {
		return "No changes detected."
	}

	originalLines := strings.Split(original, "\n")
	newLines := strings.Split(new, "\n")

	var diff strings.Builder
	diff.WriteString("--- Original\n")
	diff.WriteString("+++ Modified\n")

	maxLines := len(originalLines)
	if len(newLines) > maxLines {
		maxLines = len(newLines)
	}

	for i := 0; i < maxLines; i++ {
		var origLine, newLine string

		if i < len(originalLines) {
			origLine = originalLines[i]
		}
		if i < len(newLines) {
			newLine = newLines[i]
		}

		if origLine != newLine {
			if origLine != "" {
				diff.WriteString(fmt.Sprintf("-%s\n", origLine))
			}
			if newLine != "" {
				diff.WriteString(fmt.Sprintf("+%s\n", newLine))
			}
		}
	}

	return diff.String()
}

// Helper functions are now in tool.go

// isCodeFile checks if file extension indicates a code file
func isCodeFile(ext string) bool {
	codeExts := map[string]bool{
		".go": true, ".py": true, ".js": true, ".ts": true, ".java": true,
		".c": true, ".cpp": true, ".h": true, ".hpp": true, ".cs": true,
		".php": true, ".rb": true, ".rs": true, ".swift": true, ".kt": true,
		".scala": true, ".clj": true, ".hs": true, ".ml": true, ".fs": true,
	}
	return codeExts[ext]
}

// analyzeCode provides code-specific suggestions
func (t *EditTool) analyzeCode(content, ext string) []string {
	suggestions := []string{}
	lines := strings.Split(content, "\n")

	// Check for common code issues
	if strings.Contains(content, "TODO") || strings.Contains(content, "FIXME") {
		suggestions = append(suggestions, "Consider addressing TODO/FIXME comments")
	}

	if len(lines) > 500 {
		suggestions = append(suggestions, "File is quite large (>500 lines). Consider breaking it into smaller modules")
	}

	// Language-specific suggestions
	switch ext {
	case ".go":
		if !strings.Contains(content, "package ") {
			suggestions = append(suggestions, "Missing package declaration")
		}
		if strings.Contains(content, "fmt.Print") && !strings.Contains(content, "import \"fmt\"") {
			suggestions = append(suggestions, "Missing fmt import for print statements")
		}
	case ".py":
		if !strings.Contains(content, "#!/usr/bin/env python") && !strings.Contains(content, "# -*- coding:") {
			suggestions = append(suggestions, "Consider adding shebang and encoding declaration")
		}
	case ".js", ".ts":
		if strings.Contains(content, "var ") {
			suggestions = append(suggestions, "Consider using 'let' or 'const' instead of 'var'")
		}
	}

	return suggestions
}

// analyzeGeneral provides general file suggestions
func (t *EditTool) analyzeGeneral(content string) []string {
	suggestions := []string{}

	// Check for trailing whitespace
	lines := strings.Split(content, "\n")
	hasTrailingWhitespace := false
	for _, line := range lines {
		if len(line) > 0 && (line[len(line)-1] == ' ' || line[len(line)-1] == '\t') {
			hasTrailingWhitespace = true
			break
		}
	}
	if hasTrailingWhitespace {
		suggestions = append(suggestions, "Remove trailing whitespace")
	}

	// Check for inconsistent line endings
	if strings.Contains(content, "\r\n") && strings.Contains(content, "\n") {
		suggestions = append(suggestions, "Inconsistent line endings detected")
	}

	// Check for very long lines
	for i, line := range lines {
		if len(line) > 120 {
			suggestions = append(suggestions, fmt.Sprintf("Line %d is very long (%d chars). Consider breaking it up", i+1, len(line)))
			break
		}
	}

	return suggestions
}
