/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"fmt"
	"io"
	"os"
	"strings"
	"time"

	"github.com/sergi/go-diff/diffmatchpatch"
)

// DiffTool implements real-time diff viewing between files or content
type DiffTool struct {
	maxFileSize int64
}

// NewDiffTool creates a new diff tool
func NewDiffTool() *DiffTool {
	return &DiffTool{
		maxFileSize: 10 * 1024 * 1024, // 10MB
	}
}

// Name returns the tool name
func (t *DiffTool) Name() string {
	return "diff"
}

// Description returns the tool description
func (t *DiffTool) Description() string {
	return "View real-time diffs between files or content with various output formats"
}

// Parameters returns the tool parameter schema
func (t *DiffTool) Parameters() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"source": StringParameter("Source file path or content", true),
			"target": StringParameter("Target file path or content", true),
			"source_type": map[string]interface{}{
				"type":        "string",
				"description": "Type of source (file or content)",
				"enum":        []string{"file", "content"},
				"default":     "file",
			},
			"target_type": map[string]interface{}{
				"type":        "string",
				"description": "Type of target (file or content)",
				"enum":        []string{"file", "content"},
				"default":     "file",
			},
			"format": map[string]interface{}{
				"type":        "string",
				"description": "Output format for diff",
				"enum":        []string{"unified", "side-by-side", "context", "html", "json"},
				"default":     "unified",
			},
			"context_lines": IntParameter("Number of context lines to show", 0, 20),
			"ignore_whitespace": BoolParameter("Ignore whitespace differences", false),
			"ignore_case": BoolParameter("Ignore case differences", false),
			"word_diff": BoolParameter("Show word-level differences", false),
			"show_stats": BoolParameter("Show diff statistics", true),
			"color_output": BoolParameter("Use colored output (terminal)", true),
		},
		"required": []string{"source", "target"},
	}
}

// DiffStats represents statistics about the diff
type DiffStats struct {
	LinesAdded   int `json:"lines_added"`
	LinesRemoved int `json:"lines_removed"`
	LinesChanged int `json:"lines_changed"`
	TotalLines   int `json:"total_lines"`
	Similarity   float64 `json:"similarity"`
}

// DiffResult represents the complete diff operation result
type DiffResult struct {
	Source      string        `json:"source"`
	Target      string        `json:"target"`
	SourceType  string        `json:"source_type"`
	TargetType  string        `json:"target_type"`
	Format      string        `json:"format"`
	Diff        string        `json:"diff"`
	Stats       DiffStats     `json:"stats"`
	Duration    time.Duration `json:"duration"`
	HasChanges  bool          `json:"has_changes"`
}

// Execute performs the diff operation
func (t *DiffTool) Execute(ctx context.Context, args map[string]interface{}) Result {
	start := time.Now()
	
	// Parse arguments
	source := getStringArg(args, "source", "")
	target := getStringArg(args, "target", "")
	sourceType := getStringArg(args, "source_type", "file")
	targetType := getStringArg(args, "target_type", "file")
	format := getStringArg(args, "format", "unified")
	contextLines := getIntArg(args, "context_lines", 3)
	ignoreWhitespace := getBoolArg(args, "ignore_whitespace", false)
	ignoreCase := getBoolArg(args, "ignore_case", false)
	wordDiff := getBoolArg(args, "word_diff", false)
	showStats := getBoolArg(args, "show_stats", true)
	colorOutput := getBoolArg(args, "color_output", true)
	
	// Get source content
	sourceContent, err := t.getContent(source, sourceType)
	if err != nil {
		return Result{Error: fmt.Errorf("failed to get source content: %w", err)}
	}
	
	// Get target content
	targetContent, err := t.getContent(target, targetType)
	if err != nil {
		return Result{Error: fmt.Errorf("failed to get target content: %w", err)}
	}
	
	// Perform diff operation
	result, err := t.performDiff(sourceContent, targetContent, source, target, 
		sourceType, targetType, format, contextLines, ignoreWhitespace, 
		ignoreCase, wordDiff, showStats, colorOutput)
	if err != nil {
		return Result{Error: err}
	}
	
	result.Duration = time.Since(start)
	
	// Format output
	output := t.formatDiffResult(result)
	
	return Result{
		Output: output,
		Data:   result,
	}
}

// getContent retrieves content based on type (file or direct content)
func (t *DiffTool) getContent(input, inputType string) (string, error) {
	switch inputType {
	case "file":
		// Check file size
		info, err := os.Stat(input)
		if err != nil {
			return "", fmt.Errorf("failed to stat file %s: %w", input, err)
		}
		
		if info.Size() > t.maxFileSize {
			return "", fmt.Errorf("file %s is too large (%d bytes, max %d)", 
				input, info.Size(), t.maxFileSize)
		}
		
		// Read file content
		file, err := os.Open(input)
		if err != nil {
			return "", fmt.Errorf("failed to open file %s: %w", input, err)
		}
		defer file.Close()
		
		content, err := io.ReadAll(file)
		if err != nil {
			return "", fmt.Errorf("failed to read file %s: %w", input, err)
		}
		
		return string(content), nil
		
	case "content":
		return input, nil
		
	default:
		return "", fmt.Errorf("invalid input type: %s", inputType)
	}
}

// performDiff performs the actual diff operation
func (t *DiffTool) performDiff(sourceContent, targetContent, source, target, 
	sourceType, targetType, format string, contextLines int, ignoreWhitespace, 
	ignoreCase, wordDiff, showStats, colorOutput bool) (*DiffResult, error) {
	
	result := &DiffResult{
		Source:     source,
		Target:     target,
		SourceType: sourceType,
		TargetType: targetType,
		Format:     format,
	}
	
	// Preprocess content if needed
	processedSource := t.preprocessContent(sourceContent, ignoreWhitespace, ignoreCase)
	processedTarget := t.preprocessContent(targetContent, ignoreWhitespace, ignoreCase)
	
	// Check if there are any changes
	result.HasChanges = processedSource != processedTarget
	
	if !result.HasChanges {
		result.Diff = "No differences found."
		return result, nil
	}
	
	// Create diff using diffmatchpatch
	dmp := diffmatchpatch.New()
	
	var diff string
	var stats DiffStats
	
	if wordDiff {
		// Word-level diff
		diffs := dmp.DiffMain(processedSource, processedTarget, false)
		diffs = dmp.DiffCleanupSemantic(diffs)
		
		switch format {
		case "html":
			diff = dmp.DiffPrettyHtml(diffs)
		case "json":
			diff = t.formatDiffsAsJSON(diffs)
		default:
			diff = t.formatWordDiff(diffs, colorOutput)
		}
		
		stats = t.calculateWordStats(diffs)
		
	} else {
		// Line-level diff
		sourceLines := strings.Split(processedSource, "\n")
		targetLines := strings.Split(processedTarget, "\n")
		
		switch format {
		case "unified":
			diff = t.formatUnifiedDiff(sourceLines, targetLines, source, target, contextLines, colorOutput)
		case "side-by-side":
			diff = t.formatSideBySideDiff(sourceLines, targetLines, colorOutput)
		case "context":
			diff = t.formatContextDiff(sourceLines, targetLines, source, target, contextLines)
		case "html":
			diff = t.formatHTMLDiff(sourceLines, targetLines)
		case "json":
			diff = t.formatJSONDiff(sourceLines, targetLines)
		default:
			diff = t.formatUnifiedDiff(sourceLines, targetLines, source, target, contextLines, colorOutput)
		}
		
		stats = t.calculateLineStats(sourceLines, targetLines)
	}
	
	result.Diff = diff
	result.Stats = stats
	
	return result, nil
}

// preprocessContent applies preprocessing options to content
func (t *DiffTool) preprocessContent(content string, ignoreWhitespace, ignoreCase bool) string {
	processed := content
	
	if ignoreCase {
		processed = strings.ToLower(processed)
	}
	
	if ignoreWhitespace {
		// Normalize whitespace
		lines := strings.Split(processed, "\n")
		for i, line := range lines {
			lines[i] = strings.TrimSpace(line)
		}
		processed = strings.Join(lines, "\n")
	}
	
	return processed
}

// formatUnifiedDiff formats diff in unified format
func (t *DiffTool) formatUnifiedDiff(sourceLines, targetLines []string, source, target string, contextLines int, colorOutput bool) string {
	var result strings.Builder
	
	// Header
	result.WriteString(fmt.Sprintf("--- %s\n", source))
	result.WriteString(fmt.Sprintf("+++ %s\n", target))
	
	// Simple unified diff implementation
	dmp := diffmatchpatch.New()
	diffs := dmp.DiffMain(strings.Join(sourceLines, "\n"), strings.Join(targetLines, "\n"), false)
	
	for _, diff := range diffs {
		switch diff.Type {
		case diffmatchpatch.DiffDelete:
			lines := strings.Split(diff.Text, "\n")
			for _, line := range lines {
				if line != "" {
					if colorOutput {
						result.WriteString(fmt.Sprintf("\033[31m-%s\033[0m\n", line))
					} else {
						result.WriteString(fmt.Sprintf("-%s\n", line))
					}
				}
			}
		case diffmatchpatch.DiffInsert:
			lines := strings.Split(diff.Text, "\n")
			for _, line := range lines {
				if line != "" {
					if colorOutput {
						result.WriteString(fmt.Sprintf("\033[32m+%s\033[0m\n", line))
					} else {
						result.WriteString(fmt.Sprintf("+%s\n", line))
					}
				}
			}
		case diffmatchpatch.DiffEqual:
			lines := strings.Split(diff.Text, "\n")
			for _, line := range lines {
				if line != "" {
					result.WriteString(fmt.Sprintf(" %s\n", line))
				}
			}
		}
	}
	
	return result.String()
}

// formatSideBySideDiff formats diff in side-by-side format
func (t *DiffTool) formatSideBySideDiff(sourceLines, targetLines []string, colorOutput bool) string {
	var result strings.Builder
	
	maxLen := len(sourceLines)
	if len(targetLines) > maxLen {
		maxLen = len(targetLines)
	}
	
	result.WriteString("Source                          | Target\n")
	result.WriteString("--------------------------------|--------------------------------\n")
	
	for i := 0; i < maxLen; i++ {
		var sourceLine, targetLine string
		
		if i < len(sourceLines) {
			sourceLine = sourceLines[i]
		}
		if i < len(targetLines) {
			targetLine = targetLines[i]
		}
		
		// Truncate long lines
		if len(sourceLine) > 30 {
			sourceLine = sourceLine[:27] + "..."
		}
		if len(targetLine) > 30 {
			targetLine = targetLine[:27] + "..."
		}
		
		result.WriteString(fmt.Sprintf("%-30s | %-30s\n", sourceLine, targetLine))
	}
	
	return result.String()
}

// formatContextDiff formats diff in context format
func (t *DiffTool) formatContextDiff(sourceLines, targetLines []string, source, target string, contextLines int) string {
	var result strings.Builder
	
	result.WriteString(fmt.Sprintf("*** %s\n", source))
	result.WriteString(fmt.Sprintf("--- %s\n", target))
	
	// Simple context diff implementation
	result.WriteString("***************\n")
	
	for i, line := range sourceLines {
		result.WriteString(fmt.Sprintf("*** %d **** %s\n", i+1, line))
	}
	
	for i, line := range targetLines {
		result.WriteString(fmt.Sprintf("--- %d ---- %s\n", i+1, line))
	}
	
	return result.String()
}

// formatHTMLDiff formats diff as HTML
func (t *DiffTool) formatHTMLDiff(sourceLines, targetLines []string) string {
	dmp := diffmatchpatch.New()
	diffs := dmp.DiffMain(strings.Join(sourceLines, "\n"), strings.Join(targetLines, "\n"), false)
	return dmp.DiffPrettyHtml(diffs)
}

// formatJSONDiff formats diff as JSON
func (t *DiffTool) formatJSONDiff(sourceLines, targetLines []string) string {
	dmp := diffmatchpatch.New()
	diffs := dmp.DiffMain(strings.Join(sourceLines, "\n"), strings.Join(targetLines, "\n"), false)
	return t.formatDiffsAsJSON(diffs)
}

// formatWordDiff formats word-level diff
func (t *DiffTool) formatWordDiff(diffs []diffmatchpatch.Diff, colorOutput bool) string {
	var result strings.Builder
	
	for _, diff := range diffs {
		switch diff.Type {
		case diffmatchpatch.DiffDelete:
			if colorOutput {
				result.WriteString(fmt.Sprintf("\033[31m[-%s]\033[0m", diff.Text))
			} else {
				result.WriteString(fmt.Sprintf("[-%s]", diff.Text))
			}
		case diffmatchpatch.DiffInsert:
			if colorOutput {
				result.WriteString(fmt.Sprintf("\033[32m[+%s]\033[0m", diff.Text))
			} else {
				result.WriteString(fmt.Sprintf("[+%s]", diff.Text))
			}
		case diffmatchpatch.DiffEqual:
			result.WriteString(diff.Text)
		}
	}
	
	return result.String()
}

// formatDiffsAsJSON formats diffs as JSON
func (t *DiffTool) formatDiffsAsJSON(diffs []diffmatchpatch.Diff) string {
	var result strings.Builder
	result.WriteString("[\n")
	
	for i, diff := range diffs {
		if i > 0 {
			result.WriteString(",\n")
		}
		
		var operation string
		switch diff.Type {
		case diffmatchpatch.DiffDelete:
			operation = "delete"
		case diffmatchpatch.DiffInsert:
			operation = "insert"
		case diffmatchpatch.DiffEqual:
			operation = "equal"
		}
		
		result.WriteString(fmt.Sprintf(`  {"operation": "%s", "text": %q}`, operation, diff.Text))
	}
	
	result.WriteString("\n]")
	return result.String()
}

// calculateLineStats calculates statistics for line-level diff
func (t *DiffTool) calculateLineStats(sourceLines, targetLines []string) DiffStats {
	dmp := diffmatchpatch.New()
	diffs := dmp.DiffMain(strings.Join(sourceLines, "\n"), strings.Join(targetLines, "\n"), false)
	
	var added, removed int
	
	for _, diff := range diffs {
		lines := strings.Split(diff.Text, "\n")
		lineCount := len(lines)
		if lines[len(lines)-1] == "" {
			lineCount--
		}
		
		switch diff.Type {
		case diffmatchpatch.DiffDelete:
			removed += lineCount
		case diffmatchpatch.DiffInsert:
			added += lineCount
		}
	}
	
	totalLines := len(sourceLines) + len(targetLines)
	similarity := 1.0 - float64(added+removed)/float64(totalLines)
	
	return DiffStats{
		LinesAdded:   added,
		LinesRemoved: removed,
		LinesChanged: added + removed,
		TotalLines:   totalLines,
		Similarity:   similarity,
	}
}

// calculateWordStats calculates statistics for word-level diff
func (t *DiffTool) calculateWordStats(diffs []diffmatchpatch.Diff) DiffStats {
	var added, removed int
	var totalChars int
	
	for _, diff := range diffs {
		totalChars += len(diff.Text)
		switch diff.Type {
		case diffmatchpatch.DiffDelete:
			removed += len(strings.Fields(diff.Text))
		case diffmatchpatch.DiffInsert:
			added += len(strings.Fields(diff.Text))
		}
	}
	
	similarity := 1.0 - float64(added+removed)/float64(totalChars)
	
	return DiffStats{
		LinesAdded:   added,
		LinesRemoved: removed,
		LinesChanged: added + removed,
		TotalLines:   totalChars,
		Similarity:   similarity,
	}
}

// formatDiffResult formats the diff result for display
func (t *DiffTool) formatDiffResult(result *DiffResult) string {
	var output strings.Builder
	
	output.WriteString(fmt.Sprintf("📊 Diff Analysis Results\n"))
	output.WriteString(fmt.Sprintf("Source: %s (%s)\n", result.Source, result.SourceType))
	output.WriteString(fmt.Sprintf("Target: %s (%s)\n", result.Target, result.TargetType))
	output.WriteString(fmt.Sprintf("Format: %s | Duration: %v\n", result.Format, result.Duration))
	
	if !result.HasChanges {
		output.WriteString("✅ No differences found.\n")
		return output.String()
	}
	
	output.WriteString(fmt.Sprintf("Changes: +%d -%d | Similarity: %.1f%%\n\n", 
		result.Stats.LinesAdded, result.Stats.LinesRemoved, result.Stats.Similarity*100))
	
	output.WriteString("📝 Diff Output:\n")
	output.WriteString(result.Diff)
	
	return output.String()
}

// Validate validates the tool arguments
func (t *DiffTool) Validate(args map[string]interface{}) error {
	source := getStringArg(args, "source", "")
	if source == "" {
		return fmt.Errorf("source is required")
	}
	
	target := getStringArg(args, "target", "")
	if target == "" {
		return fmt.Errorf("target is required")
	}
	
	sourceType := getStringArg(args, "source_type", "file")
	if sourceType != "file" && sourceType != "content" {
		return fmt.Errorf("source_type must be 'file' or 'content'")
	}
	
	targetType := getStringArg(args, "target_type", "file")
	if targetType != "file" && targetType != "content" {
		return fmt.Errorf("target_type must be 'file' or 'content'")
	}
	
	format := getStringArg(args, "format", "unified")
	validFormats := []string{"unified", "side-by-side", "context", "html", "json"}
	valid := false
	for _, f := range validFormats {
		if format == f {
			valid = true
			break
		}
	}
	if !valid {
		return fmt.Errorf("format must be one of: %s", strings.Join(validFormats, ", "))
	}
	
	return nil
}

// SupportsParallel returns whether this tool supports parallel execution
func (t *DiffTool) SupportsParallel() bool {
	return true
}
