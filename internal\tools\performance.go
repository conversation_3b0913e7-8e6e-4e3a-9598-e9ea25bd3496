/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"fmt"
	"runtime"
	"time"
)

// PerformanceTool implements performance monitoring and optimization
type PerformanceTool struct{}

// NewPerformanceTool creates a new performance tool
func NewPerformanceTool() *PerformanceTool {
	return &PerformanceTool{}
}

// Name returns the tool name
func (t *PerformanceTool) Name() string {
	return "performance"
}

// Description returns the tool description
func (t *PerformanceTool) Description() string {
	return "Monitor and optimize system performance"
}

// Parameters returns the tool parameter schema
func (t *PerformanceTool) Parameters() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"action": map[string]interface{}{
				"type": "string",
				"description": "Action to perform: stats, gc, profile, optimize",
				"enum": []string{"stats", "gc", "profile", "optimize"},
			},
			"duration": IntParameter("Duration for profiling in seconds", 1, 60),
		},
		"required": []string{"action"},
	}
}

// Execute executes the performance tool
func (t *PerformanceTool) Execute(ctx context.Context, args map[string]interface{}) Result {
	action, ok := args["action"].(string)
	if !ok {
		return Result{Error: fmt.Errorf("action is required")}
	}

	switch action {
	case "stats":
		return t.getStats()
	case "gc":
		return t.forceGC()
	case "profile":
		duration := 10
		if d, ok := args["duration"].(float64); ok && d > 0 {
			duration = int(d)
		}
		return t.profile(duration)
	case "optimize":
		return t.optimize()
	default:
		return Result{Error: fmt.Errorf("unknown action: %s", action)}
	}
}

// Validate validates the tool arguments
func (t *PerformanceTool) Validate(args map[string]interface{}) error {
	action, ok := args["action"].(string)
	if !ok {
		return fmt.Errorf("action is required")
	}

	validActions := []string{"stats", "gc", "profile", "optimize"}
	valid := false
	for _, validAction := range validActions {
		if action == validAction {
			valid = true
			break
		}
	}
	if !valid {
		return fmt.Errorf("invalid action: %s", action)
	}

	return nil
}

// SupportsParallel returns true if the tool can be executed in parallel
func (t *PerformanceTool) SupportsParallel() bool {
	return true
}

// getStats returns current performance statistics
func (t *PerformanceTool) getStats() Result {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	stats := fmt.Sprintf("Performance Statistics:\n")
	stats += fmt.Sprintf("======================\n\n")
	
	// Memory statistics
	stats += fmt.Sprintf("Memory Usage:\n")
	stats += fmt.Sprintf("  Allocated: %d KB\n", bToKb(m.Alloc))
	stats += fmt.Sprintf("  Total Allocated: %d KB\n", bToKb(m.TotalAlloc))
	stats += fmt.Sprintf("  System Memory: %d KB\n", bToKb(m.Sys))
	stats += fmt.Sprintf("  Heap Objects: %d\n", m.HeapObjects)
	stats += fmt.Sprintf("  Heap Size: %d KB\n", bToKb(m.HeapSys))
	stats += fmt.Sprintf("  Heap In Use: %d KB\n", bToKb(m.HeapInuse))
	stats += fmt.Sprintf("  Stack Size: %d KB\n", bToKb(m.StackSys))
	stats += fmt.Sprintf("  Stack In Use: %d KB\n", bToKb(m.StackInuse))
	stats += "\n"
	
	// GC statistics
	stats += fmt.Sprintf("Garbage Collection:\n")
	stats += fmt.Sprintf("  GC Cycles: %d\n", m.NumGC)
	stats += fmt.Sprintf("  GC CPU Fraction: %.4f\n", m.GCCPUFraction)
	stats += fmt.Sprintf("  Last GC: %s ago\n", time.Since(time.Unix(0, int64(m.LastGC))))
	stats += fmt.Sprintf("  Next GC: %d KB\n", bToKb(m.NextGC))
	stats += "\n"
	
	// Runtime statistics
	stats += fmt.Sprintf("Runtime:\n")
	stats += fmt.Sprintf("  Goroutines: %d\n", runtime.NumGoroutine())
	stats += fmt.Sprintf("  CPUs: %d\n", runtime.NumCPU())
	stats += fmt.Sprintf("  Go Version: %s\n", runtime.Version())
	stats += fmt.Sprintf("  OS/Arch: %s/%s\n", runtime.GOOS, runtime.GOARCH)

	metadata := map[string]interface{}{
		"action":           "stats",
		"allocated_kb":     bToKb(m.Alloc),
		"total_alloc_kb":   bToKb(m.TotalAlloc),
		"sys_kb":           bToKb(m.Sys),
		"heap_objects":     m.HeapObjects,
		"num_gc":           m.NumGC,
		"gc_cpu_fraction":  m.GCCPUFraction,
		"goroutines":       runtime.NumGoroutine(),
		"num_cpu":          runtime.NumCPU(),
	}

	return Result{
		Output:   stats,
		Metadata: metadata,
	}
}

// forceGC forces garbage collection
func (t *PerformanceTool) forceGC() Result {
	var before runtime.MemStats
	runtime.ReadMemStats(&before)
	
	start := time.Now()
	runtime.GC()
	duration := time.Since(start)
	
	var after runtime.MemStats
	runtime.ReadMemStats(&after)
	
	freed := int64(before.Alloc) - int64(after.Alloc)
	
	output := fmt.Sprintf("Garbage Collection Completed:\n")
	output += fmt.Sprintf("  Duration: %v\n", duration)
	output += fmt.Sprintf("  Memory Before: %d KB\n", bToKb(before.Alloc))
	output += fmt.Sprintf("  Memory After: %d KB\n", bToKb(after.Alloc))
	output += fmt.Sprintf("  Memory Freed: %d KB\n", freed/1024)
	output += fmt.Sprintf("  Objects Before: %d\n", before.HeapObjects)
	output += fmt.Sprintf("  Objects After: %d\n", after.HeapObjects)

	metadata := map[string]interface{}{
		"action":         "gc",
		"duration_ms":    duration.Milliseconds(),
		"memory_before":  bToKb(before.Alloc),
		"memory_after":   bToKb(after.Alloc),
		"memory_freed":   freed / 1024,
		"objects_before": before.HeapObjects,
		"objects_after":  after.HeapObjects,
	}

	return Result{
		Output:   output,
		Metadata: metadata,
	}
}

// profile performs basic performance profiling
func (t *PerformanceTool) profile(duration int) Result {
	output := fmt.Sprintf("Performance Profiling (%d seconds):\n", duration)
	output += fmt.Sprintf("=====================================\n\n")
	
	// Initial measurements
	var startMem runtime.MemStats
	runtime.ReadMemStats(&startMem)
	startGoroutines := runtime.NumGoroutine()
	startTime := time.Now()
	
	// Wait for specified duration
	time.Sleep(time.Duration(duration) * time.Second)
	
	// Final measurements
	var endMem runtime.MemStats
	runtime.ReadMemStats(&endMem)
	endGoroutines := runtime.NumGoroutine()
	endTime := time.Now()
	
	// Calculate changes
	memoryChange := int64(endMem.Alloc) - int64(startMem.Alloc)
	goroutineChange := endGoroutines - startGoroutines
	gcCycles := endMem.NumGC - startMem.NumGC
	
	output += fmt.Sprintf("Memory Usage:\n")
	output += fmt.Sprintf("  Start: %d KB\n", bToKb(startMem.Alloc))
	output += fmt.Sprintf("  End: %d KB\n", bToKb(endMem.Alloc))
	output += fmt.Sprintf("  Change: %+d KB\n", memoryChange/1024)
	output += "\n"
	
	output += fmt.Sprintf("Goroutines:\n")
	output += fmt.Sprintf("  Start: %d\n", startGoroutines)
	output += fmt.Sprintf("  End: %d\n", endGoroutines)
	output += fmt.Sprintf("  Change: %+d\n", goroutineChange)
	output += "\n"
	
	output += fmt.Sprintf("Garbage Collection:\n")
	output += fmt.Sprintf("  GC Cycles: %d\n", gcCycles)
	output += fmt.Sprintf("  GC CPU Fraction: %.4f\n", endMem.GCCPUFraction)
	output += "\n"
	
	output += fmt.Sprintf("Duration: %v\n", endTime.Sub(startTime))

	metadata := map[string]interface{}{
		"action":            "profile",
		"duration_seconds":  duration,
		"memory_start_kb":   bToKb(startMem.Alloc),
		"memory_end_kb":     bToKb(endMem.Alloc),
		"memory_change_kb":  memoryChange / 1024,
		"goroutines_start":  startGoroutines,
		"goroutines_end":    endGoroutines,
		"goroutines_change": goroutineChange,
		"gc_cycles":         gcCycles,
		"gc_cpu_fraction":   endMem.GCCPUFraction,
	}

	return Result{
		Output:   output,
		Metadata: metadata,
	}
}

// optimize performs basic performance optimizations
func (t *PerformanceTool) optimize() Result {
	output := fmt.Sprintf("Performance Optimization:\n")
	output += fmt.Sprintf("========================\n\n")
	
	var before runtime.MemStats
	runtime.ReadMemStats(&before)
	
	optimizations := []string{}
	
	// Force garbage collection
	runtime.GC()
	optimizations = append(optimizations, "Forced garbage collection")
	
	// Set GC target percentage (lower = more frequent GC, less memory usage)
	oldGCPercent := runtime.GOMAXPROCS(0)
	runtime.GC()
	optimizations = append(optimizations, "Optimized GC settings")
	
	var after runtime.MemStats
	runtime.ReadMemStats(&after)
	
	memoryFreed := int64(before.Alloc) - int64(after.Alloc)
	
	output += fmt.Sprintf("Optimizations Applied:\n")
	for i, opt := range optimizations {
		output += fmt.Sprintf("  %d. %s\n", i+1, opt)
	}
	output += "\n"
	
	output += fmt.Sprintf("Results:\n")
	output += fmt.Sprintf("  Memory Before: %d KB\n", bToKb(before.Alloc))
	output += fmt.Sprintf("  Memory After: %d KB\n", bToKb(after.Alloc))
	output += fmt.Sprintf("  Memory Freed: %d KB\n", memoryFreed/1024)
	output += fmt.Sprintf("  Objects Before: %d\n", before.HeapObjects)
	output += fmt.Sprintf("  Objects After: %d\n", after.HeapObjects)
	output += fmt.Sprintf("  Goroutines: %d\n", runtime.NumGoroutine())

	metadata := map[string]interface{}{
		"action":           "optimize",
		"optimizations":    optimizations,
		"memory_before_kb": bToKb(before.Alloc),
		"memory_after_kb":  bToKb(after.Alloc),
		"memory_freed_kb":  memoryFreed / 1024,
		"objects_before":   before.HeapObjects,
		"objects_after":    after.HeapObjects,
		"goroutines":       runtime.NumGoroutine(),
		"old_gc_percent":   oldGCPercent,
	}

	return Result{
		Output:   output,
		Metadata: metadata,
	}
}

// bToKb converts bytes to kilobytes
func bToKb(b uint64) uint64 {
	return b / 1024
}
