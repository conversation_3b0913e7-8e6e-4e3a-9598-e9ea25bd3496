/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tasks

import (
	"context"
	"fmt"
	"strings"

	"arien/internal/core"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/charmbracelet/log"
)

// TaskApp handles the task management interface
type TaskApp struct {
	engine *core.Engine
	logger *log.Logger
}

// taskModel represents the task UI model
type taskModel struct {
	engine *core.Engine
	logger *log.Logger
	tasks  []core.Task
	cursor int
	mode   taskMode
	input  string
	err    error
}

type taskMode int

const (
	modeList taskMode = iota
	modeCreate
	modeEdit
)

// NewTaskApp creates a new task app
func NewTaskApp(engine *core.Engine, logger *log.Logger) *TaskApp {
	return &TaskApp{
		engine: engine,
		logger: logger,
	}
}

// Run starts the task management interface
func (t *TaskApp) Run(ctx context.Context) error {
	model := taskModel{
		engine: t.engine,
		logger: t.logger,
		mode:   modeList,
	}

	// Load initial tasks
	model.loadTasks()

	p := tea.NewProgram(model, tea.WithAltScreen())
	
	if _, err := p.Run(); err != nil {
		return fmt.Errorf("task UI failed: %w", err)
	}

	return nil
}

// Init initializes the model
func (m taskModel) Init() tea.Cmd {
	return nil
}

// Update handles messages
func (m taskModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch m.mode {
		case modeList:
			return m.updateList(msg)
		case modeCreate:
			return m.updateCreate(msg)
		case modeEdit:
			return m.updateEdit(msg)
		}
	}

	return m, nil
}

// updateList handles list mode updates
func (m taskModel) updateList(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch msg.String() {
	case "ctrl+c", "q":
		return m, tea.Quit
	case "up", "k":
		if m.cursor > 0 {
			m.cursor--
		}
	case "down", "j":
		if m.cursor < len(m.tasks)-1 {
			m.cursor++
		}
	case "n":
		m.mode = modeCreate
		m.input = ""
	case "e":
		if len(m.tasks) > 0 {
			m.mode = modeEdit
			m.input = m.tasks[m.cursor].Name
		}
	case "d":
		if len(m.tasks) > 0 {
			m.deleteCurrentTask()
		}
	case "c":
		if len(m.tasks) > 0 {
			m.toggleTaskComplete()
		}
	case "r":
		m.loadTasks()
	}

	return m, nil
}

// updateCreate handles create mode updates
func (m taskModel) updateCreate(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch msg.String() {
	case "ctrl+c", "esc":
		m.mode = modeList
		m.input = ""
	case "enter":
		if m.input != "" {
			m.createTask()
			m.mode = modeList
			m.input = ""
		}
	case "backspace":
		if len(m.input) > 0 {
			m.input = m.input[:len(m.input)-1]
		}
	default:
		if len(msg.String()) == 1 {
			m.input += msg.String()
		}
	}

	return m, nil
}

// updateEdit handles edit mode updates
func (m taskModel) updateEdit(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch msg.String() {
	case "ctrl+c", "esc":
		m.mode = modeList
		m.input = ""
	case "enter":
		if m.input != "" {
			m.updateCurrentTask()
			m.mode = modeList
			m.input = ""
		}
	case "backspace":
		if len(m.input) > 0 {
			m.input = m.input[:len(m.input)-1]
		}
	default:
		if len(msg.String()) == 1 {
			m.input += msg.String()
		}
	}

	return m, nil
}

// View renders the UI
func (m taskModel) View() string {
	switch m.mode {
	case modeList:
		return m.renderList()
	case modeCreate:
		return m.renderCreate()
	case modeEdit:
		return m.renderEdit()
	default:
		return "Unknown mode"
	}
}

// renderList renders the task list
func (m taskModel) renderList() string {
	var s strings.Builder

	s.WriteString(headerStyle.Render("📋 Task Management"))
	s.WriteString("\n\n")

	if len(m.tasks) == 0 {
		s.WriteString(emptyStyle.Render("No tasks yet. Press 'n' to create your first task."))
	} else {
		for i, task := range m.tasks {
			cursor := " "
			if m.cursor == i {
				cursor = ">"
			}

			status := m.getStatusIcon(task.Status)
			priority := m.getPriorityIcon(task.Priority)
			
			line := fmt.Sprintf("%s %s %s %s", cursor, status, priority, task.Name)
			
			if m.cursor == i {
				s.WriteString(selectedStyle.Render(line))
			} else {
				s.WriteString(line)
			}
			s.WriteString("\n")
		}
	}

	s.WriteString("\n")
	s.WriteString(helpStyle.Render("n: new • e: edit • d: delete • c: toggle complete • r: refresh • q: quit"))

	if m.err != nil {
		s.WriteString("\n\n")
		s.WriteString(errorStyle.Render(fmt.Sprintf("Error: %v", m.err)))
		m.err = nil
	}

	return s.String()
}

// renderCreate renders the create task screen
func (m taskModel) renderCreate() string {
	var s strings.Builder

	s.WriteString(headerStyle.Render("📝 Create New Task"))
	s.WriteString("\n\n")
	s.WriteString("Task name:\n")
	s.WriteString(inputStyle.Render(m.input + "█"))
	s.WriteString("\n\n")
	s.WriteString(helpStyle.Render("enter: create • esc: cancel"))

	return s.String()
}

// renderEdit renders the edit task screen
func (m taskModel) renderEdit() string {
	var s strings.Builder

	s.WriteString(headerStyle.Render("✏️ Edit Task"))
	s.WriteString("\n\n")
	s.WriteString("Task name:\n")
	s.WriteString(inputStyle.Render(m.input + "█"))
	s.WriteString("\n\n")
	s.WriteString(helpStyle.Render("enter: save • esc: cancel"))

	return s.String()
}

// loadTasks loads tasks from the engine
func (m *taskModel) loadTasks() {
	m.tasks = m.engine.Tasks().ListTasks("", "")
}

// createTask creates a new task
func (m *taskModel) createTask() {
	_, err := m.engine.Tasks().CreateTask(m.input, "", core.TaskPriorityMedium)
	if err != nil {
		m.err = err
		return
	}
	m.loadTasks()
}

// updateCurrentTask updates the current task
func (m *taskModel) updateCurrentTask() {
	if m.cursor >= len(m.tasks) {
		return
	}

	task := m.tasks[m.cursor]
	updates := map[string]interface{}{
		"name": m.input,
	}

	err := m.engine.Tasks().UpdateTask(task.ID, updates)
	if err != nil {
		m.err = err
		return
	}
	m.loadTasks()
}

// deleteCurrentTask deletes the current task
func (m *taskModel) deleteCurrentTask() {
	if m.cursor >= len(m.tasks) {
		return
	}

	task := m.tasks[m.cursor]
	err := m.engine.Tasks().DeleteTask(task.ID)
	if err != nil {
		m.err = err
		return
	}

	m.loadTasks()
	if m.cursor >= len(m.tasks) && len(m.tasks) > 0 {
		m.cursor = len(m.tasks) - 1
	}
}

// toggleTaskComplete toggles task completion status
func (m *taskModel) toggleTaskComplete() {
	if m.cursor >= len(m.tasks) {
		return
	}

	task := m.tasks[m.cursor]
	newStatus := core.TaskStatusCompleted
	if task.Status == core.TaskStatusCompleted {
		newStatus = core.TaskStatusNotStarted
	}

	updates := map[string]interface{}{
		"status": string(newStatus),
	}

	err := m.engine.Tasks().UpdateTask(task.ID, updates)
	if err != nil {
		m.err = err
		return
	}
	m.loadTasks()
}

// getStatusIcon returns an icon for task status
func (m *taskModel) getStatusIcon(status core.TaskStatus) string {
	switch status {
	case core.TaskStatusNotStarted:
		return "⭕"
	case core.TaskStatusInProgress:
		return "🔄"
	case core.TaskStatusCompleted:
		return "✅"
	case core.TaskStatusCancelled:
		return "❌"
	case core.TaskStatusBlocked:
		return "🚫"
	default:
		return "❓"
	}
}

// getPriorityIcon returns an icon for task priority
func (m *taskModel) getPriorityIcon(priority core.TaskPriority) string {
	switch priority {
	case core.TaskPriorityLow:
		return "🟢"
	case core.TaskPriorityMedium:
		return "🟡"
	case core.TaskPriorityHigh:
		return "🟠"
	case core.TaskPriorityUrgent:
		return "🔴"
	default:
		return "⚪"
	}
}

// Styles
var (
	headerStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#7C3AED")).
			Bold(true).
			Padding(1, 2).
			Border(lipgloss.RoundedBorder()).
			BorderForeground(lipgloss.Color("#7C3AED"))

	selectedStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#7C3AED")).
			Bold(true)

	emptyStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#6B7280")).
			Italic(true)

	inputStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#FFFFFF")).
			Background(lipgloss.Color("#1F2937")).
			Padding(0, 1).
			Width(60)

	helpStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#6B7280")).
			Italic(true)

	errorStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#EF4444")).
			Bold(true)
)
