/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"arien/internal/utils"
)

func TestNewManager(t *testing.T) {
	logger := utils.NewLogger()
	
	manager, err := NewManager(logger)
	if err != nil {
		t.Fatalf("Failed to create tool manager: %v", err)
	}

	if manager == nil {
		t.Fatal("Manager is nil")
	}

	tools := manager.GetTools()
	if len(tools) == 0 {
		t.<PERSON><PERSON>("No tools registered")
	}

	// Check that essential tools are registered
	essentialTools := []string{"ls", "read", "write", "shell", "search", "memory", "tasks"}
	for _, toolName := range essentialTools {
		if _, exists := tools[toolName]; !exists {
			t.<PERSON>rf("Essential tool '%s' not registered", toolName)
		}
	}
}

func TestLsTool(t *testing.T) {
	logger := utils.NewLogger()
	manager, _ := NewManager(logger)
	
	ctx := context.Background()
	
	// Test basic ls
	result := manager.ExecuteTool(ctx, "ls", map[string]interface{}{
		"path": ".",
	})
	
	if result.Error != nil {
		t.Fatalf("ls tool failed: %v", result.Error)
	}
	
	if result.Output == "" {
		t.Fatal("ls tool returned empty output")
	}
	
	t.Logf("ls output: %s", result.Output)
}

func TestReadWriteTools(t *testing.T) {
	logger := utils.NewLogger()
	manager, _ := NewManager(logger)
	
	ctx := context.Background()
	
	// Create a temporary file for testing
	tempDir := t.TempDir()
	testFile := filepath.Join(tempDir, "test.txt")
	testContent := "Hello, Arien!\nThis is a test file."
	
	// Test write tool
	writeResult := manager.ExecuteTool(ctx, "write", map[string]interface{}{
		"file":    testFile,
		"content": testContent,
		"mode":    "create",
	})
	
	if writeResult.Error != nil {
		t.Fatalf("write tool failed: %v", writeResult.Error)
	}
	
	// Verify file was created
	if _, err := os.Stat(testFile); os.IsNotExist(err) {
		t.Fatal("File was not created")
	}
	
	// Test read tool
	readResult := manager.ExecuteTool(ctx, "read", map[string]interface{}{
		"file": testFile,
	})
	
	if readResult.Error != nil {
		t.Fatalf("read tool failed: %v", readResult.Error)
	}
	
	if !strings.Contains(readResult.Output, "Hello, Arien!") {
		t.Fatalf("read tool output doesn't contain expected content. Got: %s", readResult.Output)
	}
}

func TestShellTool(t *testing.T) {
	logger := utils.NewLogger()
	manager, _ := NewManager(logger)
	
	ctx := context.Background()
	
	// Test a simple shell command
	result := manager.ExecuteTool(ctx, "shell", map[string]interface{}{
		"command": "echo Hello World",
		"timeout": 5,
	})
	
	if result.Error != nil {
		t.Fatalf("shell tool failed: %v", result.Error)
	}
	
	if !strings.Contains(strings.ReplaceAll(result.Output, "\r\n", "\n"), "Hello World") {
		t.Fatalf("shell tool output doesn't contain expected text: %s", result.Output)
	}
}

func TestSearchTool(t *testing.T) {
	logger := utils.NewLogger()
	manager, _ := NewManager(logger)
	
	ctx := context.Background()
	
	// Test search tool
	result := manager.ExecuteTool(ctx, "search", map[string]interface{}{
		"query":          "test",
		"path":           ".",
		"content_search": false,
		"max_results":    5,
	})
	
	if result.Error != nil {
		t.Fatalf("search tool failed: %v", result.Error)
	}
	
	if result.Output == "" {
		t.Fatal("search tool returned empty output")
	}
}

func TestMemoryTool(t *testing.T) {
	logger := utils.NewLogger()
	manager, _ := NewManager(logger)
	
	ctx := context.Background()
	
	// Test memory save
	saveResult := manager.ExecuteTool(ctx, "memory", map[string]interface{}{
		"action":  "save",
		"content": "This is a test memory",
	})
	
	if saveResult.Error != nil {
		t.Fatalf("memory save failed: %v", saveResult.Error)
	}
	
	// Test memory recall
	recallResult := manager.ExecuteTool(ctx, "memory", map[string]interface{}{
		"action": "recall",
		"query":  "test",
	})
	
	if recallResult.Error != nil {
		t.Fatalf("memory recall failed: %v", recallResult.Error)
	}
}

func TestTaskTool(t *testing.T) {
	logger := utils.NewLogger()
	manager, _ := NewManager(logger)
	
	ctx := context.Background()
	
	// Test task creation
	createResult := manager.ExecuteTool(ctx, "tasks", map[string]interface{}{
		"action":      "create",
		"name":        "Test Task",
		"description": "This is a test task",
		"priority":    "high",
	})
	
	if createResult.Error != nil {
		t.Fatalf("task create failed: %v", createResult.Error)
	}
	
	// Test task listing
	listResult := manager.ExecuteTool(ctx, "tasks", map[string]interface{}{
		"action": "list",
	})
	
	if listResult.Error != nil {
		t.Fatalf("task list failed: %v", listResult.Error)
	}
	
	// Test task stats
	statsResult := manager.ExecuteTool(ctx, "tasks", map[string]interface{}{
		"action": "stats",
	})
	
	if statsResult.Error != nil {
		t.Fatalf("task stats failed: %v", statsResult.Error)
	}
}

func TestWebTools(t *testing.T) {
	logger := utils.NewLogger()
	manager, _ := NewManager(logger)
	
	ctx := context.Background()
	
	// Test web search (placeholder)
	searchResult := manager.ExecuteTool(ctx, "web-search", map[string]interface{}{
		"query":       "golang testing",
		"num_results": 3,
	})
	
	if searchResult.Error != nil {
		t.Fatalf("web search failed: %v", searchResult.Error)
	}
	
	if !strings.Contains(searchResult.Output, "golang testing") {
		t.Fatal("web search output doesn't contain query")
	}
	
	// Test web fetch (will fail for invalid URL, but tests validation)
	fetchResult := manager.ExecuteTool(ctx, "web-fetch", map[string]interface{}{
		"url":    "https://httpbin.org/get",
		"format": "text",
		"timeout": 10,
	})
	
	// This might fail due to network issues, so we just check it doesn't panic
	t.Logf("Web fetch result: error=%v", fetchResult.Error)
}

func TestToolValidation(t *testing.T) {
	logger := utils.NewLogger()
	manager, _ := NewManager(logger)
	
	ctx := context.Background()
	
	// Test invalid tool name
	result := manager.ExecuteTool(ctx, "nonexistent", map[string]interface{}{})
	if result.Error == nil {
		t.Fatal("Expected error for nonexistent tool")
	}
	
	// Test invalid arguments
	result = manager.ExecuteTool(ctx, "read", map[string]interface{}{
		"file": "", // Empty file path should fail validation
	})
	if result.Error == nil {
		t.Fatal("Expected error for invalid arguments")
	}
}

func TestToolParallelExecution(t *testing.T) {
	logger := utils.NewLogger()
	manager, _ := NewManager(logger)
	
	ctx := context.Background()
	
	// Test parallel execution of multiple tools
	toolCalls := []ToolCall{
		{Name: "ls", Args: map[string]interface{}{"path": "."}},
		{Name: "memory", Args: map[string]interface{}{"action": "recall"}},
		{Name: "tasks", Args: map[string]interface{}{"action": "stats"}},
	}
	
	start := time.Now()
	results := manager.ExecuteToolsParallel(ctx, toolCalls)
	duration := time.Since(start)
	
	if len(results) != len(toolCalls) {
		t.Fatalf("Expected %d results, got %d", len(toolCalls), len(results))
	}
	
	// Parallel execution should be faster than sequential
	t.Logf("Parallel execution took: %v", duration)
	
	for i, result := range results {
		if result.Error != nil {
			t.Logf("Tool %s failed: %v", toolCalls[i].Name, result.Error)
		}
	}
}
