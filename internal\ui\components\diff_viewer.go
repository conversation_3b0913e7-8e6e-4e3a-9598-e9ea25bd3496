/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package components

import (
	"fmt"
	"strings"

	"github.com/charmbracelet/bubbles/viewport"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// DiffViewer represents a real-time diff viewer component
type DiffViewer struct {
	viewport        viewport.Model
	originalText    string
	newText         string
	width           int
	height          int
	focused         bool
	showLineNumbers bool
	theme           DiffTheme
}

// DiffTheme defines colors for diff display
type DiffTheme struct {
	AddedBg     lipgloss.Color
	AddedFg     lipgloss.Color
	DeletedBg   lipgloss.Color
	DeletedFg   lipgloss.Color
	UnchangedFg lipgloss.Color
	LineNumberFg lipgloss.Color
	BorderColor lipgloss.Color
}

// DefaultDiffTheme returns the default diff theme
func DefaultDiffTheme() DiffTheme {
	return DiffTheme{
		AddedBg:      lipgloss.Color("#22c55e"),
		AddedFg:      lipgloss.Color("#ffffff"),
		DeletedBg:    lipgloss.Color("#ef4444"),
		DeletedFg:    lipgloss.Color("#ffffff"),
		UnchangedFg:  lipgloss.Color("#6b7280"),
		LineNumberFg: lipgloss.Color("#9ca3af"),
		BorderColor:  lipgloss.Color("#374151"),
	}
}

// DarkDiffTheme returns a dark diff theme
func DarkDiffTheme() DiffTheme {
	return DiffTheme{
		AddedBg:      lipgloss.Color("#16a34a"),
		AddedFg:      lipgloss.Color("#ffffff"),
		DeletedBg:    lipgloss.Color("#dc2626"),
		DeletedFg:    lipgloss.Color("#ffffff"),
		UnchangedFg:  lipgloss.Color("#9ca3af"),
		LineNumberFg: lipgloss.Color("#6b7280"),
		BorderColor:  lipgloss.Color("#4b5563"),
	}
}

// NewDiffViewer creates a new diff viewer
func NewDiffViewer(originalText, newText string, width, height int) *DiffViewer {
	dv := &DiffViewer{
		originalText:    originalText,
		newText:         newText,
		width:           width,
		height:          height,
		showLineNumbers: true,
		theme:           DefaultDiffTheme(),
	}

	dv.viewport = viewport.New(width-4, height-4)
	dv.updateContent()

	return dv
}

// SetTheme sets the diff viewer theme
func (dv *DiffViewer) SetTheme(theme DiffTheme) {
	dv.theme = theme
	dv.updateContent()
}

// SetSize updates the diff viewer size
func (dv *DiffViewer) SetSize(width, height int) {
	dv.width = width
	dv.height = height
	dv.viewport.Width = width - 4
	dv.viewport.Height = height - 4
	dv.updateContent()
}

// SetTexts updates the texts to compare
func (dv *DiffViewer) SetTexts(originalText, newText string) {
	dv.originalText = originalText
	dv.newText = newText
	dv.updateContent()
}

// SetFocus sets the focus state
func (dv *DiffViewer) SetFocus(focused bool) {
	dv.focused = focused
}

// ToggleLineNumbers toggles line number display
func (dv *DiffViewer) ToggleLineNumbers() {
	dv.showLineNumbers = !dv.showLineNumbers
	dv.updateContent()
}

// computeDiffs is no longer needed - using simple diff

// updateContent updates the viewport content with formatted diffs
func (dv *DiffViewer) updateContent() {
	content := dv.formatDiffs()
	dv.viewport.SetContent(content)
}

// formatDiffs formats the diffs for display using simple line-by-line comparison
func (dv *DiffViewer) formatDiffs() string {
	if dv.originalText == dv.newText {
		return "No differences found."
	}

	originalLines := strings.Split(dv.originalText, "\n")
	newLines := strings.Split(dv.newText, "\n")

	var result strings.Builder

	// Styles
	addedStyle := lipgloss.NewStyle().
		Background(dv.theme.AddedBg).
		Foreground(dv.theme.AddedFg).
		Bold(true)

	deletedStyle := lipgloss.NewStyle().
		Background(dv.theme.DeletedBg).
		Foreground(dv.theme.DeletedFg).
		Bold(true)

	unchangedStyle := lipgloss.NewStyle().
		Foreground(dv.theme.UnchangedFg)

	lineNumberStyle := lipgloss.NewStyle().
		Foreground(dv.theme.LineNumberFg).
		Width(4).
		Align(lipgloss.Right)

	maxLines := len(originalLines)
	if len(newLines) > maxLines {
		maxLines = len(newLines)
	}

	for i := 0; i < maxLines; i++ {
		var origLine, newLine string

		if i < len(originalLines) {
			origLine = originalLines[i]
		}
		if i < len(newLines) {
			newLine = newLines[i]
		}

		linePrefix := ""
		if dv.showLineNumbers {
			linePrefix = lineNumberStyle.Render(fmt.Sprintf("%d", i+1)) + " "
		}

		if origLine != newLine {
			if origLine != "" {
				styledLine := deletedStyle.Render("- " + origLine)
				result.WriteString(linePrefix + styledLine + "\n")
			}
			if newLine != "" {
				styledLine := addedStyle.Render("+ " + newLine)
				result.WriteString(linePrefix + styledLine + "\n")
			}
		} else {
			styledLine := unchangedStyle.Render("  " + origLine)
			result.WriteString(linePrefix + styledLine + "\n")
		}
	}

	return result.String()
}

// Update handles tea.Msg updates
func (dv *DiffViewer) Update(msg tea.Msg) (*DiffViewer, tea.Cmd) {
	var cmd tea.Cmd
	
	switch msg := msg.(type) {
	case tea.KeyMsg:
		if dv.focused {
			switch msg.String() {
			case "l":
				dv.ToggleLineNumbers()
				return dv, nil
			}
		}
	}
	
	dv.viewport, cmd = dv.viewport.Update(msg)
	return dv, cmd
}

// View renders the diff viewer
func (dv *DiffViewer) View() string {
	borderStyle := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(dv.theme.BorderColor).
		Width(dv.width).
		Height(dv.height)
	
	if dv.focused {
		borderStyle = borderStyle.BorderForeground(lipgloss.Color("#3b82f6"))
	}
	
	// Header
	header := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("#ffffff")).
		Background(lipgloss.Color("#374151")).
		Width(dv.width - 4).
		Align(lipgloss.Center).
		Render("Diff Viewer")
	
	// Footer with controls
	footer := lipgloss.NewStyle().
		Foreground(dv.theme.LineNumberFg).
		Width(dv.width - 4).
		Align(lipgloss.Center).
		Render("Press 'l' to toggle line numbers • ↑/↓ to scroll")
	
	content := lipgloss.JoinVertical(
		lipgloss.Left,
		header,
		dv.viewport.View(),
		footer,
	)
	
	return borderStyle.Render(content)
}

// GetStats returns diff statistics
func (dv *DiffViewer) GetStats() DiffStats {
	originalLines := strings.Split(dv.originalText, "\n")
	newLines := strings.Split(dv.newText, "\n")

	stats := DiffStats{
		AddedLines:    0,
		DeletedLines:  0,
		UnchangedLines: 0,
		AddedChars:    0,
		DeletedChars:  0,
	}

	maxLines := len(originalLines)
	if len(newLines) > maxLines {
		maxLines = len(newLines)
	}

	for i := 0; i < maxLines; i++ {
		var origLine, newLine string

		if i < len(originalLines) {
			origLine = originalLines[i]
		}
		if i < len(newLines) {
			newLine = newLines[i]
		}

		if origLine != newLine {
			if origLine != "" && newLine == "" {
				stats.DeletedLines++
				stats.DeletedChars += len(origLine)
			} else if origLine == "" && newLine != "" {
				stats.AddedLines++
				stats.AddedChars += len(newLine)
			} else {
				// Line modified
				stats.DeletedLines++
				stats.AddedLines++
				stats.DeletedChars += len(origLine)
				stats.AddedChars += len(newLine)
			}
		} else {
			stats.UnchangedLines++
		}
	}

	stats.TotalLines = stats.AddedLines + stats.DeletedLines + stats.UnchangedLines
	return stats
}

// DiffStats represents diff statistics
type DiffStats struct {
	AddedLines    int
	DeletedLines  int
	UnchangedLines int
	TotalLines    int
	AddedChars    int
	DeletedChars  int
}

// String returns a formatted string representation of the stats
func (ds DiffStats) String() string {
	return fmt.Sprintf(
		"Lines: +%d -%d ~%d (total: %d) | Chars: +%d -%d",
		ds.AddedLines, ds.DeletedLines, ds.UnchangedLines, ds.TotalLines,
		ds.AddedChars, ds.DeletedChars,
	)
}

// SideBySideDiffViewer represents a side-by-side diff viewer
type SideBySideDiffViewer struct {
	leftViewport  viewport.Model
	rightViewport viewport.Model
	originalText  string
	newText       string
	width         int
	height        int
	focused       bool
	activePane    int // 0 = left, 1 = right
	theme         DiffTheme
}

// NewSideBySideDiffViewer creates a new side-by-side diff viewer
func NewSideBySideDiffViewer(originalText, newText string, width, height int) *SideBySideDiffViewer {
	halfWidth := (width - 6) / 2
	
	sbs := &SideBySideDiffViewer{
		originalText: originalText,
		newText:      newText,
		width:        width,
		height:       height,
		theme:        DefaultDiffTheme(),
	}
	
	sbs.leftViewport = viewport.New(halfWidth, height-4)
	sbs.rightViewport = viewport.New(halfWidth, height-4)
	
	sbs.updateContent()
	
	return sbs
}

// updateContent updates both viewports
func (sbs *SideBySideDiffViewer) updateContent() {
	sbs.leftViewport.SetContent(sbs.formatText(sbs.originalText, "Original"))
	sbs.rightViewport.SetContent(sbs.formatText(sbs.newText, "Modified"))
}

// formatText formats text for display
func (sbs *SideBySideDiffViewer) formatText(text, title string) string {
	lines := strings.Split(text, "\n")
	var result strings.Builder
	
	// Add title
	titleStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("#ffffff")).
		Background(lipgloss.Color("#374151")).
		Width(sbs.leftViewport.Width).
		Align(lipgloss.Center)
	
	result.WriteString(titleStyle.Render(title) + "\n")
	
	// Add line numbers and content
	lineNumberStyle := lipgloss.NewStyle().
		Foreground(sbs.theme.LineNumberFg).
		Width(4).
		Align(lipgloss.Right)
	
	for i, line := range lines {
		lineNum := lineNumberStyle.Render(fmt.Sprintf("%d", i+1))
		result.WriteString(fmt.Sprintf("%s %s\n", lineNum, line))
	}
	
	return result.String()
}

// Update handles tea.Msg updates for side-by-side viewer
func (sbs *SideBySideDiffViewer) Update(msg tea.Msg) (*SideBySideDiffViewer, tea.Cmd) {
	var cmd tea.Cmd
	
	switch msg := msg.(type) {
	case tea.KeyMsg:
		if sbs.focused {
			switch msg.String() {
			case "tab":
				sbs.activePane = (sbs.activePane + 1) % 2
				return sbs, nil
			}
		}
	}
	
	if sbs.activePane == 0 {
		sbs.leftViewport, cmd = sbs.leftViewport.Update(msg)
	} else {
		sbs.rightViewport, cmd = sbs.rightViewport.Update(msg)
	}
	
	return sbs, cmd
}

// View renders the side-by-side diff viewer
func (sbs *SideBySideDiffViewer) View() string {
	leftBorder := lipgloss.RoundedBorder()
	rightBorder := lipgloss.RoundedBorder()
	
	if sbs.activePane == 0 {
		leftBorder.Left = "┃"
	} else {
		rightBorder.Left = "┃"
	}
	
	leftStyle := lipgloss.NewStyle().
		Border(leftBorder).
		BorderForeground(sbs.theme.BorderColor).
		Width((sbs.width-2)/2).
		Height(sbs.height)
	
	rightStyle := lipgloss.NewStyle().
		Border(rightBorder).
		BorderForeground(sbs.theme.BorderColor).
		Width((sbs.width-2)/2).
		Height(sbs.height)
	
	if sbs.focused && sbs.activePane == 0 {
		leftStyle = leftStyle.BorderForeground(lipgloss.Color("#3b82f6"))
	} else if sbs.focused && sbs.activePane == 1 {
		rightStyle = rightStyle.BorderForeground(lipgloss.Color("#3b82f6"))
	}
	
	left := leftStyle.Render(sbs.leftViewport.View())
	right := rightStyle.Render(sbs.rightViewport.View())
	
	return lipgloss.JoinHorizontal(lipgloss.Top, left, right)
}
