/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/charmbracelet/log"
)

// Manager manages all built-in tools
type Manager struct {
	logger *log.Logger
	tools  map[string]Tool
	mu     sync.RWMutex
}

// NewManager creates a new tool manager
func NewManager(logger *log.Logger) (*Manager, error) {
	manager := &Manager{
		logger: logger,
		tools:  make(map[string]Tool),
	}

	if err := manager.registerBuiltinTools(); err != nil {
		return nil, fmt.Errorf("failed to register builtin tools: %w", err)
	}

	return manager, nil
}

// registerBuiltinTools registers all built-in tools
func (m *Manager) registerBuiltinTools() error {
	// File operation tools
	m.RegisterTool(NewLsTool())
	m.RegisterTool(NewReadTool())
	m.RegisterTool(NewWriteTool())
	m.RegisterTool(NewReadManyFilesTool())

	// Search tools
	m.RegisterTool(NewGrepTool())
	m.RegisterTool(NewGlobTool())
	m.RegisterTool(NewSearchTool())

	// Shell tools
	m.RegisterTool(NewShellTool())

	// Edit tool
	m.RegisterTool(NewEditTool())

	// Memory tool
	m.RegisterTool(NewMemoryTool())

	// Web tools
	m.RegisterTool(NewWebSearchTool())
	m.RegisterTool(NewWebFetchTool())
	m.RegisterTool(NewBrowserTool())

	// Task tools
	m.RegisterTool(NewTaskTool())

	// Utility tools
	m.RegisterTool(NewDiffTool())

	// Performance tools
	m.RegisterTool(NewPerformanceTool())

	// Advanced tools
	m.RegisterTool(NewPlaywrightTool())
	m.RegisterTool(NewMermaidTool())
	m.RegisterTool(NewDiagnosticsTool())

	m.logger.Info("Built-in tools registered", "count", len(m.tools))
	return nil
}

// RegisterTool registers a new tool
func (m *Manager) RegisterTool(tool Tool) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.tools[tool.Name()] = tool
	m.logger.Debug("Tool registered", "name", tool.Name())
}

// GetTool returns a tool by name
func (m *Manager) GetTool(name string) (Tool, bool) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	tool, exists := m.tools[name]
	return tool, exists
}

// GetTools returns all registered tools
func (m *Manager) GetTools() map[string]Tool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	tools := make(map[string]Tool)
	for name, tool := range m.tools {
		tools[name] = tool
	}
	return tools
}

// GetToolsByCategory returns tools filtered by category
func (m *Manager) GetToolsByCategory(category ToolCategory) []Tool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	var tools []Tool
	for _, tool := range m.tools {
		// This would require adding category to Tool interface
		// For now, we'll implement basic categorization
		tools = append(tools, tool)
	}
	return tools
}

// ExecuteTool executes a tool with given arguments
func (m *Manager) ExecuteTool(ctx context.Context, name string, args map[string]interface{}) Result {
	start := time.Now()
	
	tool, exists := m.GetTool(name)
	if !exists {
		return Result{
			Error:     fmt.Errorf("tool '%s' not found", name),
			Duration:  time.Since(start),
			Timestamp: time.Now(),
		}
	}

	// Validate arguments
	if err := tool.Validate(args); err != nil {
		return Result{
			Error:     fmt.Errorf("tool validation failed: %w", err),
			Duration:  time.Since(start),
			Timestamp: time.Now(),
		}
	}

	m.logger.Debug("Executing tool", "name", name, "args", args)

	// Execute the tool
	result := tool.Execute(ctx, args)
	result.Duration = time.Since(start)
	result.Timestamp = time.Now()

	if result.Error != nil {
		m.logger.Error("Tool execution failed", "name", name, "error", result.Error)
	} else {
		m.logger.Debug("Tool execution completed", "name", name, "duration", result.Duration)
	}

	return result
}

// ExecuteToolsParallel executes multiple tools in parallel
func (m *Manager) ExecuteToolsParallel(ctx context.Context, toolCalls []ToolCall) []Result {
	if len(toolCalls) == 0 {
		return nil
	}

	results := make([]Result, len(toolCalls))
	var wg sync.WaitGroup

	for i, toolCall := range toolCalls {
		wg.Add(1)
		go func(index int, call ToolCall) {
			defer wg.Done()
			results[index] = m.ExecuteTool(ctx, call.Name, call.Args)
		}(i, toolCall)
	}

	wg.Wait()
	return results
}

// List displays all available tools
func (m *Manager) List() error {
	tools := m.GetTools()
	
	fmt.Printf("Available Tools (%d):\n\n", len(tools))
	
	categories := map[ToolCategory][]Tool{
		CategoryFile:   {},
		CategorySearch: {},
		CategoryShell:  {},
		CategoryWeb:    {},
		CategoryMemory: {},
		CategoryTask:   {},
		CategoryUtil:   {},
	}

	// Categorize tools (basic implementation)
	for _, tool := range tools {
		name := tool.Name()
		switch {
		case name == "ls" || name == "read" || name == "write" || name == "read-many-files":
			categories[CategoryFile] = append(categories[CategoryFile], tool)
		case name == "grep" || name == "glob" || name == "search":
			categories[CategorySearch] = append(categories[CategorySearch], tool)
		case name == "shell":
			categories[CategoryShell] = append(categories[CategoryShell], tool)
		case name == "web-search" || name == "web-fetch" || name == "browser":
			categories[CategoryWeb] = append(categories[CategoryWeb], tool)
		case name == "memory":
			categories[CategoryMemory] = append(categories[CategoryMemory], tool)
		case name == "tasks":
			categories[CategoryTask] = append(categories[CategoryTask], tool)
		default:
			categories[CategoryUtil] = append(categories[CategoryUtil], tool)
		}
	}

	// Display by category
	for category, categoryTools := range categories {
		if len(categoryTools) == 0 {
			continue
		}
		
		fmt.Printf("📁 %s Tools:\n", string(category))
		for _, tool := range categoryTools {
			parallel := ""
			if tool.SupportsParallel() {
				parallel = " (parallel)"
			}
			fmt.Printf("  • %s%s - %s\n", tool.Name(), parallel, tool.Description())
		}
		fmt.Println()
	}

	return nil
}

// GetToolInfo returns information about all tools for LLM function calling
func (m *Manager) GetToolInfo() []ToolInfo {
	tools := m.GetTools()
	info := make([]ToolInfo, 0, len(tools))
	
	for _, tool := range tools {
		info = append(info, ToolInfo{
			Name:        tool.Name(),
			Description: tool.Description(),
			Parameters:  tool.Parameters(),
			Parallel:    tool.SupportsParallel(),
		})
	}
	
	return info
}

// Shutdown gracefully shuts down the tool manager
func (m *Manager) Shutdown(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.logger.Info("Shutting down tool manager...")
	
	// Tools don't need explicit shutdown for now
	// In the future, tools with persistent connections would be shut down here
	
	m.logger.Info("Tool manager shutdown complete")
	return nil
}

// ToolCall represents a tool call request
type ToolCall struct {
	Name string                 `json:"name"`
	Args map[string]interface{} `json:"args"`
}
