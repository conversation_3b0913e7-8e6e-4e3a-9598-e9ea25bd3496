/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package security

import (
	"fmt"
	"path/filepath"
	"regexp"
	"strings"
)

// Validator provides security validation for user inputs
type Validator struct {
	// Dangerous command patterns
	dangerousCommands []string
	// Dangerous file patterns
	dangerousFiles []string
	// Allowed file extensions
	allowedExtensions map[string]bool
	// Path traversal patterns
	pathTraversalPatterns []*regexp.Regexp
}

// NewValidator creates a new security validator
func NewValidator() *Validator {
	return &Validator{
		dangerousCommands: []string{
			"rm -rf",
			"del /f",
			"format",
			"fdisk",
			"mkfs",
			"dd if=",
			":(){ :|:& };:",  // Fork bomb
			"sudo rm",
			"sudo dd",
			"sudo mkfs",
			"shutdown",
			"reboot",
			"halt",
			"poweroff",
			"init 0",
			"init 6",
			"killall",
			"pkill -9",
			"kill -9",
			"> /dev/",
			"chmod 777",
			"chown root",
			"passwd",
			"su -",
			"sudo su",
		},
		dangerousFiles: []string{
			"/etc/passwd",
			"/etc/shadow",
			"/etc/sudoers",
			"/boot/",
			"/sys/",
			"/proc/",
			"/dev/",
			"C:\\Windows\\System32",
			"C:\\Windows\\Boot",
			"C:\\Program Files",
			"C:\\Users\\<USER>\.\.[\\/]`),
			regexp.MustCompile(`[\\/]\.\.[\\/]`),
			regexp.MustCompile(`^\.\.[\\/]`),
			regexp.MustCompile(`[\\/]\.\.$`),
		},
	}
}

// ValidateCommand validates a shell command for security risks
func (v *Validator) ValidateCommand(command string) error {
	if command == "" {
		return fmt.Errorf("empty command")
	}

	lowerCommand := strings.ToLower(command)

	// Check for dangerous commands
	for _, dangerous := range v.dangerousCommands {
		if strings.Contains(lowerCommand, strings.ToLower(dangerous)) {
			return fmt.Errorf("potentially dangerous command detected: %s", dangerous)
		}
	}

	// Check for suspicious patterns
	suspiciousPatterns := []string{
		"curl.*|.*sh",     // Pipe curl to shell
		"wget.*|.*sh",     // Pipe wget to shell
		"eval.*\\$",       // Dynamic evaluation
		"exec.*\\$",       // Dynamic execution
		"system.*\\$",     // System calls with variables
		"\\$\\(.*\\)",     // Command substitution
		"`.*`",            // Backtick command substitution
		"nc.*-e",          // Netcat with execute
		"ncat.*-e",        // Ncat with execute
		"/dev/tcp/",       // TCP device files
		"/dev/udp/",       // UDP device files
	}

	for _, pattern := range suspiciousPatterns {
		if matched, _ := regexp.MatchString(pattern, command); matched {
			return fmt.Errorf("suspicious command pattern detected: %s", pattern)
		}
	}

	return nil
}

// ValidateFilePath validates a file path for security risks
func (v *Validator) ValidateFilePath(path string) error {
	if path == "" {
		return fmt.Errorf("empty file path")
	}

	// Normalize path
	cleanPath := filepath.Clean(path)

	// Check for path traversal
	for _, pattern := range v.pathTraversalPatterns {
		if pattern.MatchString(path) {
			return fmt.Errorf("path traversal detected in: %s", path)
		}
	}

	// Check for dangerous system paths
	for _, dangerous := range v.dangerousFiles {
		if strings.HasPrefix(strings.ToLower(cleanPath), strings.ToLower(dangerous)) {
			return fmt.Errorf("access to dangerous system path denied: %s", dangerous)
		}
	}

	// Check file extension if it's a file
	ext := strings.ToLower(filepath.Ext(cleanPath))
	if ext != "" && !v.allowedExtensions[ext] {
		return fmt.Errorf("file extension not allowed: %s", ext)
	}

	// Check for null bytes (can be used to bypass filters)
	if strings.Contains(path, "\x00") {
		return fmt.Errorf("null byte detected in path")
	}

	// Check for excessively long paths
	if len(path) > 4096 {
		return fmt.Errorf("path too long (max 4096 characters)")
	}

	return nil
}

// ValidateURL validates a URL for security risks
func (v *Validator) ValidateURL(url string) error {
	if url == "" {
		return fmt.Errorf("empty URL")
	}

	// Check for dangerous protocols
	dangerousProtocols := []string{
		"file://",
		"ftp://",
		"sftp://",
		"ssh://",
		"telnet://",
		"ldap://",
		"ldaps://",
		"gopher://",
	}

	lowerURL := strings.ToLower(url)
	for _, protocol := range dangerousProtocols {
		if strings.HasPrefix(lowerURL, protocol) {
			return fmt.Errorf("dangerous protocol not allowed: %s", protocol)
		}
	}

	// Check for localhost/internal network access
	internalHosts := []string{
		"localhost",
		"127.0.0.1",
		"0.0.0.0",
		"::1",
		"10.",
		"172.16.",
		"172.17.",
		"172.18.",
		"172.19.",
		"172.20.",
		"172.21.",
		"172.22.",
		"172.23.",
		"172.24.",
		"172.25.",
		"172.26.",
		"172.27.",
		"172.28.",
		"172.29.",
		"172.30.",
		"172.31.",
		"192.168.",
		"169.254.",
	}

	for _, host := range internalHosts {
		if strings.Contains(lowerURL, host) {
			return fmt.Errorf("access to internal network not allowed: %s", host)
		}
	}

	// Check URL length
	if len(url) > 2048 {
		return fmt.Errorf("URL too long (max 2048 characters)")
	}

	return nil
}

// ValidateInput validates general user input for security risks
func (v *Validator) ValidateInput(input string) error {
	if len(input) > 100000 {
		return fmt.Errorf("input too long (max 100KB)")
	}

	// Check for potential injection patterns
	injectionPatterns := []string{
		"<script",
		"javascript:",
		"vbscript:",
		"onload=",
		"onerror=",
		"onclick=",
		"onmouseover=",
		"eval\\(",
		"setTimeout\\(",
		"setInterval\\(",
		"Function\\(",
		"\\$\\{.*\\}",  // Template injection
		"\\#\\{.*\\}",  // Template injection
	}

	lowerInput := strings.ToLower(input)
	for _, pattern := range injectionPatterns {
		if matched, _ := regexp.MatchString(pattern, lowerInput); matched {
			return fmt.Errorf("potential injection pattern detected: %s", pattern)
		}
	}

	// Check for excessive special characters (potential obfuscation)
	specialCharCount := 0
	for _, char := range input {
		if !((char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z') || 
			 (char >= '0' && char <= '9') || char == ' ' || char == '\n' || 
			 char == '\t' || char == '.' || char == ',' || char == '!' || 
			 char == '?' || char == ':' || char == ';' || char == '-' || 
			 char == '_' || char == '(' || char == ')' || char == '[' || 
			 char == ']' || char == '{' || char == '}' || char == '"' || 
			 char == '\'' || char == '/' || char == '\\' || char == '@' || 
			 char == '#' || char == '$' || char == '%' || char == '^' || 
			 char == '&' || char == '*' || char == '+' || char == '=' || 
			 char == '<' || char == '>') {
			specialCharCount++
		}
	}

	if len(input) > 0 && float64(specialCharCount)/float64(len(input)) > 0.3 {
		return fmt.Errorf("excessive special characters detected (potential obfuscation)")
	}

	return nil
}

// SanitizeInput sanitizes user input by removing potentially dangerous content
func (v *Validator) SanitizeInput(input string) string {
	// Remove null bytes
	input = strings.ReplaceAll(input, "\x00", "")
	
	// Remove excessive whitespace
	input = regexp.MustCompile(`\s+`).ReplaceAllString(input, " ")
	
	// Trim whitespace
	input = strings.TrimSpace(input)
	
	// Limit length
	if len(input) > 100000 {
		input = input[:100000]
	}
	
	return input
}

// IsPathSafe checks if a path is safe for file operations
func (v *Validator) IsPathSafe(path string) bool {
	return v.ValidateFilePath(path) == nil
}

// IsCommandSafe checks if a command is safe to execute
func (v *Validator) IsCommandSafe(command string) bool {
	return v.ValidateCommand(command) == nil
}

// IsURLSafe checks if a URL is safe to access
func (v *Validator) IsURLSafe(url string) bool {
	return v.ValidateURL(url) == nil
}
