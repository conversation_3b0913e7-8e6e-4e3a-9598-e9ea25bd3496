/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package llm

// This package now re-exports interfaces from the interfaces package
// to maintain backward compatibility while avoiding import cycles

import "arien/internal/interfaces"

// Re-export types from interfaces package
type Provider = interfaces.Provider
type Request = interfaces.Request
type Response = interfaces.Response
type Message = interfaces.Message
type Tool = interfaces.Tool
type ToolFunction = interfaces.ToolFunction
type ToolCall = interfaces.ToolCall
type Usage = interfaces.Usage

// Re-export constants
const (
	RoleSystem    = interfaces.RoleSystem
	RoleUser      = interfaces.RoleUser
	RoleAssistant = interfaces.RoleAssistant
	RoleTool      = interfaces.RoleTool
)

const (
	ToolTypeFunction = interfaces.ToolTypeFunction
)

// Re-export functions
var (
	NewMessage         = interfaces.NewMessage
	NewSystemMessage   = interfaces.NewSystemMessage
	NewUserMessage     = interfaces.NewUserMessage
	NewAssistantMessage = interfaces.NewAssistantMessage
	NewToolMessage     = interfaces.NewToolMessage
)
