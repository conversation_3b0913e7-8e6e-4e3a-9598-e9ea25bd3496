/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package core

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/charmbracelet/log"
)

// Metrics tracks performance and usage statistics
type Metrics struct {
	logger *log.Logger
	mu     sync.RWMutex
	
	// Request metrics
	TotalRequests     int64         `json:"total_requests"`
	SuccessfulRequests int64        `json:"successful_requests"`
	FailedRequests    int64         `json:"failed_requests"`
	AverageLatency    time.Duration `json:"average_latency"`
	
	// Tool metrics
	ToolExecutions    map[string]int64 `json:"tool_executions"`
	ToolLatencies     map[string]time.Duration `json:"tool_latencies"`
	
	// LLM metrics
	LLMRequests       map[string]int64 `json:"llm_requests"`
	TokensUsed        map[string]int64 `json:"tokens_used"`
	
	// Memory metrics
	MemoryOperations  int64 `json:"memory_operations"`
	
	// Task metrics
	TasksCreated      int64 `json:"tasks_created"`
	TasksCompleted    int64 `json:"tasks_completed"`
	
	// Performance tracking
	latencies         []time.Duration
	startTime         time.Time
}

// NewMetrics creates a new metrics instance
func NewMetrics(logger *log.Logger) *Metrics {
	return &Metrics{
		logger:        logger,
		ToolExecutions: make(map[string]int64),
		ToolLatencies:  make(map[string]time.Duration),
		LLMRequests:   make(map[string]int64),
		TokensUsed:    make(map[string]int64),
		latencies:     make([]time.Duration, 0, 1000), // Keep last 1000 latencies
		startTime:     time.Now(),
	}
}

// RecordRequest records a request with its latency and success status
func (m *Metrics) RecordRequest(latency time.Duration, success bool) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.TotalRequests++
	if success {
		m.SuccessfulRequests++
	} else {
		m.FailedRequests++
	}
	
	// Add to latencies (keep only last 1000)
	m.latencies = append(m.latencies, latency)
	if len(m.latencies) > 1000 {
		m.latencies = m.latencies[1:]
	}
	
	// Recalculate average latency
	m.calculateAverageLatency()
}

// RecordToolExecution records a tool execution
func (m *Metrics) RecordToolExecution(toolName string, latency time.Duration) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.ToolExecutions[toolName]++
	
	// Update average latency for this tool
	currentLatency := m.ToolLatencies[toolName]
	currentCount := m.ToolExecutions[toolName]
	
	// Calculate new average: (old_avg * (count-1) + new_latency) / count
	if currentCount == 1 {
		m.ToolLatencies[toolName] = latency
	} else {
		oldTotal := currentLatency * time.Duration(currentCount-1)
		newAverage := (oldTotal + latency) / time.Duration(currentCount)
		m.ToolLatencies[toolName] = newAverage
	}
}

// RecordLLMRequest records an LLM request
func (m *Metrics) RecordLLMRequest(provider string, tokens int64) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.LLMRequests[provider]++
	m.TokensUsed[provider] += tokens
}

// RecordMemoryOperation records a memory operation
func (m *Metrics) RecordMemoryOperation() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.MemoryOperations++
}

// RecordTaskCreated records a task creation
func (m *Metrics) RecordTaskCreated() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.TasksCreated++
}

// RecordTaskCompleted records a task completion
func (m *Metrics) RecordTaskCompleted() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.TasksCompleted++
}

// GetStats returns current statistics
func (m *Metrics) GetStats() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	uptime := time.Since(m.startTime)
	successRate := float64(0)
	if m.TotalRequests > 0 {
		successRate = float64(m.SuccessfulRequests) / float64(m.TotalRequests) * 100
	}
	
	requestsPerSecond := float64(0)
	if uptime.Seconds() > 0 {
		requestsPerSecond = float64(m.TotalRequests) / uptime.Seconds()
	}
	
	return map[string]interface{}{
		"uptime":              uptime.String(),
		"total_requests":      m.TotalRequests,
		"successful_requests": m.SuccessfulRequests,
		"failed_requests":     m.FailedRequests,
		"success_rate":        successRate,
		"requests_per_second": requestsPerSecond,
		"average_latency":     m.AverageLatency.String(),
		"tool_executions":     m.ToolExecutions,
		"tool_latencies":      m.formatDurations(m.ToolLatencies),
		"llm_requests":        m.LLMRequests,
		"tokens_used":         m.TokensUsed,
		"memory_operations":   m.MemoryOperations,
		"tasks_created":       m.TasksCreated,
		"tasks_completed":     m.TasksCompleted,
	}
}

// GetPerformanceReport generates a detailed performance report
func (m *Metrics) GetPerformanceReport() string {
	stats := m.GetStats()
	
	report := "=== Arien Performance Report ===\n\n"
	
	// System metrics
	report += "System Metrics:\n"
	report += fmt.Sprintf("  Uptime: %v\n", stats["uptime"])
	report += fmt.Sprintf("  Total Requests: %d\n", stats["total_requests"])
	report += fmt.Sprintf("  Success Rate: %.2f%%\n", stats["success_rate"])
	report += fmt.Sprintf("  Requests/Second: %.2f\n", stats["requests_per_second"])
	report += fmt.Sprintf("  Average Latency: %v\n", stats["average_latency"])
	report += "\n"
	
	// Tool metrics
	report += "Tool Usage:\n"
	toolExecs := stats["tool_executions"].(map[string]int64)
	toolLats := stats["tool_latencies"].(map[string]string)
	for tool, count := range toolExecs {
		latency := toolLats[tool]
		report += fmt.Sprintf("  %s: %d executions, avg latency: %s\n", tool, count, latency)
	}
	report += "\n"
	
	// LLM metrics
	report += "LLM Usage:\n"
	llmReqs := stats["llm_requests"].(map[string]int64)
	tokensUsed := stats["tokens_used"].(map[string]int64)
	for provider, count := range llmReqs {
		tokens := tokensUsed[provider]
		report += fmt.Sprintf("  %s: %d requests, %d tokens\n", provider, count, tokens)
	}
	report += "\n"
	
	// Other metrics
	report += "Other Metrics:\n"
	report += fmt.Sprintf("  Memory Operations: %d\n", stats["memory_operations"])
	report += fmt.Sprintf("  Tasks Created: %d\n", stats["tasks_created"])
	report += fmt.Sprintf("  Tasks Completed: %d\n", stats["tasks_completed"])
	
	return report
}

// calculateAverageLatency calculates the average latency from recent samples
func (m *Metrics) calculateAverageLatency() {
	if len(m.latencies) == 0 {
		m.AverageLatency = 0
		return
	}
	
	var total time.Duration
	for _, latency := range m.latencies {
		total += latency
	}
	
	m.AverageLatency = total / time.Duration(len(m.latencies))
}

// formatDurations converts duration map to string map for JSON serialization
func (m *Metrics) formatDurations(durations map[string]time.Duration) map[string]string {
	result := make(map[string]string)
	for key, duration := range durations {
		result[key] = duration.String()
	}
	return result
}

// Reset resets all metrics
func (m *Metrics) Reset() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.TotalRequests = 0
	m.SuccessfulRequests = 0
	m.FailedRequests = 0
	m.AverageLatency = 0
	m.ToolExecutions = make(map[string]int64)
	m.ToolLatencies = make(map[string]time.Duration)
	m.LLMRequests = make(map[string]int64)
	m.TokensUsed = make(map[string]int64)
	m.MemoryOperations = 0
	m.TasksCreated = 0
	m.TasksCompleted = 0
	m.latencies = make([]time.Duration, 0, 1000)
	m.startTime = time.Now()
	
	m.logger.Info("Metrics reset")
}

// Shutdown gracefully shuts down the metrics system
func (m *Metrics) Shutdown(ctx context.Context) error {
	m.logger.Info("Shutting down metrics system...")
	
	// Log final stats
	m.logger.Info("Final performance stats", "stats", m.GetStats())
	
	return nil
}
