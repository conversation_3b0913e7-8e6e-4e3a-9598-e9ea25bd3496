/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// DiagnosticsTool implements IDE integration and error detection
type DiagnosticsTool struct {
	supportedLanguages map[string]bool
}

// NewDiagnosticsTool creates a new diagnostics tool
func NewDiagnosticsTool() *DiagnosticsTool {
	return &DiagnosticsTool{
		supportedLanguages: map[string]bool{
			"go":         true,
			"python":     true,
			"javascript": true,
			"typescript": true,
			"java":       true,
			"c":          true,
			"cpp":        true,
			"rust":       true,
			"php":        true,
			"ruby":       true,
		},
	}
}

// Name returns the tool name
func (t *DiagnosticsTool) Name() string {
	return "diagnostics"
}

// Description returns the tool description
func (t *DiagnosticsTool) Description() string {
	return "Get IDE issues, errors, warnings, and code quality diagnostics"
}

// Parameters returns the tool parameter schema
func (t *DiagnosticsTool) Parameters() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"action": map[string]interface{}{
				"type": "string",
				"description": "Diagnostic action to perform",
				"enum": []string{"scan", "analyze", "lint", "format_check", "security_scan", "performance_check"},
			},
			"path": StringParameter("File or directory path to analyze", false),
			"language": map[string]interface{}{
				"type": "string",
				"description": "Programming language",
				"enum": []string{"go", "python", "javascript", "typescript", "java", "c", "cpp", "rust", "php", "ruby"},
			},
			"severity": map[string]interface{}{
				"type": "string",
				"description": "Minimum severity level",
				"enum": []string{"error", "warning", "info", "hint"},
				"default": "warning",
			},
			"include_suggestions": BoolParameter("Include improvement suggestions", true),
			"recursive": BoolParameter("Scan directories recursively", true),
			"output_format": map[string]interface{}{
				"type": "string",
				"description": "Output format",
				"enum": []string{"text", "json", "markdown"},
				"default": "text",
			},
		},
		"required": []string{"action"},
	}
}

// Execute executes the diagnostics tool
func (t *DiagnosticsTool) Execute(ctx context.Context, args map[string]interface{}) Result {
	action, ok := args["action"].(string)
	if !ok {
		return Result{Error: fmt.Errorf("action is required")}
	}

	path, _ := args["path"].(string)
	if path == "" {
		path = "."
	}

	language, _ := args["language"].(string)
	severity, _ := args["severity"].(string)
	if severity == "" {
		severity = "warning"
	}

	includeSuggestions := getBoolArg(args, "include_suggestions", true)
	recursive := getBoolArg(args, "recursive", true)
	outputFormat, _ := args["output_format"].(string)
	if outputFormat == "" {
		outputFormat = "text"
	}

	switch action {
	case "scan":
		return t.scanForIssues(path, language, severity, recursive, outputFormat)
	case "analyze":
		return t.analyzeCode(path, language, includeSuggestions, outputFormat)
	case "lint":
		return t.runLinter(path, language, outputFormat)
	case "format_check":
		return t.checkFormatting(path, language, outputFormat)
	case "security_scan":
		return t.securityScan(path, language, outputFormat)
	case "performance_check":
		return t.performanceCheck(path, language, outputFormat)
	default:
		return Result{Error: fmt.Errorf("unknown action: %s", action)}
	}
}

// Validate validates the tool arguments
func (t *DiagnosticsTool) Validate(args map[string]interface{}) error {
	action, ok := args["action"].(string)
	if !ok {
		return fmt.Errorf("action is required")
	}

	validActions := []string{"scan", "analyze", "lint", "format_check", "security_scan", "performance_check"}
	valid := false
	for _, validAction := range validActions {
		if action == validAction {
			valid = true
			break
		}
	}
	if !valid {
		return fmt.Errorf("invalid action: %s", action)
	}

	if path, ok := args["path"].(string); ok && path != "" {
		if _, err := os.Stat(path); os.IsNotExist(err) {
			return fmt.Errorf("path does not exist: %s", path)
		}
	}

	return nil
}

// SupportsParallel returns true if the tool can be executed in parallel
func (t *DiagnosticsTool) SupportsParallel() bool {
	return true
}

// scanForIssues scans for general issues in code
func (t *DiagnosticsTool) scanForIssues(path, language, severity string, recursive bool, outputFormat string) Result {
	issues := []Issue{}

	// Walk through files
	err := filepath.Walk(path, func(filePath string, info os.FileInfo, err error) error {
		if err != nil {
			return nil // Skip files with errors
		}

		if info.IsDir() {
			return nil
		}

		// Skip if not recursive and not in root directory
		if !recursive && filepath.Dir(filePath) != path {
			return filepath.SkipDir
		}

		// Detect language if not specified
		detectedLang := language
		if detectedLang == "" {
			detectedLang = t.detectLanguage(filePath)
		}

		if detectedLang != "" && t.supportedLanguages[detectedLang] {
			fileIssues := t.analyzeFile(filePath, detectedLang, severity)
			issues = append(issues, fileIssues...)
		}

		return nil
	})

	if err != nil {
		return Result{Error: fmt.Errorf("failed to scan files: %w", err)}
	}

	// Format output
	output := t.formatIssues(issues, outputFormat)

	metadata := map[string]interface{}{
		"action":       "scan",
		"path":         path,
		"language":     language,
		"severity":     severity,
		"recursive":    recursive,
		"total_issues": len(issues),
		"error_count":  t.countIssuesBySeverity(issues, "error"),
		"warning_count": t.countIssuesBySeverity(issues, "warning"),
		"info_count":   t.countIssuesBySeverity(issues, "info"),
		"timestamp":    time.Now(),
	}

	return Result{
		Output:   output,
		Metadata: metadata,
	}
}

// analyzeCode performs comprehensive code analysis
func (t *DiagnosticsTool) analyzeCode(path, language string, includeSuggestions bool, outputFormat string) Result {
	analysis := CodeAnalysis{
		Path:        path,
		Language:    language,
		Timestamp:   time.Now(),
		Metrics:     t.calculateMetrics(path, language),
		Issues:      []Issue{},
		Suggestions: []string{},
	}

	// Detect language if not specified
	if language == "" {
		analysis.Language = t.detectLanguage(path)
	}

	// Analyze file or directory
	if info, err := os.Stat(path); err == nil {
		if info.IsDir() {
			analysis = t.analyzeDirectory(path, analysis.Language, includeSuggestions)
		} else {
			analysis = t.analyzeFileDetailed(path, analysis.Language, includeSuggestions)
		}
	}

	// Format output
	output := t.formatAnalysis(analysis, outputFormat)

	metadata := map[string]interface{}{
		"action":             "analyze",
		"path":               path,
		"language":           analysis.Language,
		"include_suggestions": includeSuggestions,
		"metrics":            analysis.Metrics,
		"total_issues":       len(analysis.Issues),
		"suggestions_count":  len(analysis.Suggestions),
		"timestamp":          time.Now(),
	}

	return Result{
		Output:   output,
		Metadata: metadata,
	}
}

// runLinter runs language-specific linters
func (t *DiagnosticsTool) runLinter(path, language, outputFormat string) Result {
	if language == "" {
		language = t.detectLanguage(path)
	}

	linterResults := LinterResults{
		Language:  language,
		Path:      path,
		Issues:    []Issue{},
		Timestamp: time.Now(),
	}

	// Simulate linter results based on language
	switch language {
	case "go":
		linterResults.LinterUsed = "golangci-lint"
		linterResults.Issues = t.simulateGoLint(path)
	case "python":
		linterResults.LinterUsed = "pylint"
		linterResults.Issues = t.simulatePythonLint(path)
	case "javascript", "typescript":
		linterResults.LinterUsed = "eslint"
		linterResults.Issues = t.simulateESLint(path)
	default:
		linterResults.LinterUsed = "generic"
		linterResults.Issues = t.simulateGenericLint(path)
	}

	output := t.formatLinterResults(linterResults, outputFormat)

	metadata := map[string]interface{}{
		"action":       "lint",
		"path":         path,
		"language":     language,
		"linter":       linterResults.LinterUsed,
		"total_issues": len(linterResults.Issues),
		"timestamp":    time.Now(),
	}

	return Result{
		Output:   output,
		Metadata: metadata,
	}
}

// checkFormatting checks code formatting
func (t *DiagnosticsTool) checkFormatting(path, language, outputFormat string) Result {
	if language == "" {
		language = t.detectLanguage(path)
	}

	formatIssues := []FormatIssue{}

	// Read file content
	content, err := os.ReadFile(path)
	if err != nil {
		return Result{Error: fmt.Errorf("failed to read file: %w", err)}
	}

	lines := strings.Split(string(content), "\n")

	// Check common formatting issues
	for i, line := range lines {
		lineNum := i + 1

		// Check trailing whitespace
		if len(line) > 0 && (line[len(line)-1] == ' ' || line[len(line)-1] == '\t') {
			formatIssues = append(formatIssues, FormatIssue{
				Line:        lineNum,
				Column:      len(line),
				Type:        "trailing_whitespace",
				Description: "Trailing whitespace",
				Severity:    "info",
			})
		}

		// Check line length
		if len(line) > 120 {
			formatIssues = append(formatIssues, FormatIssue{
				Line:        lineNum,
				Column:      120,
				Type:        "line_too_long",
				Description: fmt.Sprintf("Line too long (%d > 120 characters)", len(line)),
				Severity:    "warning",
			})
		}

		// Language-specific checks
		switch language {
		case "go":
			if strings.Contains(line, "\t ") || strings.Contains(line, " \t") {
				formatIssues = append(formatIssues, FormatIssue{
					Line:        lineNum,
					Column:      0,
					Type:        "mixed_indentation",
					Description: "Mixed tabs and spaces",
					Severity:    "error",
				})
			}
		case "python":
			if strings.HasPrefix(line, "\t") {
				formatIssues = append(formatIssues, FormatIssue{
					Line:        lineNum,
					Column:      0,
					Type:        "tab_indentation",
					Description: "Use spaces instead of tabs for indentation",
					Severity:    "warning",
				})
			}
		}
	}

	output := t.formatFormatIssues(formatIssues, outputFormat)

	metadata := map[string]interface{}{
		"action":        "format_check",
		"path":          path,
		"language":      language,
		"total_issues":  len(formatIssues),
		"line_count":    len(lines),
		"timestamp":     time.Now(),
	}

	return Result{
		Output:   output,
		Metadata: metadata,
	}
}

// securityScan performs security analysis
func (t *DiagnosticsTool) securityScan(path, language, outputFormat string) Result {
	output := fmt.Sprintf("🔒 Security Scan Results for: %s\n", path)
	output += strings.Repeat("=", 50) + "\n\n"
	output += "📝 Note: This is a basic security scan simulation.\n"
	output += "For production use, integrate with tools like:\n"
	output += "• Snyk for dependency vulnerabilities\n"
	output += "• SonarQube for code security\n"
	output += "• Bandit for Python security\n"
	output += "• Gosec for Go security\n\n"
	output += "✅ No critical security issues found in simulation.\n"

	metadata := map[string]interface{}{
		"action":    "security_scan",
		"path":      path,
		"language":  language,
		"timestamp": time.Now(),
	}

	return Result{
		Output:   output,
		Metadata: metadata,
	}
}

// performanceCheck performs performance analysis
func (t *DiagnosticsTool) performanceCheck(path, language, outputFormat string) Result {
	output := fmt.Sprintf("⚡ Performance Check Results for: %s\n", path)
	output += strings.Repeat("=", 50) + "\n\n"
	output += "📝 Note: This is a basic performance check simulation.\n"
	output += "For production use, integrate with tools like:\n"
	output += "• Go pprof for Go profiling\n"
	output += "• Python cProfile for Python profiling\n"
	output += "• Chrome DevTools for JavaScript\n"
	output += "• JProfiler for Java\n\n"
	output += "✅ No performance bottlenecks detected in simulation.\n"

	metadata := map[string]interface{}{
		"action":    "performance_check",
		"path":      path,
		"language":  language,
		"timestamp": time.Now(),
	}

	return Result{
		Output:   output,
		Metadata: metadata,
	}
}
