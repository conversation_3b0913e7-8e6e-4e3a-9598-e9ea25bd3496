/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package chat

import (
	"context"
	"fmt"

	"arien/internal/core"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/log"
)

// ChatApp handles the main chat interface
type ChatApp struct {
	engine *core.Engine
	logger *log.Logger
}

// NewChatApp creates a new chat app
func NewChatApp(engine *core.Engine, logger *log.Logger) *ChatApp {
	return &ChatApp{
		engine: engine,
		logger: logger,
	}
}

// Run starts the chat interface
func (c *ChatApp) Run(ctx context.Context) error {
	// Initialize the chat model with all components
	model, err := NewChatModel(c.engine, c.logger)
	if err != nil {
		return fmt.Errorf("failed to initialize chat model: %w", err)
	}

	// Start the Bubble Tea program
	p := tea.NewProgram(model, tea.WithAltScreen())

	if _, err := p.Run(); err != nil {
		return fmt.Errorf("chat UI failed: %w", err)
	}

	return nil
}


