/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package chat

import (
	"context"
	"fmt"
	"strings"

	"arien/internal/core"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/charmbracelet/log"
)

// ChatA<PERSON> handles the main chat interface
type ChatApp struct {
	engine *core.Engine
	logger *log.Logger
}

// chatModel represents the chat UI model
type chatModel struct {
	engine   *core.Engine
	logger   *log.Logger
	messages []Message
	input    string
	cursor   int
	loading  bool
	err      error
}

// Message represents a chat message
type Message struct {
	Role    string
	Content string
	Time    string
}

// NewChatApp creates a new chat app
func NewChatApp(engine *core.Engine, logger *log.Logger) *ChatApp {
	return &ChatApp{
		engine: engine,
		logger: logger,
	}
}

// Run starts the chat interface
func (c *ChatApp) Run(ctx context.Context) error {
	model := chatModel{
		engine:   c.engine,
		logger:   c.logger,
		messages: []Message{},
	}

	// Add welcome message
	model.messages = append(model.messages, Message{
		Role:    "assistant",
		Content: "Hello! I'm <PERSON><PERSON>, your AI-powered software engineering assistant. How can I help you today?",
		Time:    "now",
	})

	p := tea.NewProgram(model, tea.WithAltScreen())
	
	if _, err := p.Run(); err != nil {
		return fmt.Errorf("chat UI failed: %w", err)
	}

	return nil
}

// Init initializes the model
func (m chatModel) Init() tea.Cmd {
	return nil
}

// Update handles messages
func (m chatModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c":
			return m, tea.Quit
		case "enter":
			if m.input != "" && !m.loading {
				return m.sendMessage()
			}
		case "backspace":
			if len(m.input) > 0 {
				m.input = m.input[:len(m.input)-1]
			}
		default:
			if !m.loading && len(msg.String()) == 1 {
				m.input += msg.String()
			}
		}
	case responseMsg:
		m.loading = false
		m.messages = append(m.messages, Message{
			Role:    "assistant",
			Content: string(msg),
			Time:    "now",
		})
	case errorMsg:
		m.loading = false
		m.err = error(msg)
	}

	return m, nil
}

// View renders the UI
func (m chatModel) View() string {
	var s strings.Builder

	// Header
	s.WriteString(headerStyle.Render("🤖 Arien - AI Assistant"))
	s.WriteString("\n\n")

	// Messages
	for _, msg := range m.messages {
		if msg.Role == "user" {
			s.WriteString(userMessageStyle.Render("You: " + msg.Content))
		} else {
			s.WriteString(assistantMessageStyle.Render("Arien: " + msg.Content))
		}
		s.WriteString("\n\n")
	}

	// Loading indicator
	if m.loading {
		s.WriteString(loadingStyle.Render("Arien is thinking..."))
		s.WriteString("\n\n")
	}

	// Error display
	if m.err != nil {
		s.WriteString(errorStyle.Render(fmt.Sprintf("Error: %v", m.err)))
		s.WriteString("\n\n")
		m.err = nil // Clear error after displaying
	}

	// Input area
	s.WriteString(inputLabelStyle.Render("Message:"))
	s.WriteString("\n")
	if m.loading {
		s.WriteString(disabledInputStyle.Render(m.input))
	} else {
		s.WriteString(inputStyle.Render(m.input + "█"))
	}

	s.WriteString("\n\n")
	s.WriteString(helpStyle.Render("enter: send • ctrl+c: quit"))

	return s.String()
}

// sendMessage sends a user message and gets AI response
func (m chatModel) sendMessage() (chatModel, tea.Cmd) {
	userMessage := strings.TrimSpace(m.input)
	if userMessage == "" {
		return m, nil
	}

	// Add user message
	m.messages = append(m.messages, Message{
		Role:    "user",
		Content: userMessage,
		Time:    "now",
	})

	// Clear input and set loading
	m.input = ""
	m.loading = true

	// Send to AI
	return m, m.getAIResponse(userMessage)
}

// getAIResponse gets response from AI engine
func (m chatModel) getAIResponse(message string) tea.Cmd {
	return func() tea.Msg {
		ctx := context.Background()
		response, err := m.engine.ProcessMessage(ctx, message)
		if err != nil {
			return errorMsg(err)
		}
		return responseMsg(response.Content)
	}
}

// Messages for tea.Cmd
type responseMsg string
type errorMsg error

// Styles
var (
	headerStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#7C3AED")).
			Bold(true).
			Padding(1, 2).
			Border(lipgloss.RoundedBorder()).
			BorderForeground(lipgloss.Color("#7C3AED"))

	userMessageStyle = lipgloss.NewStyle().
				Foreground(lipgloss.Color("#10B981")).
				Padding(0, 1).
				MarginLeft(2)

	assistantMessageStyle = lipgloss.NewStyle().
				Foreground(lipgloss.Color("#3B82F6")).
				Padding(0, 1)

	loadingStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#F59E0B")).
			Italic(true)

	inputLabelStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#6B7280")).
			Bold(true)

	inputStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#FFFFFF")).
			Background(lipgloss.Color("#1F2937")).
			Padding(0, 1).
			Width(80)

	disabledInputStyle = lipgloss.NewStyle().
				Foreground(lipgloss.Color("#6B7280")).
				Background(lipgloss.Color("#374151")).
				Padding(0, 1).
				Width(80)

	helpStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#6B7280")).
			Italic(true)

	errorStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#EF4444")).
			Bold(true)
)
