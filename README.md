# Arien - Elite AI-Powered Software Engineering Assistant

[![Go Version](https://img.shields.io/badge/Go-1.24+-blue.svg)](https://golang.org)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)](#)

<PERSON><PERSON> is a sophisticated AI-powered software engineering assistant designed to revolutionize how developers work. Built with Go and featuring a modular architecture, <PERSON>en provides intelligent code assistance, project management, and automation capabilities.

## 🚀 Features

### Multi-LLM Provider Support
- **DeepSeek**: `deepseek-chat` and `deepseek-reasoner` models
- **OpenAI**: `gpt-4`, `gpt-4-turbo`, and `gpt-3.5-turbo` models  
- **Google Gemini**: `gemini-pro` and `gemini-pro-vision` models
- **Anthropic**: `claude-opus`, `claude-4-sonnet`, and `claude-3-haiku` models

### Built-in Tools
- **File Operations**: Enhanced directory listing, file reading/writing with syntax highlighting
- **Search & Analysis**: Advanced grep, glob patterns, recursive search with ranking
- **Shell Integration**: Process management, terminal interaction, command execution
- **Memory System**: Persistent fact storage and retrieval
- **Web Tools**: Search, fetch, browser automation with Playwright
- **Task Management**: Structured task planning and progress tracking
- **Diff Viewer**: Real-time code difference visualization

### Modern Terminal Interface
- **Authentication**: Secure provider selection and API key management
- **Interactive Chat**: Rich conversation interface with message history
- **Theme System**: 5 modern themes with live preview and accessibility support
- **Real-time Updates**: Live diff viewing and progress indicators

## 🛠 Installation

### Prerequisites
- Go 1.24 or later
- Terminal with 256-color support

### Build from Source
```bash
git clone https://github.com/arien-llc/arien.git
cd arien
go build -o arien cmd/arien/main.go
```

### Quick Start
```bash
# First run - authentication setup
./arien

# Interactive mode
./arien chat

# Direct command execution
./arien exec "analyze this codebase"
```

## 📖 Usage

### Authentication
On first launch, Arien will guide you through:
1. AI provider selection
2. API key configuration (securely stored)
3. Model selection and preferences

### Available Commands
```bash
arien chat          # Start interactive chat session
arien config        # Manage configuration and themes
arien tools         # List available built-in tools
arien memory        # Manage persistent memory
arien tasks         # Task management interface
arien --help        # Show all available commands
```

### Built-in Tools Usage
```bash
# File operations
ls [path] [--filter] [--sort]
read <file> [--highlight]
write <file> <content>

# Search operations  
grep <pattern> [path] [--regex] [--context]
search <query> [--recursive] [--rank]

# Shell integration
shell <command> [--background] [--timeout]

# Memory management
memory save <fact>
memory recall [query]

# Web tools
web-search <query> [--results=5]
web-fetch <url>
```

## 🎨 Themes

Arien includes 5 carefully crafted themes:
- **Dark Pro**: High contrast dark theme for extended coding sessions
- **Soft Dark**: Easy on the eyes with muted colors
- **Light Clean**: Bright, clean interface for daytime use
- **Accessibility**: Color-blind friendly with high contrast
- **Retro Terminal**: Classic green-on-black terminal aesthetic

Switch themes with: `arien config theme`

## 🔧 Configuration

Configuration is stored in `~/.arien/config.yaml`:

```yaml
# API Configuration
providers:
  openai:
    api_key: "your-api-key"
    model: "gpt-4"
  deepseek:
    api_key: "your-api-key" 
    model: "deepseek-chat"

# UI Preferences
ui:
  theme: "dark-pro"
  auto_save: true
  syntax_highlighting: true

# Tool Settings
tools:
  shell_timeout: 30
  max_file_size: "10MB"
  concurrent_limit: 5
```

## 🏗 Architecture

Arien is built with a modular architecture:

```
┌─────────────────────────────────────────┐
│                 Arien CLI               │
├─────────────────────────────────────────┤
│  UI Layer (Bubble Tea)                  │
├─────────────────────────────────────────┤
│  Core Engine & Tool Orchestrator        │
├─────────────────────────────────────────┤
│  Built-in Tools & LLM Providers         │
└─────────────────────────────────────────┘
```

## 🤝 Contributing

We welcome contributions! Please see [CONTRIBUTING.md](CONTRIBUTING.md) for guidelines.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🔗 Links

- [Documentation](https://docs.arien.dev)
- [GitHub Repository](https://github.com/arien-llc/arien)
- [Issue Tracker](https://github.com/arien-llc/arien/issues)
- [Discord Community](https://discord.gg/arien)

---

**Arien** - Intelligent, proactive, and context-aware assistance for the complete spectrum of software engineering tasks.
