/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"strings"
	"syscall"

	"arien/internal/core"
	"arien/internal/security"
	"arien/internal/ui"
	"arien/internal/utils"

	"github.com/charmbracelet/log"
	"github.com/spf13/cobra"
)

var (
	version = "1.0.0"
	commit  = "dev"
	date    = "unknown"
)

func main() {
	// Initialize logger
	logger := utils.NewLogger()
	
	// Create context with cancellation
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		<-sigChan
		logger.Info("Received shutdown signal, gracefully shutting down...")
		cancel()
	}()

	// Initialize core engine
	engine, err := core.NewEngine(ctx, logger)
	if err != nil {
		logger.Fatal("Failed to initialize core engine", "error", err)
	}

	// Create root command
	rootCmd := &cobra.Command{
		Use:     "arien",
		Short:   "Elite AI-powered software engineering assistant",
		Long:    `Arien is an elite AI-powered software engineering assistant CLI terminal tool with advanced reasoning capabilities, comprehensive tool orchestration, and deep expertise across all programming languages, frameworks, and software engineering disciplines.`,
		Version: fmt.Sprintf("%s (commit: %s, built: %s)", version, commit, date),
		RunE: func(cmd *cobra.Command, args []string) error {
			// Default behavior: start interactive mode
			return runInteractive(ctx, engine, logger)
		},
	}

	// Add subcommands
	rootCmd.AddCommand(
		createChatCommand(ctx, engine, logger),
		createConfigCommand(ctx, engine, logger),
		createToolsCommand(ctx, engine, logger),
		createMemoryCommand(ctx, engine, logger),
		createTasksCommand(ctx, engine, logger),
		createExecCommand(ctx, engine, logger),
		createAskCommand(ctx, engine, logger),
		createSearchCommand(ctx, engine, logger),
		createPerformanceCommand(ctx, engine, logger),
		createAuthCommand(ctx, engine, logger),
		createVersionCommand(),
	)

	// Execute root command
	if err := rootCmd.ExecuteContext(ctx); err != nil {
		logger.Error("Command execution failed", "error", err)
		os.Exit(1)
	}
}

func runInteractive(ctx context.Context, engine *core.Engine, logger *log.Logger) error {
	// Check if first run (no config exists)
	if !engine.Config().IsConfigured() {
		logger.Info("First run detected, starting authentication setup...")
		return runAuthSetup(ctx, engine, logger)
	}

	// Start main chat interface
	return runChat(ctx, engine, logger)
}

func runAuthSetup(ctx context.Context, engine *core.Engine, logger *log.Logger) error {
	app := ui.NewAuthApp(engine, logger)
	return app.Run(ctx)
}

func runChat(ctx context.Context, engine *core.Engine, logger *log.Logger) error {
	app := ui.NewChatApp(engine, logger)
	return app.Run(ctx)
}

func createChatCommand(ctx context.Context, engine *core.Engine, logger *log.Logger) *cobra.Command {
	return &cobra.Command{
		Use:   "chat",
		Short: "Start interactive chat session",
		Long:  "Start an interactive chat session with the AI assistant",
		RunE: func(cmd *cobra.Command, args []string) error {
			return runChat(ctx, engine, logger)
		},
	}
}

func createConfigCommand(ctx context.Context, engine *core.Engine, logger *log.Logger) *cobra.Command {
	configCmd := &cobra.Command{
		Use:   "config",
		Short: "Manage configuration and themes",
		Long:  "Manage Arien configuration, API keys, and UI themes",
	}

	configCmd.AddCommand(
		&cobra.Command{
			Use:   "show",
			Short: "Show current configuration",
			RunE: func(cmd *cobra.Command, args []string) error {
				return engine.Config().Show()
			},
		},
		&cobra.Command{
			Use:   "theme [theme-name]",
			Short: "Set or show current theme",
			RunE: func(cmd *cobra.Command, args []string) error {
				if len(args) == 0 {
					return engine.Config().ShowTheme()
				}
				return engine.Config().SetTheme(args[0])
			},
		},
		&cobra.Command{
			Use:   "reset",
			Short: "Reset configuration to defaults",
			RunE: func(cmd *cobra.Command, args []string) error {
				return engine.Config().Reset()
			},
		},
		createProviderCommands(ctx, engine, logger),
	)

	return configCmd
}

func createToolsCommand(ctx context.Context, engine *core.Engine, logger *log.Logger) *cobra.Command {
	return &cobra.Command{
		Use:   "tools",
		Short: "List available built-in tools",
		Long:  "List all available built-in tools and their descriptions",
		RunE: func(cmd *cobra.Command, args []string) error {
			return engine.Tools().List()
		},
	}
}

func createMemoryCommand(ctx context.Context, engine *core.Engine, logger *log.Logger) *cobra.Command {
	memoryCmd := &cobra.Command{
		Use:   "memory",
		Short: "Manage persistent memory",
		Long:  "Manage Arien's persistent memory system for storing facts and information",
	}

	memoryCmd.AddCommand(
		&cobra.Command{
			Use:   "save <fact>",
			Short: "Save a fact to memory",
			Args:  cobra.ExactArgs(1),
			RunE: func(cmd *cobra.Command, args []string) error {
				return engine.Memory().Save(args[0])
			},
		},
		&cobra.Command{
			Use:   "recall [query]",
			Short: "Recall facts from memory",
			RunE: func(cmd *cobra.Command, args []string) error {
				query := ""
				if len(args) > 0 {
					query = args[0]
				}
				return engine.Memory().Recall(query)
			},
		},
		&cobra.Command{
			Use:   "clear",
			Short: "Clear all memory",
			RunE: func(cmd *cobra.Command, args []string) error {
				return engine.Memory().Clear()
			},
		},
	)

	return memoryCmd
}

func createTasksCommand(ctx context.Context, engine *core.Engine, logger *log.Logger) *cobra.Command {
	return &cobra.Command{
		Use:   "tasks",
		Short: "Task management interface",
		Long:  "Open the task management interface for planning and tracking work",
		RunE: func(cmd *cobra.Command, args []string) error {
			app := ui.NewTaskApp(engine, logger)
			return app.Run(ctx)
		},
	}
}

func createExecCommand(ctx context.Context, engine *core.Engine, logger *log.Logger) *cobra.Command {
	return &cobra.Command{
		Use:   "exec <command>",
		Short: "Execute a direct command",
		Long:  "Execute a direct command without entering interactive mode",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			return engine.ExecuteCommand(ctx, args[0])
		},
	}
}

// createAskCommand creates the ask command
func createAskCommand(ctx context.Context, engine *core.Engine, logger *log.Logger) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "ask [question]",
		Short: "Ask a question to the AI assistant",
		Long:  "Ask a question to the AI assistant and get an immediate response",
		Args:  cobra.MinimumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			question := strings.Join(args, " ")

			response, err := engine.ProcessMessage(ctx, question)
			if err != nil {
				return fmt.Errorf("failed to process question: %w", err)
			}

			fmt.Println(response.Content)
			return nil
		},
	}

	return cmd
}

// createSearchCommand creates the search command
func createSearchCommand(ctx context.Context, engine *core.Engine, logger *log.Logger) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "search [query]",
		Short: "Search files and directories",
		Long:  "Search for files and content in the current directory and subdirectories",
		Args:  cobra.MinimumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			query := strings.Join(args, " ")

			path, _ := cmd.Flags().GetString("path")
			contentSearch, _ := cmd.Flags().GetBool("content-search")
			maxResults, _ := cmd.Flags().GetInt("max-results")

			result := engine.Tools().ExecuteTool(ctx, "search", map[string]interface{}{
				"query":          query,
				"path":           path,
				"content_search": contentSearch,
				"max_results":    maxResults,
			})

			if result.Error != nil {
				return fmt.Errorf("search failed: %w", result.Error)
			}

			fmt.Print(result.Output)
			return nil
		},
	}

	cmd.Flags().StringP("path", "p", ".", "Directory to search")
	cmd.Flags().BoolP("content-search", "c", true, "Search within file contents")
	cmd.Flags().IntP("max-results", "m", 50, "Maximum number of results")

	return cmd
}

// createPerformanceCommand creates the performance command
func createPerformanceCommand(ctx context.Context, engine *core.Engine, logger *log.Logger) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "performance [action]",
		Short: "Monitor and optimize performance",
		Long:  "Monitor system performance and run optimization routines",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			action := args[0]

			result := engine.Tools().ExecuteTool(ctx, "performance", map[string]interface{}{
				"action": action,
			})

			if result.Error != nil {
				return fmt.Errorf("performance command failed: %w", result.Error)
			}

			fmt.Print(result.Output)
			return nil
		},
	}

	return cmd
}

// createAuthCommand creates the auth command
func createAuthCommand(ctx context.Context, engine *core.Engine, logger *log.Logger) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "auth",
		Short: "Configure authentication",
		Long:  "Set up AI provider authentication and configuration",
		RunE: func(cmd *cobra.Command, args []string) error {
			authApp := ui.NewAuthApp(engine, logger)
			return authApp.Run(ctx)
		},
	}

	return cmd
}

// createVersionCommand creates the version command
func createVersionCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "version",
		Short: "Show version information",
		Long:  "Display version, build information, and system details",
		Run: func(cmd *cobra.Command, args []string) {
			fmt.Printf("Arien %s\n", version)
			fmt.Printf("Commit: %s\n", commit)
			fmt.Printf("Built: %s\n", date)
		},
	}

	return cmd
}

// createProviderCommands creates provider management commands
func createProviderCommands(ctx context.Context, engine *core.Engine, logger *log.Logger) *cobra.Command {
	providerCmd := &cobra.Command{
		Use:   "provider",
		Short: "Manage AI provider configurations",
		Long:  "Manage AI provider configurations including API keys, models, and settings",
	}

	providerCmd.AddCommand(
		&cobra.Command{
			Use:   "list",
			Short: "List configured providers",
			RunE: func(cmd *cobra.Command, args []string) error {
				providers := engine.Config().ListProviders()
				if len(providers) == 0 {
					fmt.Println("No providers configured.")
					return nil
				}

				fmt.Println("Configured Providers:")
				for name, config := range providers {
					fmt.Printf("\n%s:\n", strings.Title(name))
					fmt.Printf("  Model: %s\n", config.Model)
					fmt.Printf("  API Key: %s\n", config.APIKey)
					if config.BaseURL != "" {
						fmt.Printf("  Base URL: %s\n", config.BaseURL)
					}
					if config.RateLimit > 0 {
						fmt.Printf("  Rate Limit: %d/min\n", config.RateLimit)
					}
				}
				return nil
			},
		},
		&cobra.Command{
			Use:   "rotate <provider>",
			Short: "Rotate API key for a provider",
			Long:  "Rotate the API key for a specific provider with validation",
			Args:  cobra.ExactArgs(1),
			RunE: func(cmd *cobra.Command, args []string) error {
				provider := args[0]

				// Check if provider exists
				if _, exists := engine.Config().GetProvider(provider); !exists {
					return fmt.Errorf("provider %s is not configured", provider)
				}

				// Prompt for new API key
				fmt.Printf("Enter new API key for %s: ", provider)
				var newAPIKey string
				if _, err := fmt.Scanln(&newAPIKey); err != nil {
					return fmt.Errorf("failed to read API key: %w", err)
				}

				// Rotate the key
				if err := engine.Config().RotateProviderAPIKey(provider, newAPIKey); err != nil {
					return fmt.Errorf("failed to rotate API key: %w", err)
				}

				fmt.Printf("✅ API key for %s rotated successfully!\n", provider)
				return nil
			},
		},
		&cobra.Command{
			Use:   "remove <provider>",
			Short: "Remove a provider configuration",
			Args:  cobra.ExactArgs(1),
			RunE: func(cmd *cobra.Command, args []string) error {
				provider := args[0]

				// Confirm removal
				fmt.Printf("Are you sure you want to remove provider %s? (y/N): ", provider)
				var confirm string
				fmt.Scanln(&confirm)

				if strings.ToLower(confirm) != "y" && strings.ToLower(confirm) != "yes" {
					fmt.Println("Operation cancelled.")
					return nil
				}

				if err := engine.Config().RemoveProvider(provider); err != nil {
					return fmt.Errorf("failed to remove provider: %w", err)
				}

				fmt.Printf("✅ Provider %s removed successfully!\n", provider)
				return nil
			},
		},
		&cobra.Command{
			Use:   "test <provider>",
			Short: "Test a provider's API key",
			Args:  cobra.ExactArgs(1),
			RunE: func(cmd *cobra.Command, args []string) error {
				provider := args[0]

				// Get provider config
				config, exists := engine.Config().GetProvider(provider)
				if !exists {
					return fmt.Errorf("provider %s is not configured", provider)
				}

				// Test the API key
				validator := security.NewValidator()
				if err := validator.TestAPIKey(ctx, provider, config.APIKey, config.Model); err != nil {
					fmt.Printf("❌ API key test failed: %v\n", err)
					return nil
				}

				fmt.Printf("✅ API key for %s is valid!\n", provider)
				return nil
			},
		},
	)

	return providerCmd
}
