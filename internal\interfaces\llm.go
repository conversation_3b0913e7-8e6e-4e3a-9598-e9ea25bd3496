/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package interfaces

import (
	"context"
	"time"
)

// Provider defines the interface for LLM providers
type Provider interface {
	// Name returns the provider name
	Name() string
	
	// Models returns available models for this provider
	Models() []string
	
	// ProcessMessage processes a message and returns a response
	ProcessMessage(ctx context.Context, request *Request) (*Response, error)
	
	// SupportsToolCalling returns true if the provider supports function calling
	SupportsToolCalling() bool
	
	// SupportsStreaming returns true if the provider supports streaming responses
	SupportsStreaming() bool
	
	// Shutdown gracefully shuts down the provider
	Shutdown(ctx context.Context) error
}

// Request represents a request to an LLM provider
type Request struct {
	Messages    []Message              `json:"messages"`
	Model       string                 `json:"model"`
	Tools       []Tool                 `json:"tools,omitempty"`
	Temperature float64                `json:"temperature,omitempty"`
	MaxTokens   int                    `json:"max_tokens,omitempty"`
	Stream      bool                   `json:"stream,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// Response represents a response from an LLM provider
type Response struct {
	Content     string                 `json:"content"`
	Model       string                 `json:"model"`
	Provider    string                 `json:"provider"`
	ToolCalls   []ToolCall             `json:"tool_calls,omitempty"`
	Usage       Usage                  `json:"usage,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Error       error                  `json:"error,omitempty"`
	Timestamp   time.Time              `json:"timestamp"`
}

// Message represents a chat message
type Message struct {
	Role      string                 `json:"role"`      // "system", "user", "assistant", "tool"
	Content   string                 `json:"content"`
	Name      string                 `json:"name,omitempty"`
	ToolCalls []ToolCall             `json:"tool_calls,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// Tool represents a function tool definition
type Tool struct {
	Type     string       `json:"type"`     // "function"
	Function ToolFunction `json:"function"`
}

// ToolFunction represents a function definition
type ToolFunction struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
}

// ToolCall represents a function call from the LLM
type ToolCall struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"` // "function"
	Name      string                 `json:"name"`
	Arguments map[string]interface{} `json:"arguments"`
}

// Usage represents token usage information
type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// MessageRole constants
const (
	RoleSystem    = "system"
	RoleUser      = "user"
	RoleAssistant = "assistant"
	RoleTool      = "tool"
)

// ToolType constants
const (
	ToolTypeFunction = "function"
)

// NewMessage creates a new message
func NewMessage(role, content string) Message {
	return Message{
		Role:    role,
		Content: content,
	}
}

// NewSystemMessage creates a new system message
func NewSystemMessage(content string) Message {
	return NewMessage(RoleSystem, content)
}

// NewUserMessage creates a new user message
func NewUserMessage(content string) Message {
	return NewMessage(RoleUser, content)
}

// NewAssistantMessage creates a new assistant message
func NewAssistantMessage(content string) Message {
	return NewMessage(RoleAssistant, content)
}

// NewToolMessage creates a new tool message
func NewToolMessage(content, toolCallID string) Message {
	return Message{
		Role:    RoleTool,
		Content: content,
		Name:    toolCallID,
	}
}

// AddToolCall adds a tool call to the message
func (m *Message) AddToolCall(toolCall ToolCall) {
	if m.ToolCalls == nil {
		m.ToolCalls = make([]ToolCall, 0)
	}
	m.ToolCalls = append(m.ToolCalls, toolCall)
}

// IsError returns true if the response contains an error
func (r *Response) IsError() bool {
	return r.Error != nil
}

// HasToolCalls returns true if the response contains tool calls
func (r *Response) HasToolCalls() bool {
	return len(r.ToolCalls) > 0
}
