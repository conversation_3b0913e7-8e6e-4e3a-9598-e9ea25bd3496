/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package chat

import (
	"context"
	"strings"
	"time"

	tea "github.com/charmbracelet/bubbletea"
)

// Update implements tea.Model
func (m *ChatModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.KeyMsg:
		cmd := m.handleKeyPress(msg)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

	case tea.WindowSizeMsg:
		m.handleResize(msg.Width, msg.Height)

	case MessageSentMsg:
		m.handleMessageSent(msg.Message)

	case MessageReceivedMsg:
		cmd := m.handleMessageReceived(msg.Message)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

	case StreamingChunkMsg:
		m.handleStreamingChunk(msg.Chunk)

	case StreamingCompleteMsg:
		m.handleStreamingComplete()

	case ErrorMsg:
		m.handleError(msg.Error)

	case SessionLoadedMsg:
		m.handleSessionLoaded(msg.Session)

	case SessionSavedMsg:
		m.handleSessionSaved(msg.Session)

	case AttachmentAddedMsg:
		m.handleAttachmentAdded(msg.Attachment)

	case ConfigUpdatedMsg:
		m.handleConfigUpdated(msg.Config)

	case ResizeMsg:
		m.handleResize(msg.Width, msg.Height)
	}

	m.lastUpdate = time.Now()
	return m, tea.Batch(cmds...)
}

// handleKeyPress processes keyboard input
func (m *ChatModel) handleKeyPress(msg tea.KeyMsg) tea.Cmd {
	key := msg.String()

	// Handle global shortcuts first
	if m.isKeyInMap(key, m.keyMap.Quit) {
		return tea.Quit
	}

	if m.isKeyInMap(key, m.keyMap.NewSession) {
		return m.createNewSessionCmd()
	}

	if m.isKeyInMap(key, m.keyMap.SaveSession) {
		return m.saveSessionCmd()
	}

	if m.isKeyInMap(key, m.keyMap.LoadSession) {
		return m.loadSessionCmd()
	}

	if m.isKeyInMap(key, m.keyMap.ToggleMetadata) {
		m.state.ShowMetadata = !m.state.ShowMetadata
		return nil
	}

	if m.isKeyInMap(key, m.keyMap.ToggleDebug) {
		if m.state.ViewMode == ViewModeDebug {
			m.state.ViewMode = ViewModeNormal
		} else {
			m.state.ViewMode = ViewModeDebug
		}
		return nil
	}

	if m.isKeyInMap(key, m.keyMap.AddAttachment) {
		return m.addAttachmentCmd()
	}

	// Handle input field shortcuts
	if m.isKeyInMap(key, m.keyMap.Send) && !m.state.IsLoading {
		return m.sendMessageCmd()
	}

	if m.isKeyInMap(key, m.keyMap.ClearInput) {
		m.clearInput()
		return nil
	}

	// Handle viewport navigation
	if m.isKeyInMap(key, m.keyMap.ScrollUp) {
		m.scrollUp()
		return nil
	}

	if m.isKeyInMap(key, m.keyMap.ScrollDown) {
		m.scrollDown()
		return nil
	}

	if m.isKeyInMap(key, m.keyMap.PreviousMessage) {
		m.selectPreviousMessage()
		return nil
	}

	if m.isKeyInMap(key, m.keyMap.NextMessage) {
		m.selectNextMessage()
		return nil
	}

	// Handle text input
	if !m.state.IsLoading {
		return m.handleTextInput(msg)
	}

	return nil
}

// handleTextInput processes text input for the input field
func (m *ChatModel) handleTextInput(msg tea.KeyMsg) tea.Cmd {
	key := msg.String()

	switch key {
	case "backspace":
		if len(m.inputField.Value) > 0 && m.inputField.CursorPos > 0 {
			if m.inputField.CursorPos >= len(m.inputField.Value) {
				m.inputField.Value = m.inputField.Value[:len(m.inputField.Value)-1]
				m.inputField.CursorPos = len(m.inputField.Value)
			} else {
				m.inputField.Value = m.inputField.Value[:m.inputField.CursorPos-1] + 
					m.inputField.Value[m.inputField.CursorPos:]
				m.inputField.CursorPos--
			}
		}

	case "delete":
		if m.inputField.CursorPos < len(m.inputField.Value) {
			m.inputField.Value = m.inputField.Value[:m.inputField.CursorPos] + 
				m.inputField.Value[m.inputField.CursorPos+1:]
		}

	case "left":
		if m.inputField.CursorPos > 0 {
			m.inputField.CursorPos--
		}

	case "right":
		if m.inputField.CursorPos < len(m.inputField.Value) {
			m.inputField.CursorPos++
		}

	case "home":
		m.inputField.CursorPos = 0

	case "end":
		m.inputField.CursorPos = len(m.inputField.Value)

	case "ctrl+w":
		// Delete word backwards
		m.deleteWordBackward()

	case "ctrl+u":
		// Delete to beginning of line
		m.inputField.Value = m.inputField.Value[m.inputField.CursorPos:]
		m.inputField.CursorPos = 0

	case "ctrl+k":
		// Delete to end of line
		m.inputField.Value = m.inputField.Value[:m.inputField.CursorPos]

	case "tab":
		// Auto-complete or insert tab
		return m.handleAutoComplete()

	default:
		// Regular character input
		if len(key) == 1 && len(m.inputField.Value) < m.inputField.MaxLength {
			if m.inputField.CursorPos >= len(m.inputField.Value) {
				m.inputField.Value += key
			} else {
				m.inputField.Value = m.inputField.Value[:m.inputField.CursorPos] + 
					key + m.inputField.Value[m.inputField.CursorPos:]
			}
			m.inputField.CursorPos++
		}
	}

	// Update state
	m.state.InputBuffer = m.inputField.Value
	return nil
}

// Command functions

// sendMessageCmd creates a command to send a message
func (m *ChatModel) sendMessageCmd() tea.Cmd {
	if m.inputField.Value == "" {
		return nil
	}

	message := strings.TrimSpace(m.inputField.Value)
	if message == "" {
		return nil
	}

	// Validate input
	if err := m.messageProcessor.ValidateInput(message); err != nil {
		m.state.LastError = err
		return nil
	}

	// Sanitize input
	message = m.messageProcessor.SanitizeInput(message)

	// Add to history
	m.addToHistory(message)

	// Clear input
	m.clearInput()

	// Set loading state
	m.state.IsLoading = true

	return func() tea.Msg {
		ctx := context.Background()
		
		// Process the message
		response, err := m.messageProcessor.ProcessMessage(ctx, message, m.state.CurrentSession)
		if err != nil {
			return ErrorMsg{Error: err}
		}

		return MessageReceivedMsg{Message: response}
	}
}

// createNewSessionCmd creates a new chat session
func (m *ChatModel) createNewSessionCmd() tea.Cmd {
	return func() tea.Msg {
		session, err := m.sessionManager.CreateSession("New Chat")
		if err != nil {
			return ErrorMsg{Error: err}
		}
		return SessionLoadedMsg{Session: session}
	}
}

// saveSessionCmd saves the current session
func (m *ChatModel) saveSessionCmd() tea.Cmd {
	if m.state.CurrentSession == nil {
		return nil
	}

	return func() tea.Msg {
		err := m.sessionManager.SaveSession(m.state.CurrentSession)
		if err != nil {
			return ErrorMsg{Error: err}
		}
		return SessionSavedMsg{Session: m.state.CurrentSession}
	}
}

// loadSessionCmd loads a session (placeholder for session selection UI)
func (m *ChatModel) loadSessionCmd() tea.Cmd {
	return func() tea.Msg {
		// This would typically open a session selection dialog
		// For now, just return nil
		return nil
	}
}

// addAttachmentCmd adds a file attachment
func (m *ChatModel) addAttachmentCmd() tea.Cmd {
	return func() tea.Msg {
		// This would typically open a file selection dialog
		// For now, just return nil
		return nil
	}
}

// handleAutoComplete handles tab completion
func (m *ChatModel) handleAutoComplete() tea.Cmd {
	// Implement auto-completion logic here
	return nil
}

// Event handlers

// handleMessageSent handles when a message is sent
func (m *ChatModel) handleMessageSent(message *Message) {
	if m.state.CurrentSession != nil {
		m.state.CurrentSession.Messages = append(m.state.CurrentSession.Messages, *message)
		m.updateViewport()
		m.eventHandler.OnMessageSent(message)
	}
}

// handleMessageReceived handles when a message is received
func (m *ChatModel) handleMessageReceived(message *Message) tea.Cmd {
	m.state.IsLoading = false
	
	if m.state.CurrentSession != nil {
		m.state.CurrentSession.Messages = append(m.state.CurrentSession.Messages, *message)
		m.updateViewport()
		m.eventHandler.OnMessageReceived(message)
		
		// Auto-save after receiving message
		return m.saveSessionCmd()
	}
	
	return nil
}

// handleStreamingChunk handles streaming response chunks
func (m *ChatModel) handleStreamingChunk(chunk string) {
	if m.state.CurrentSession != nil && len(m.state.CurrentSession.Messages) > 0 {
		// Update the last message with the new chunk
		lastIdx := len(m.state.CurrentSession.Messages) - 1
		lastMsg := &m.state.CurrentSession.Messages[lastIdx]
		
		if lastMsg.Role == "assistant" && lastMsg.Status == MessageStatusStreaming {
			lastMsg.Content += chunk
			m.updateViewport()
			m.eventHandler.OnStreamingChunk(chunk)
		}
	}
}

// handleStreamingComplete handles when streaming is complete
func (m *ChatModel) handleStreamingComplete() {
	m.state.IsStreaming = false
	
	if m.state.CurrentSession != nil && len(m.state.CurrentSession.Messages) > 0 {
		lastIdx := len(m.state.CurrentSession.Messages) - 1
		lastMsg := &m.state.CurrentSession.Messages[lastIdx]
		
		if lastMsg.Role == "assistant" && lastMsg.Status == MessageStatusStreaming {
			lastMsg.Status = MessageStatusDelivered
			m.eventHandler.OnStreamingCompleted()
		}
	}
}

// handleError handles error messages
func (m *ChatModel) handleError(err error) {
	m.state.LastError = err
	m.state.IsLoading = false
	m.state.IsStreaming = false
	m.eventHandler.OnError(err)
}

// handleSessionLoaded handles when a session is loaded
func (m *ChatModel) handleSessionLoaded(session *ChatSession) {
	m.state.CurrentSession = session
	m.sessionManager.SetCurrentSession(session)
	m.updateViewport()
	m.eventHandler.OnSessionChanged(session)
}

// handleSessionSaved handles when a session is saved
func (m *ChatModel) handleSessionSaved(session *ChatSession) {
	m.statusBar.LastSaved = time.Now()
}

// handleAttachmentAdded handles when an attachment is added
func (m *ChatModel) handleAttachmentAdded(attachment *Attachment) {
	// Update UI to show attachment
}

// handleConfigUpdated handles when configuration is updated
func (m *ChatModel) handleConfigUpdated(config *ChatConfig) {
	m.deps.Config = config
}

// handleResize handles window resize events
func (m *ChatModel) handleResize(width, height int) {
	m.viewport.Width = width - 4  // Account for padding
	m.viewport.Height = height - 8 // Account for header, input, and status
	m.updateViewport()
}

// Utility functions

// isKeyInMap checks if a key is in a key map
func (m *ChatModel) isKeyInMap(key string, keyMap []string) bool {
	for _, k := range keyMap {
		if k == key {
			return true
		}
	}
	return false
}

// clearInput clears the input field
func (m *ChatModel) clearInput() {
	m.inputField.Value = ""
	m.inputField.CursorPos = 0
	m.state.InputBuffer = ""
}

// addToHistory adds a message to input history
func (m *ChatModel) addToHistory(message string) {
	m.inputField.History = append(m.inputField.History, message)
	if len(m.inputField.History) > 100 { // Limit history size
		m.inputField.History = m.inputField.History[1:]
	}
	m.inputField.HistoryIndex = len(m.inputField.History)
}

// deleteWordBackward deletes a word backward from cursor
func (m *ChatModel) deleteWordBackward() {
	if m.inputField.CursorPos == 0 {
		return
	}

	// Find the start of the current word
	pos := m.inputField.CursorPos - 1
	for pos > 0 && m.inputField.Value[pos] == ' ' {
		pos--
	}
	for pos > 0 && m.inputField.Value[pos] != ' ' {
		pos--
	}
	if m.inputField.Value[pos] == ' ' {
		pos++
	}

	// Delete from pos to cursor
	m.inputField.Value = m.inputField.Value[:pos] + m.inputField.Value[m.inputField.CursorPos:]
	m.inputField.CursorPos = pos
}

// scrollUp scrolls the viewport up
func (m *ChatModel) scrollUp() {
	if m.viewport.ScrollOffset > 0 {
		m.viewport.ScrollOffset--
	}
}

// scrollDown scrolls the viewport down
func (m *ChatModel) scrollDown() {
	if m.viewport.ScrollOffset < m.viewport.MaxScroll {
		m.viewport.ScrollOffset++
	}
}

// selectPreviousMessage selects the previous message
func (m *ChatModel) selectPreviousMessage() {
	if m.state.SelectedMessage > 0 {
		m.state.SelectedMessage--
	}
}

// selectNextMessage selects the next message
func (m *ChatModel) selectNextMessage() {
	maxIndex := len(m.viewport.Messages) - 1
	if m.state.SelectedMessage < maxIndex {
		m.state.SelectedMessage++
	}
}
