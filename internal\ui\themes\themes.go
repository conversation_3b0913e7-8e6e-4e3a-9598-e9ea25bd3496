/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package themes

import (
	"github.com/charmbracelet/lipgloss"
)

// Theme represents a complete UI theme
type Theme struct {
	Name        string
	Description string
	Colors      ColorScheme
	Styles      StyleScheme
}

// ColorScheme defines the color palette for a theme
type ColorScheme struct {
	// Primary colors
	Primary       lipgloss.Color
	Secondary     lipgloss.Color
	Accent        lipgloss.Color
	
	// Background colors
	Background    lipgloss.Color
	Surface       lipgloss.Color
	Card          lipgloss.Color
	
	// Text colors
	Text          lipgloss.Color
	TextSecondary lipgloss.Color
	TextMuted     lipgloss.Color
	
	// Status colors
	Success       lipgloss.Color
	Warning       lipgloss.Color
	Error         lipgloss.Color
	Info          lipgloss.Color
	
	// Border colors
	Border        lipgloss.Color
	BorderFocus   lipgloss.Color
	BorderActive  lipgloss.Color
	
	// Special colors
	Highlight     lipgloss.Color
	Selection     lipgloss.Color
	Cursor        lipgloss.Color
}

// StyleScheme defines the styling rules for a theme
type StyleScheme struct {
	// Base styles
	Base          lipgloss.Style
	Container     lipgloss.Style
	Card          lipgloss.Style
	
	// Text styles
	Title         lipgloss.Style
	Subtitle      lipgloss.Style
	Body          lipgloss.Style
	Caption       lipgloss.Style
	Code          lipgloss.Style
	
	// Interactive styles
	Button        lipgloss.Style
	ButtonActive  lipgloss.Style
	Input         lipgloss.Style
	InputFocus    lipgloss.Style
	
	// Status styles
	Success       lipgloss.Style
	Warning       lipgloss.Style
	Error         lipgloss.Style
	Info          lipgloss.Style
	
	// Layout styles
	Header        lipgloss.Style
	Footer        lipgloss.Style
	Sidebar       lipgloss.Style
	Content       lipgloss.Style
	
	// Special styles
	Highlight     lipgloss.Style
	Selection     lipgloss.Style
	Progress      lipgloss.Style
}

// Available themes
var (
	DarkProTheme      = createDarkProTheme()
	DarkSoftTheme     = createDarkSoftTheme()
	LightTheme        = createLightTheme()
	HighContrastTheme = createHighContrastTheme()
	ColorBlindTheme   = createColorBlindTheme()
)

// GetAllThemes returns all available themes
func GetAllThemes() map[string]Theme {
	return map[string]Theme{
		"dark-pro":      DarkProTheme,
		"dark-soft":     DarkSoftTheme,
		"light":         LightTheme,
		"high-contrast": HighContrastTheme,
		"colorblind":    ColorBlindTheme,
	}
}

// GetTheme returns a theme by name
func GetTheme(name string) (Theme, bool) {
	themes := GetAllThemes()
	theme, exists := themes[name]
	return theme, exists
}

// GetDefaultTheme returns the default theme
func GetDefaultTheme() Theme {
	return DarkProTheme
}

// createDarkProTheme creates the Dark Pro theme
func createDarkProTheme() Theme {
	colors := ColorScheme{
		Primary:       lipgloss.Color("#3b82f6"),
		Secondary:     lipgloss.Color("#6366f1"),
		Accent:        lipgloss.Color("#8b5cf6"),
		Background:    lipgloss.Color("#0f172a"),
		Surface:       lipgloss.Color("#1e293b"),
		Card:          lipgloss.Color("#334155"),
		Text:          lipgloss.Color("#f8fafc"),
		TextSecondary: lipgloss.Color("#cbd5e1"),
		TextMuted:     lipgloss.Color("#64748b"),
		Success:       lipgloss.Color("#10b981"),
		Warning:       lipgloss.Color("#f59e0b"),
		Error:         lipgloss.Color("#ef4444"),
		Info:          lipgloss.Color("#06b6d4"),
		Border:        lipgloss.Color("#475569"),
		BorderFocus:   lipgloss.Color("#3b82f6"),
		BorderActive:  lipgloss.Color("#8b5cf6"),
		Highlight:     lipgloss.Color("#fbbf24"),
		Selection:     lipgloss.Color("#3730a3"),
		Cursor:        lipgloss.Color("#f8fafc"),
	}
	
	styles := createStyleScheme(colors)
	
	return Theme{
		Name:        "Dark Pro",
		Description: "Professional dark theme with blue accents",
		Colors:      colors,
		Styles:      styles,
	}
}

// createDarkSoftTheme creates the Dark Soft theme
func createDarkSoftTheme() Theme {
	colors := ColorScheme{
		Primary:       lipgloss.Color("#4ade80"),
		Secondary:     lipgloss.Color("#22d3ee"),
		Accent:        lipgloss.Color("#a78bfa"),
		Background:    lipgloss.Color("#1a1a1a"),
		Surface:       lipgloss.Color("#2d2d2d"),
		Card:          lipgloss.Color("#404040"),
		Text:          lipgloss.Color("#f5f5f5"),
		TextSecondary: lipgloss.Color("#d4d4d4"),
		TextMuted:     lipgloss.Color("#a3a3a3"),
		Success:       lipgloss.Color("#22c55e"),
		Warning:       lipgloss.Color("#eab308"),
		Error:         lipgloss.Color("#f87171"),
		Info:          lipgloss.Color("#38bdf8"),
		Border:        lipgloss.Color("#525252"),
		BorderFocus:   lipgloss.Color("#4ade80"),
		BorderActive:  lipgloss.Color("#a78bfa"),
		Highlight:     lipgloss.Color("#fde047"),
		Selection:     lipgloss.Color("#166534"),
		Cursor:        lipgloss.Color("#f5f5f5"),
	}
	
	styles := createStyleScheme(colors)
	
	return Theme{
		Name:        "Dark Soft",
		Description: "Soft dark theme with green accents",
		Colors:      colors,
		Styles:      styles,
	}
}

// createLightTheme creates the Light theme
func createLightTheme() Theme {
	colors := ColorScheme{
		Primary:       lipgloss.Color("#2563eb"),
		Secondary:     lipgloss.Color("#7c3aed"),
		Accent:        lipgloss.Color("#dc2626"),
		Background:    lipgloss.Color("#ffffff"),
		Surface:       lipgloss.Color("#f8fafc"),
		Card:          lipgloss.Color("#f1f5f9"),
		Text:          lipgloss.Color("#0f172a"),
		TextSecondary: lipgloss.Color("#334155"),
		TextMuted:     lipgloss.Color("#64748b"),
		Success:       lipgloss.Color("#059669"),
		Warning:       lipgloss.Color("#d97706"),
		Error:         lipgloss.Color("#dc2626"),
		Info:          lipgloss.Color("#0284c7"),
		Border:        lipgloss.Color("#cbd5e1"),
		BorderFocus:   lipgloss.Color("#2563eb"),
		BorderActive:  lipgloss.Color("#dc2626"),
		Highlight:     lipgloss.Color("#fbbf24"),
		Selection:     lipgloss.Color("#dbeafe"),
		Cursor:        lipgloss.Color("#0f172a"),
	}
	
	styles := createStyleScheme(colors)
	
	return Theme{
		Name:        "Light",
		Description: "Clean light theme for bright environments",
		Colors:      colors,
		Styles:      styles,
	}
}

// createHighContrastTheme creates the High Contrast theme
func createHighContrastTheme() Theme {
	colors := ColorScheme{
		Primary:       lipgloss.Color("#ffffff"),
		Secondary:     lipgloss.Color("#ffff00"),
		Accent:        lipgloss.Color("#00ffff"),
		Background:    lipgloss.Color("#000000"),
		Surface:       lipgloss.Color("#1a1a1a"),
		Card:          lipgloss.Color("#333333"),
		Text:          lipgloss.Color("#ffffff"),
		TextSecondary: lipgloss.Color("#cccccc"),
		TextMuted:     lipgloss.Color("#999999"),
		Success:       lipgloss.Color("#00ff00"),
		Warning:       lipgloss.Color("#ffff00"),
		Error:         lipgloss.Color("#ff0000"),
		Info:          lipgloss.Color("#00ffff"),
		Border:        lipgloss.Color("#ffffff"),
		BorderFocus:   lipgloss.Color("#ffff00"),
		BorderActive:  lipgloss.Color("#00ffff"),
		Highlight:     lipgloss.Color("#ffff00"),
		Selection:     lipgloss.Color("#0000ff"),
		Cursor:        lipgloss.Color("#ffffff"),
	}
	
	styles := createStyleScheme(colors)
	
	return Theme{
		Name:        "High Contrast",
		Description: "High contrast theme for accessibility",
		Colors:      colors,
		Styles:      styles,
	}
}

// createColorBlindTheme creates the Color Blind Friendly theme
func createColorBlindTheme() Theme {
	colors := ColorScheme{
		Primary:       lipgloss.Color("#0173b2"), // Blue
		Secondary:     lipgloss.Color("#de8f05"), // Orange
		Accent:        lipgloss.Color("#cc78bc"), // Pink
		Background:    lipgloss.Color("#1a1a1a"),
		Surface:       lipgloss.Color("#2d2d2d"),
		Card:          lipgloss.Color("#404040"),
		Text:          lipgloss.Color("#f5f5f5"),
		TextSecondary: lipgloss.Color("#d4d4d4"),
		TextMuted:     lipgloss.Color("#a3a3a3"),
		Success:       lipgloss.Color("#029e73"), // Green (colorblind safe)
		Warning:       lipgloss.Color("#de8f05"), // Orange
		Error:         lipgloss.Color("#d55e00"), // Red-orange
		Info:          lipgloss.Color("#0173b2"), // Blue
		Border:        lipgloss.Color("#525252"),
		BorderFocus:   lipgloss.Color("#0173b2"),
		BorderActive:  lipgloss.Color("#de8f05"),
		Highlight:     lipgloss.Color("#fbf1c7"),
		Selection:     lipgloss.Color("#3c3836"),
		Cursor:        lipgloss.Color("#f5f5f5"),
	}
	
	styles := createStyleScheme(colors)
	
	return Theme{
		Name:        "Color Blind Friendly",
		Description: "Accessible theme for color vision deficiency",
		Colors:      colors,
		Styles:      styles,
	}
}

// createStyleScheme creates a style scheme from a color scheme
func createStyleScheme(colors ColorScheme) StyleScheme {
	return StyleScheme{
		Base: lipgloss.NewStyle().
			Foreground(colors.Text).
			Background(colors.Background),
		
		Container: lipgloss.NewStyle().
			Background(colors.Surface).
			Border(lipgloss.RoundedBorder()).
			BorderForeground(colors.Border).
			Padding(1),
		
		Card: lipgloss.NewStyle().
			Background(colors.Card).
			Border(lipgloss.RoundedBorder()).
			BorderForeground(colors.Border).
			Padding(1).
			Margin(1),
		
		Title: lipgloss.NewStyle().
			Foreground(colors.Primary).
			Bold(true).
			Underline(true),
		
		Subtitle: lipgloss.NewStyle().
			Foreground(colors.Secondary).
			Bold(true),
		
		Body: lipgloss.NewStyle().
			Foreground(colors.Text),
		
		Caption: lipgloss.NewStyle().
			Foreground(colors.TextMuted).
			Italic(true),
		
		Code: lipgloss.NewStyle().
			Foreground(colors.Accent).
			Background(colors.Surface).
			Padding(0, 1),
		
		Button: lipgloss.NewStyle().
			Foreground(colors.Background).
			Background(colors.Primary).
			Padding(0, 2).
			Border(lipgloss.RoundedBorder()),
		
		ButtonActive: lipgloss.NewStyle().
			Foreground(colors.Background).
			Background(colors.Accent).
			Padding(0, 2).
			Border(lipgloss.RoundedBorder()).
			Bold(true),
		
		Input: lipgloss.NewStyle().
			Foreground(colors.Text).
			Background(colors.Surface).
			Border(lipgloss.RoundedBorder()).
			BorderForeground(colors.Border).
			Padding(0, 1),
		
		InputFocus: lipgloss.NewStyle().
			Foreground(colors.Text).
			Background(colors.Surface).
			Border(lipgloss.RoundedBorder()).
			BorderForeground(colors.BorderFocus).
			Padding(0, 1),
		
		Success: lipgloss.NewStyle().
			Foreground(colors.Success).
			Bold(true),
		
		Warning: lipgloss.NewStyle().
			Foreground(colors.Warning).
			Bold(true),
		
		Error: lipgloss.NewStyle().
			Foreground(colors.Error).
			Bold(true),
		
		Info: lipgloss.NewStyle().
			Foreground(colors.Info).
			Bold(true),
		
		Header: lipgloss.NewStyle().
			Foreground(colors.Text).
			Background(colors.Primary).
			Bold(true).
			Padding(0, 1),
		
		Footer: lipgloss.NewStyle().
			Foreground(colors.TextMuted).
			Background(colors.Surface).
			Padding(0, 1),
		
		Sidebar: lipgloss.NewStyle().
			Background(colors.Surface).
			Border(lipgloss.RoundedBorder()).
			BorderForeground(colors.Border).
			Padding(1),
		
		Content: lipgloss.NewStyle().
			Background(colors.Background).
			Padding(1),
		
		Highlight: lipgloss.NewStyle().
			Background(colors.Highlight).
			Foreground(colors.Background).
			Bold(true),
		
		Selection: lipgloss.NewStyle().
			Background(colors.Selection).
			Foreground(colors.Text),
		
		Progress: lipgloss.NewStyle().
			Background(colors.Primary).
			Foreground(colors.Background),
	}
}
