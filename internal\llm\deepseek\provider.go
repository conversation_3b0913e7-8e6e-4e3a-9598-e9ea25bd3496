/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package deepseek

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"arien/internal/config"
	"arien/internal/interfaces"

	"github.com/charmbracelet/log"
	"golang.org/x/time/rate"
)

// Provider implements the DeepSeek LLM provider
type Provider struct {
	config  config.ProviderConfig
	logger  *log.Logger
	client  *http.Client
	limiter *rate.Limiter
	baseURL string
}

// DeepSeekRequest represents a request to DeepSeek API
type DeepSeekRequest struct {
	Model       string                   `json:"model"`
	Messages    []DeepSeekMessage        `json:"messages"`
	Tools       []DeepSeekTool           `json:"tools,omitempty"`
	Temperature float64                  `json:"temperature,omitempty"`
	MaxTokens   int                      `json:"max_tokens,omitempty"`
	Stream      bool                     `json:"stream,omitempty"`
}

// DeepSeekResponse represents a response from DeepSeek API
type DeepSeekResponse struct {
	ID      string                 `json:"id"`
	Object  string                 `json:"object"`
	Created int64                  `json:"created"`
	Model   string                 `json:"model"`
	Choices []DeepSeekChoice       `json:"choices"`
	Usage   DeepSeekUsage          `json:"usage"`
	Error   *DeepSeekError         `json:"error,omitempty"`
}

// DeepSeekMessage represents a message in DeepSeek format
type DeepSeekMessage struct {
	Role      string                 `json:"role"`
	Content   string                 `json:"content"`
	Name      string                 `json:"name,omitempty"`
	ToolCalls []DeepSeekToolCall     `json:"tool_calls,omitempty"`
}

// DeepSeekChoice represents a choice in DeepSeek response
type DeepSeekChoice struct {
	Index        int                    `json:"index"`
	Message      DeepSeekMessage        `json:"message"`
	FinishReason string                 `json:"finish_reason"`
}

// DeepSeekTool represents a tool definition for DeepSeek
type DeepSeekTool struct {
	Type     string                 `json:"type"`
	Function DeepSeekToolFunction   `json:"function"`
}

// DeepSeekToolFunction represents a function definition for DeepSeek
type DeepSeekToolFunction struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
}

// DeepSeekToolCall represents a tool call from DeepSeek
type DeepSeekToolCall struct {
	ID       string                 `json:"id"`
	Type     string                 `json:"type"`
	Function DeepSeekFunctionCall   `json:"function"`
}

// DeepSeekFunctionCall represents a function call from DeepSeek
type DeepSeekFunctionCall struct {
	Name      string `json:"name"`
	Arguments string `json:"arguments"`
}

// DeepSeekUsage represents usage information from DeepSeek
type DeepSeekUsage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// DeepSeekError represents an error from DeepSeek API
type DeepSeekError struct {
	Message string `json:"message"`
	Type    string `json:"type"`
	Code    string `json:"code"`
}

// NewProvider creates a new DeepSeek provider
func NewProvider(providerConfig config.ProviderConfig, logger *log.Logger) (*Provider, error) {
	if providerConfig.APIKey == "" {
		return nil, fmt.Errorf("DeepSeek API key is required")
	}

	baseURL := providerConfig.BaseURL
	if baseURL == "" {
		baseURL = "https://api.deepseek.com/v1"
	}

	// Set up rate limiting (DeepSeek specific limits)
	rateLimit := providerConfig.RateLimit
	if rateLimit == 0 {
		rateLimit = 60 // Default: 60 requests per minute
	}

	return &Provider{
		config:  providerConfig,
		logger:  logger,
		client:  &http.Client{Timeout: 60 * time.Second},
		limiter: rate.NewLimiter(rate.Every(time.Minute/time.Duration(rateLimit)), 1),
		baseURL: baseURL,
	}, nil
}

// Name returns the provider name
func (p *Provider) Name() string {
	return "deepseek"
}

// Models returns available models for DeepSeek
func (p *Provider) Models() []string {
	return []string{
		"deepseek-chat",
		"deepseek-reasoner",
	}
}

// ProcessMessage processes a message using DeepSeek API
func (p *Provider) ProcessMessage(ctx context.Context, request *interfaces.Request) (*interfaces.Response, error) {
	// Rate limiting
	if err := p.limiter.Wait(ctx); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	// Convert request to DeepSeek format
	deepseekReq := p.convertRequest(request)

	// Make API call
	deepseekResp, err := p.makeAPICall(ctx, deepseekReq)
	if err != nil {
		return nil, fmt.Errorf("DeepSeek API call failed: %w", err)
	}

	// Convert response to standard format
	response := p.convertResponse(deepseekResp)
	return response, nil
}

// SupportsToolCalling returns true if DeepSeek supports function calling
func (p *Provider) SupportsToolCalling() bool {
	return true
}

// SupportsStreaming returns true if DeepSeek supports streaming
func (p *Provider) SupportsStreaming() bool {
	return true
}

// convertRequest converts standard request to DeepSeek format
func (p *Provider) convertRequest(request *interfaces.Request) *DeepSeekRequest {
	deepseekReq := &DeepSeekRequest{
		Model:       request.Model,
		Temperature: request.Temperature,
		MaxTokens:   request.MaxTokens,
		Stream:      request.Stream,
	}

	// Convert messages
	for _, msg := range request.Messages {
		deepseekMsg := DeepSeekMessage{
			Role:    msg.Role,
			Content: msg.Content,
			Name:    msg.Name,
		}

		// Convert tool calls
		for _, toolCall := range msg.ToolCalls {
			args, _ := json.Marshal(toolCall.Arguments)
			deepseekMsg.ToolCalls = append(deepseekMsg.ToolCalls, DeepSeekToolCall{
				ID:   toolCall.ID,
				Type: toolCall.Type,
				Function: DeepSeekFunctionCall{
					Name:      toolCall.Name,
					Arguments: string(args),
				},
			})
		}

		deepseekReq.Messages = append(deepseekReq.Messages, deepseekMsg)
	}

	// Convert tools
	for _, tool := range request.Tools {
		deepseekReq.Tools = append(deepseekReq.Tools, DeepSeekTool{
			Type: tool.Type,
			Function: DeepSeekToolFunction{
				Name:        tool.Function.Name,
				Description: tool.Function.Description,
				Parameters:  tool.Function.Parameters,
			},
		})
	}

	return deepseekReq
}

// convertResponse converts DeepSeek response to standard format
func (p *Provider) convertResponse(deepseekResp *DeepSeekResponse) *interfaces.Response {
	response := &interfaces.Response{
		Model:     deepseekResp.Model,
		Provider:  p.Name(),
		Timestamp: time.Now(),
		Usage: interfaces.Usage{
			PromptTokens:     deepseekResp.Usage.PromptTokens,
			CompletionTokens: deepseekResp.Usage.CompletionTokens,
			TotalTokens:      deepseekResp.Usage.TotalTokens,
		},
	}

	if deepseekResp.Error != nil {
		response.Error = fmt.Errorf("DeepSeek API error: %s", deepseekResp.Error.Message)
		return response
	}

	if len(deepseekResp.Choices) > 0 {
		choice := deepseekResp.Choices[0]
		response.Content = choice.Message.Content

		// Convert tool calls
		for _, toolCall := range choice.Message.ToolCalls {
			var args map[string]interface{}
			json.Unmarshal([]byte(toolCall.Function.Arguments), &args)

			response.ToolCalls = append(response.ToolCalls, interfaces.ToolCall{
				ID:        toolCall.ID,
				Type:      toolCall.Type,
				Name:      toolCall.Function.Name,
				Arguments: args,
			})
		}
	}

	return response
}

// makeAPICall makes the actual API call to DeepSeek
func (p *Provider) makeAPICall(ctx context.Context, request *DeepSeekRequest) (*DeepSeekResponse, error) {
	// Serialize request
	reqBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	url := fmt.Sprintf("%s/chat/completions", p.baseURL)
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", p.config.APIKey))

	p.logger.Debug("Making DeepSeek API call", "url", url, "model", request.Model)

	// Make request
	resp, err := p.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Check status code
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	// Parse response
	var deepseekResp DeepSeekResponse
	if err := json.Unmarshal(respBody, &deepseekResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &deepseekResp, nil
}

// Shutdown gracefully shuts down the provider
func (p *Provider) Shutdown(ctx context.Context) error {
	p.logger.Info("Shutting down DeepSeek provider...")
	// No specific cleanup needed for HTTP client
	return nil
}
