package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"time"
)

type OpenAIMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type OpenAIRequest struct {
	Model       string          `json:"model"`
	Messages    []OpenAIMessage `json:"messages"`
	Temperature float64         `json:"temperature"`
	MaxTokens   int             `json:"max_tokens"`
}

type OpenAIChoice struct {
	Index   int           `json:"index"`
	Message OpenAIMessage `json:"message"`
}

type OpenAIUsage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

type OpenAIResponse struct {
	ID      string        `json:"id"`
	Object  string        `json:"object"`
	Created int64         `json:"created"`
	Model   string        `json:"model"`
	Choices []OpenAIChoice `json:"choices"`
	Usage   OpenAIUsage   `json:"usage"`
}

func main() {
	// Get API key from environment or config
	apiKey := os.Getenv("OPENAI_API_KEY")
	if apiKey == "" {
		// Try to read from Arien config
		homeDir, _ := os.UserHomeDir()
		configPath := homeDir + "/.arien/.arien-config.yaml"
		fmt.Printf("Checking config at: %s\n", configPath)
		
		// For now, let's use a placeholder
		fmt.Println("Please set OPENAI_API_KEY environment variable")
		return
	}

	// Create a simple request
	request := OpenAIRequest{
		Model: "gpt-4",
		Messages: []OpenAIMessage{
			{Role: "system", Content: "You are a helpful assistant."},
			{Role: "user", Content: "Hello, how are you?"},
		},
		Temperature: 0.7,
		MaxTokens:   100,
	}

	// Serialize request
	reqBody, err := json.Marshal(request)
	if err != nil {
		fmt.Printf("Failed to marshal request: %v\n", err)
		return
	}

	fmt.Printf("Request body: %s\n", string(reqBody))

	// Create HTTP request
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	url := "https://api.openai.com/v1/chat/completions"
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		fmt.Printf("Failed to create request: %v\n", err)
		return
	}

	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiKey))

	fmt.Printf("Making request to: %s\n", url)

	// Make request
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(httpReq)
	if err != nil {
		fmt.Printf("HTTP request failed: %v\n", err)
		return
	}
	defer resp.Body.Close()

	// Read response
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("Failed to read response: %v\n", err)
		return
	}

	fmt.Printf("Response status: %d\n", resp.StatusCode)
	fmt.Printf("Response body: %s\n", string(respBody))

	// Check status code
	if resp.StatusCode != http.StatusOK {
		fmt.Printf("API request failed with status %d\n", resp.StatusCode)
		return
	}

	// Parse response
	var openaiResp OpenAIResponse
	if err := json.Unmarshal(respBody, &openaiResp); err != nil {
		fmt.Printf("Failed to unmarshal response: %v\n", err)
		return
	}

	fmt.Printf("Parsed response: %+v\n", openaiResp)

	if len(openaiResp.Choices) > 0 {
		fmt.Printf("Content: %s\n", openaiResp.Choices[0].Message.Content)
	} else {
		fmt.Println("No choices in response")
	}
}
