/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package core

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"arien/internal/interfaces"
	"arien/internal/llm"
	"arien/internal/tools"

	"github.com/charmbracelet/log"
	"golang.org/x/time/rate"
)

// Orchestrator coordinates between LLM providers and tools
type Orchestrator struct {
	llmManager  *llm.Manager
	toolManager *tools.Manager
	logger      *log.Logger
	limiter     *rate.Limiter
	mu          sync.RWMutex
}

// NewOrchestrator creates a new orchestrator instance
func NewOrchestrator(llmManager *llm.Manager, toolManager *tools.Manager, logger *log.Logger) (*Orchestrator, error) {
	return &Orchestrator{
		llmManager:  llmManager,
		toolManager: toolManager,
		logger:      logger,
		limiter:     rate.NewLimiter(rate.Every(time.Second), 10), // 10 requests per second
	}, nil
}

// ProcessCommand processes a direct command
func (o *Orchestrator) ProcessCommand(ctx context.Context, command string) (string, error) {
	o.mu.RLock()
	defer o.mu.RUnlock()

	// Rate limiting
	if err := o.limiter.Wait(ctx); err != nil {
		return "", fmt.Errorf("rate limit exceeded: %w", err)
	}

	o.logger.Debug("Processing command", "command", command)

	// Check if this is a direct tool command
	if toolResult, handled := o.tryDirectToolExecution(ctx, command); handled {
		if toolResult.Error != nil {
			return "", toolResult.Error
		}
		return toolResult.Output, nil
	}

	// Use LLM to process the command
	response, err := o.llmManager.ProcessMessage(ctx, command, nil)
	if err != nil {
		return "", fmt.Errorf("LLM processing failed: %w", err)
	}

	// Execute any tool calls in the response
	if len(response.ToolCalls) > 0 {
		toolResults, err := o.executeToolCalls(ctx, response.ToolCalls)
		if err != nil {
			o.logger.Error("Tool execution failed", "error", err)
			return response.Content, nil // Return LLM response even if tools fail
		}

		// Combine LLM response with tool results
		return o.combineResults(response.Content, toolResults), nil
	}

	return response.Content, nil
}

// ProcessMessage processes a message in interactive mode
func (o *Orchestrator) ProcessMessage(ctx context.Context, message string) (*Response, error) {
	o.mu.RLock()
	defer o.mu.RUnlock()

	// Rate limiting
	if err := o.limiter.Wait(ctx); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	o.logger.Debug("Processing message", "message", message)

	response := &Response{
		Metadata: make(map[string]interface{}),
	}

	// Check if this is a direct tool command
	if toolResult, handled := o.tryDirectToolExecution(ctx, message); handled {
		response.Content = toolResult.Output
		response.ToolResults = []tools.Result{toolResult}
		response.Error = toolResult.Error
		return response, nil
	}

	// Use LLM to process the message
	llmResponse, err := o.llmManager.ProcessMessage(ctx, message, nil)
	if err != nil {
		response.Error = fmt.Errorf("LLM processing failed: %w", err)
		return response, nil
	}

	response.Content = llmResponse.Content
	response.Metadata["model"] = llmResponse.Model
	response.Metadata["provider"] = llmResponse.Provider

	// Execute any tool calls in the response
	if len(llmResponse.ToolCalls) > 0 {
		toolResults, err := o.executeToolCalls(ctx, llmResponse.ToolCalls)
		if err != nil {
			o.logger.Error("Tool execution failed", "error", err)
			// Don't return error, just log it and continue with LLM response
		} else {
			response.ToolResults = toolResults
			// Update content with tool results
			response.Content = o.combineResults(response.Content, toolResults)
		}
	}

	return response, nil
}

// tryDirectToolExecution attempts to execute a command as a direct tool call
func (o *Orchestrator) tryDirectToolExecution(ctx context.Context, command string) (tools.Result, bool) {
	// Parse command to see if it matches a tool pattern
	toolName, args, isToolCommand := o.parseToolCommand(command)
	if !isToolCommand {
		return tools.Result{}, false
	}

	// Execute the tool directly
	result := o.toolManager.ExecuteTool(ctx, toolName, args)
	return result, true
}

// parseToolCommand parses a command to extract tool name and arguments
func (o *Orchestrator) parseToolCommand(command string) (string, map[string]interface{}, bool) {
	command = strings.TrimSpace(command)
	if command == "" {
		return "", nil, false
	}

	// Split command into parts
	parts := strings.Fields(command)
	if len(parts) == 0 {
		return "", nil, false
	}

	toolName := parts[0]
	args := make(map[string]interface{})

	// Parse arguments based on tool type
	switch toolName {
	case "ls":
		if len(parts) > 1 {
			args["path"] = parts[1]
		}
		if len(parts) > 2 {
			// Parse additional flags
			for i := 2; i < len(parts); i++ {
				switch parts[i] {
				case "-l", "--long":
					args["long"] = true
				case "-a", "--all":
					args["all"] = true
				case "-r", "--recursive":
					args["recursive"] = true
				}
			}
		}
		return toolName, args, true

	case "read":
		if len(parts) > 1 {
			args["file"] = parts[1]
		}
		return toolName, args, true

	case "write":
		if len(parts) > 1 {
			args["file"] = parts[1]
		}
		if len(parts) > 2 {
			// Join remaining parts as content
			args["content"] = strings.Join(parts[2:], " ")
		}
		return toolName, args, true

	case "grep":
		if len(parts) > 1 {
			args["pattern"] = parts[1]
		}
		if len(parts) > 2 {
			args["file"] = parts[2]
		}
		return toolName, args, true

	case "search":
		if len(parts) > 1 {
			args["query"] = strings.Join(parts[1:], " ")
		}
		return toolName, args, true

	case "memory":
		if len(parts) > 1 {
			args["action"] = parts[1]
		}
		if len(parts) > 2 {
			args["content"] = strings.Join(parts[2:], " ")
		}
		return toolName, args, true

	case "tasks":
		if len(parts) > 1 {
			args["action"] = parts[1]
		}
		if len(parts) > 2 {
			switch parts[1] {
			case "create":
				args["name"] = strings.Join(parts[2:], " ")
			case "update", "delete":
				args["task_id"] = parts[2]
			}
		}
		return toolName, args, true

	default:
		// For unknown tools, try to parse as key-value pairs
		for i := 1; i < len(parts); i++ {
			if strings.Contains(parts[i], "=") {
				kv := strings.SplitN(parts[i], "=", 2)
				if len(kv) == 2 {
					args[kv[0]] = kv[1]
				}
			} else {
				// Use positional arguments
				args[fmt.Sprintf("arg%d", i-1)] = parts[i]
			}
		}
		return toolName, args, true
	}
}

// executeToolCalls executes multiple tool calls concurrently
func (o *Orchestrator) executeToolCalls(ctx context.Context, toolCalls []interfaces.ToolCall) ([]tools.Result, error) {
	if len(toolCalls) == 0 {
		return nil, nil
	}

	results := make([]tools.Result, len(toolCalls))
	var wg sync.WaitGroup
	var mu sync.Mutex
	var firstError error

	for i, toolCall := range toolCalls {
		wg.Add(1)
		go func(index int, call interfaces.ToolCall) {
			defer wg.Done()

			result := o.toolManager.ExecuteTool(ctx, call.Name, call.Arguments)

			mu.Lock()
			results[index] = result
			if result.Error != nil && firstError == nil {
				firstError = result.Error
			}
			mu.Unlock()
		}(i, toolCall)
	}

	wg.Wait()

	// Log any errors but don't fail the entire operation
	if firstError != nil {
		o.logger.Error("Some tool calls failed", "error", firstError)
	}

	return results, nil
}

// combineResults combines LLM response with tool results
func (o *Orchestrator) combineResults(llmContent string, toolResults []tools.Result) string {
	if len(toolResults) == 0 {
		return llmContent
	}

	result := llmContent + "\n\n"

	for i, toolResult := range toolResults {
		if toolResult.Error != nil {
			result += fmt.Sprintf("Tool %d Error: %v\n", i+1, toolResult.Error)
		} else if toolResult.Output != "" {
			result += fmt.Sprintf("Tool %d Output:\n%s\n", i+1, toolResult.Output)
		}
	}

	return result
}

// Shutdown gracefully shuts down the orchestrator
func (o *Orchestrator) Shutdown(ctx context.Context) error {
	o.mu.Lock()
	defer o.mu.Unlock()

	o.logger.Info("Shutting down orchestrator...")
	// No specific cleanup needed for orchestrator
	return nil
}
