/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package chat

import (
	"context"
	"time"

	"arien/internal/core"
	"arien/internal/interfaces"
	"arien/internal/security"

	"github.com/charmbracelet/log"
)

// Message represents a chat message with enhanced metadata
type Message struct {
	ID          string                 `json:"id"`
	Role        string                 `json:"role"`
	Content     string                 `json:"content"`
	Timestamp   time.Time              `json:"timestamp"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	ToolResults []interfaces.ToolCall  `json:"tool_results,omitempty"`
	Attachments []Attachment           `json:"attachments,omitempty"`
	Status      MessageStatus          `json:"status"`
	Error       string                 `json:"error,omitempty"`
}

// Attachment represents a file attachment
type Attachment struct {
	ID       string    `json:"id"`
	Name     string    `json:"name"`
	Path     string    `json:"path"`
	Size     int64     `json:"size"`
	MimeType string    `json:"mime_type"`
	Created  time.Time `json:"created"`
}

// MessageStatus represents the status of a message
type MessageStatus int

const (
	MessageStatusPending MessageStatus = iota
	MessageStatusSending
	MessageStatusSent
	MessageStatusDelivered
	MessageStatusFailed
	MessageStatusStreaming
)

// ChatSession represents a chat session with persistence
type ChatSession struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Messages    []Message              `json:"messages"`
	Created     time.Time              `json:"created"`
	Updated     time.Time              `json:"updated"`
	Metadata    map[string]interface{} `json:"metadata"`
	Provider    string                 `json:"provider"`
	Model       string                 `json:"model"`
	Settings    SessionSettings        `json:"settings"`
}

// SessionSettings contains session-specific settings
type SessionSettings struct {
	Temperature      float64 `json:"temperature"`
	MaxTokens        int     `json:"max_tokens"`
	SystemPrompt     string  `json:"system_prompt"`
	AutoSave         bool    `json:"auto_save"`
	StreamResponses  bool    `json:"stream_responses"`
	EnableTools      bool    `json:"enable_tools"`
	EnableMemory     bool    `json:"enable_memory"`
	MaxHistorySize   int     `json:"max_history_size"`
}

// ChatState represents the current state of the chat UI
type ChatState struct {
	CurrentSession   *ChatSession
	InputBuffer      string
	CursorPosition   int
	ScrollOffset     int
	IsLoading        bool
	IsStreaming      bool
	LastError        error
	ViewMode         ViewMode
	SelectedMessage  int
	ShowAttachments  bool
	ShowMetadata     bool
}

// ViewMode represents different view modes for the chat
type ViewMode int

const (
	ViewModeNormal ViewMode = iota
	ViewModeCompact
	ViewModeDetailed
	ViewModeDebug
)

// ChatConfig contains configuration for the chat module
type ChatConfig struct {
	MaxMessageLength   int
	MaxHistorySize     int
	AutoSaveInterval   time.Duration
	StreamingEnabled   bool
	AttachmentsEnabled bool
	MaxAttachmentSize  int64
	AllowedMimeTypes   []string
	SessionTimeout     time.Duration
}

// Dependencies contains all external dependencies for the chat module
type Dependencies struct {
	Engine    *core.Engine
	Logger    *log.Logger
	Validator *security.Validator
	Config    *ChatConfig
}

// MessageProcessor handles message processing and AI interactions
type MessageProcessor interface {
	ProcessMessage(ctx context.Context, message string, session *ChatSession) (*Message, error)
	ProcessCommand(ctx context.Context, command string, session *ChatSession) (*Message, error)
	StreamMessage(ctx context.Context, message string, session *ChatSession, callback func(chunk string)) error
	ValidateInput(input string) error
	SanitizeInput(input string) string
}

// SessionManager handles chat session persistence and management
type SessionManager interface {
	CreateSession(name string) (*ChatSession, error)
	LoadSession(id string) (*ChatSession, error)
	SaveSession(session *ChatSession) error
	DeleteSession(id string) error
	ListSessions() ([]*ChatSession, error)
	GetCurrentSession() *ChatSession
	SetCurrentSession(session *ChatSession)
	AutoSave(session *ChatSession) error
}

// AttachmentManager handles file attachments
type AttachmentManager interface {
	AddAttachment(filePath string) (*Attachment, error)
	RemoveAttachment(id string) error
	GetAttachment(id string) (*Attachment, error)
	ValidateAttachment(filePath string) error
	ProcessAttachment(attachment *Attachment) (string, error)
}

// EventHandler handles chat events and notifications
type EventHandler interface {
	OnMessageSent(message *Message)
	OnMessageReceived(message *Message)
	OnError(err error)
	OnSessionChanged(session *ChatSession)
	OnStreamingStarted()
	OnStreamingChunk(chunk string)
	OnStreamingCompleted()
}

// ChatModel represents the main Bubble Tea model for the chat interface
type ChatModel struct {
	deps             *Dependencies
	state            *ChatState
	messageProcessor MessageProcessor
	sessionManager   SessionManager
	attachmentMgr    AttachmentManager
	eventHandler     EventHandler
	
	// UI components
	viewport         *Viewport
	inputField       *InputField
	statusBar        *StatusBar
	
	// Internal state
	initialized      bool
	lastUpdate       time.Time
	keyMap           KeyMap
}

// Viewport handles the scrollable message display area
type Viewport struct {
	Width         int
	Height        int
	ScrollOffset  int
	MaxScroll     int
	Messages      []Message
	SelectedIndex int
}

// InputField handles user input with enhanced features
type InputField struct {
	Value         string
	CursorPos     int
	MaxLength     int
	Placeholder   string
	Multiline     bool
	History       []string
	HistoryIndex  int
	Suggestions   []string
}

// StatusBar displays status information
type StatusBar struct {
	Provider      string
	Model         string
	TokenCount    int
	SessionName   string
	LastSaved     time.Time
	IsConnected   bool
	IsStreaming   bool
	ErrorMessage  string
}

// KeyMap defines keyboard shortcuts
type KeyMap struct {
	Send            []string
	Quit            []string
	ScrollUp        []string
	ScrollDown      []string
	ClearInput      []string
	NewSession      []string
	SaveSession     []string
	LoadSession     []string
	ToggleMetadata  []string
	ToggleDebug     []string
	AddAttachment   []string
	PreviousMessage []string
	NextMessage     []string
}

// Tea messages for the Bubble Tea framework
type (
	MessageSentMsg       struct{ Message *Message }
	MessageReceivedMsg   struct{ Message *Message }
	StreamingChunkMsg    struct{ Chunk string }
	StreamingCompleteMsg struct{}
	ErrorMsg             struct{ Error error }
	SessionLoadedMsg     struct{ Session *ChatSession }
	SessionSavedMsg      struct{ Session *ChatSession }
	AttachmentAddedMsg   struct{ Attachment *Attachment }
	ConfigUpdatedMsg     struct{ Config *ChatConfig }
	ResizeMsg            struct{ Width, Height int }
)
