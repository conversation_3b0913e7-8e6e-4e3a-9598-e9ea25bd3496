/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
)

// ReadTool implements file reading with syntax highlighting
type ReadTool struct{
	enableSyntaxHighlighting bool
}

// NewReadTool creates a new read tool
func NewReadTool() *ReadTool {
	return &ReadTool{
		enableSyntaxHighlighting: true,
	}
}

// Name returns the tool name
func (t *ReadTool) Name() string {
	return "read"
}

// Description returns the tool description
func (t *ReadTool) Description() string {
	return "Read file contents with optional syntax highlighting and line numbers"
}

// Parameters returns the tool parameter schema
func (t *ReadTool) Parameters() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"file": StringParameter("File path to read", true),
			"highlight": BoolParameter("Enable syntax highlighting", true),
			"theme": map[string]interface{}{
				"type":        "string",
				"description": "Syntax highlighting theme",
				"enum":        []string{"github", "monokai", "dracula", "solarized-dark", "solarized-light"},
				"default":     "github",
			},
			"line_numbers": BoolParameter("Show line numbers", true),
			"start_line": IntParameter("Start reading from line number", 1, 0),
			"end_line": IntParameter("End reading at line number", 1, 0),
			"max_lines": IntParameter("Maximum number of lines to read", 1, 10000),
			"encoding": map[string]interface{}{
				"type": "string",
				"description": "File encoding (utf-8, ascii, etc.)",
				"default": "utf-8",
			},
		},
		"required": []string{"file"},
	}
}

// Execute executes the read tool
func (t *ReadTool) Execute(ctx context.Context, args map[string]interface{}) Result {
	// Parse arguments
	filePath, ok := args["file"].(string)
	if !ok || filePath == "" {
		return Result{Error: fmt.Errorf("file path is required")}
	}

	highlight := true
	if h, ok := args["highlight"].(bool); ok {
		highlight = h
	}

	lineNumbers := true
	if ln, ok := args["line_numbers"].(bool); ok {
		lineNumbers = ln
	}

	startLine := 1
	if sl, ok := args["start_line"].(float64); ok && sl > 0 {
		startLine = int(sl)
	}

	endLine := 0
	if el, ok := args["end_line"].(float64); ok && el > 0 {
		endLine = int(el)
	}

	maxLines := 1000
	if ml, ok := args["max_lines"].(float64); ok && ml > 0 {
		maxLines = int(ml)
	}

	encoding := "utf-8"
	if enc, ok := args["encoding"].(string); ok {
		encoding = enc
	}

	// Read and format file
	output, metadata, err := t.readFile(filePath, highlight, lineNumbers, startLine, endLine, maxLines, encoding)
	if err != nil {
		return Result{Error: err}
	}

	return Result{
		Output:   output,
		Metadata: metadata,
	}
}

// Validate validates the tool arguments
func (t *ReadTool) Validate(args map[string]interface{}) error {
	filePath, ok := args["file"].(string)
	if !ok || filePath == "" {
		return fmt.Errorf("file path is required")
	}

	// Check if file exists and is readable
	info, err := os.Stat(filePath)
	if err != nil {
		return fmt.Errorf("cannot access file: %w", err)
	}

	if info.IsDir() {
		return fmt.Errorf("path is a directory, not a file: %s", filePath)
	}

	// Check file size (limit to 10MB for safety)
	if info.Size() > 10*1024*1024 {
		return fmt.Errorf("file too large (>10MB): %s", filePath)
	}

	return nil
}

// SupportsParallel returns true if the tool can be executed in parallel
func (t *ReadTool) SupportsParallel() bool {
	return true
}

// readFile performs the actual file reading
func (t *ReadTool) readFile(filePath string, highlight, lineNumbers bool, startLine, endLine, maxLines int, encoding string) (string, map[string]interface{}, error) {
	// Open file
	file, err := os.Open(filePath)
	if err != nil {
		return "", nil, fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	// Get file info
	info, err := file.Stat()
	if err != nil {
		return "", nil, fmt.Errorf("failed to get file info: %w", err)
	}

	// Read file content
	content, err := io.ReadAll(file)
	if err != nil {
		return "", nil, fmt.Errorf("failed to read file: %w", err)
	}

	// Convert to string (assuming UTF-8 for now)
	contentStr := string(content)
	
	// Split into lines
	lines := strings.Split(contentStr, "\n")
	totalLines := len(lines)

	// Apply line range filtering
	if startLine > totalLines {
		return "", nil, fmt.Errorf("start line %d exceeds file length %d", startLine, totalLines)
	}

	// Adjust indices (convert to 0-based)
	start := startLine - 1
	end := totalLines
	
	if endLine > 0 && endLine <= totalLines {
		end = endLine
	}

	// Apply max lines limit
	if end-start > maxLines {
		end = start + maxLines
	}

	// Extract the requested lines
	selectedLines := lines[start:end]

	// Format output
	var output strings.Builder
	
	// Add file header
	output.WriteString(fmt.Sprintf("File: %s\n", filePath))
	output.WriteString(fmt.Sprintf("Lines: %d-%d of %d\n", start+1, end, totalLines))
	output.WriteString(strings.Repeat("-", 50) + "\n")

	// Add content with optional line numbers
	for i, line := range selectedLines {
		lineNum := start + i + 1
		
		if lineNumbers {
			output.WriteString(fmt.Sprintf("%4d | %s\n", lineNum, line))
		} else {
			output.WriteString(line + "\n")
		}
	}

	// Add syntax highlighting if requested
	if highlight {
		// Basic syntax highlighting based on file extension
		ext := strings.ToLower(filepath.Ext(filePath))
		language := t.detectLanguage(ext)
		
		if language != "" {
			// In a full implementation, we would use a syntax highlighting library
			// For now, we'll just add a note about the detected language
			output.WriteString(fmt.Sprintf("\n[Detected language: %s]", language))
		}
	}

	// Prepare metadata
	metadata := map[string]interface{}{
		"file_path":    filePath,
		"file_size":    info.Size(),
		"total_lines":  totalLines,
		"lines_shown":  len(selectedLines),
		"start_line":   start + 1,
		"end_line":     end,
		"encoding":     encoding,
		"modified":     info.ModTime(),
	}

	if highlight {
		ext := strings.ToLower(filepath.Ext(filePath))
		metadata["language"] = t.detectLanguage(ext)
	}

	return output.String(), metadata, nil
}

// detectLanguage detects programming language from file extension
func (t *ReadTool) detectLanguage(ext string) string {
	languages := map[string]string{
		".go":   "Go",
		".py":   "Python",
		".js":   "JavaScript",
		".ts":   "TypeScript",
		".java": "Java",
		".c":    "C",
		".cpp":  "C++",
		".h":    "C/C++ Header",
		".cs":   "C#",
		".php":  "PHP",
		".rb":   "Ruby",
		".rs":   "Rust",
		".sh":   "Shell",
		".bash": "Bash",
		".zsh":  "Zsh",
		".fish": "Fish",
		".ps1":  "PowerShell",
		".html": "HTML",
		".css":  "CSS",
		".scss": "SCSS",
		".sass": "Sass",
		".xml":  "XML",
		".json": "JSON",
		".yaml": "YAML",
		".yml":  "YAML",
		".toml": "TOML",
		".ini":  "INI",
		".conf": "Config",
		".md":   "Markdown",
		".txt":  "Text",
		".sql":  "SQL",
		".r":    "R",
		".m":    "MATLAB",
		".swift": "Swift",
		".kt":   "Kotlin",
		".scala": "Scala",
		".clj":  "Clojure",
		".hs":   "Haskell",
		".elm":  "Elm",
		".ex":   "Elixir",
		".erl":  "Erlang",
		".lua":  "Lua",
		".pl":   "Perl",
		".vim":  "Vim Script",
	}

	if lang, exists := languages[ext]; exists {
		return lang
	}
	
	return ""
}
