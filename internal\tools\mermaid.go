/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// MermaidTool implements interactive Mermaid diagram creation and rendering
type MermaidTool struct {
	outputDir string
}

// NewMermaidTool creates a new Mermaid tool
func NewMermaidTool() *MermaidTool {
	return &MermaidTool{
		outputDir: "./diagrams",
	}
}

// Name returns the tool name
func (t *MermaidTool) Name() string {
	return "mermaid"
}

// Description returns the tool description
func (t *MermaidTool) Description() string {
	return "Create and render interactive Mermaid diagrams (flowcharts, sequence diagrams, etc.)"
}

// Parameters returns the tool parameter schema
func (t *MermaidTool) Parameters() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"action": map[string]interface{}{
				"type": "string",
				"description": "Action to perform",
				"enum": []string{"create", "render", "validate", "examples", "convert"},
			},
			"diagram_type": map[string]interface{}{
				"type": "string",
				"description": "Type of Mermaid diagram",
				"enum": []string{
					"flowchart", "sequence", "class", "state", "entity_relationship",
					"user_journey", "gantt", "pie", "requirement", "gitgraph",
					"mindmap", "timeline", "quadrant", "sankey",
				},
			},
			"content": StringParameter("Mermaid diagram content/definition", false),
			"title": StringParameter("Diagram title", false),
			"output_file": StringParameter("Output file name (without extension)", false),
			"format": map[string]interface{}{
				"type": "string",
				"description": "Output format",
				"enum": []string{"svg", "png", "pdf", "html"},
				"default": "svg",
			},
			"theme": map[string]interface{}{
				"type": "string",
				"description": "Diagram theme",
				"enum": []string{"default", "dark", "forest", "neutral", "base"},
				"default": "default",
			},
			"width": IntParameter("Diagram width in pixels", 100, 4000),
			"height": IntParameter("Diagram height in pixels", 100, 4000),
			"background": StringParameter("Background color (hex)", false),
		},
		"required": []string{"action"},
	}
}

// Execute executes the Mermaid tool
func (t *MermaidTool) Execute(ctx context.Context, args map[string]interface{}) Result {
	action, ok := args["action"].(string)
	if !ok {
		return Result{Error: fmt.Errorf("action is required")}
	}

	switch action {
	case "create":
		return t.createDiagram(args)
	case "render":
		return t.renderDiagram(args)
	case "validate":
		return t.validateDiagram(args)
	case "examples":
		return t.showExamples(args)
	case "convert":
		return t.convertDiagram(args)
	default:
		return Result{Error: fmt.Errorf("unknown action: %s", action)}
	}
}

// Validate validates the tool arguments
func (t *MermaidTool) Validate(args map[string]interface{}) error {
	action, ok := args["action"].(string)
	if !ok {
		return fmt.Errorf("action is required")
	}

	validActions := []string{"create", "render", "validate", "examples", "convert"}
	valid := false
	for _, validAction := range validActions {
		if action == validAction {
			valid = true
			break
		}
	}
	if !valid {
		return fmt.Errorf("invalid action: %s", action)
	}

	if action == "create" || action == "render" || action == "validate" {
		if content, ok := args["content"].(string); !ok || content == "" {
			return fmt.Errorf("content is required for %s action", action)
		}
	}

	return nil
}

// SupportsParallel returns true if the tool can be executed in parallel
func (t *MermaidTool) SupportsParallel() bool {
	return true
}

// createDiagram creates a new Mermaid diagram
func (t *MermaidTool) createDiagram(args map[string]interface{}) Result {
	diagramType, _ := args["diagram_type"].(string)
	title, _ := args["title"].(string)
	content, _ := args["content"].(string)

	if diagramType == "" {
		diagramType = "flowchart"
	}

	// Generate diagram content if not provided
	if content == "" {
		content = t.generateTemplate(diagramType, title)
	}

	// Ensure output directory exists
	if err := os.MkdirAll(t.outputDir, 0755); err != nil {
		return Result{Error: fmt.Errorf("failed to create output directory: %w", err)}
	}

	// Generate filename
	filename := "diagram"
	if title != "" {
		filename = strings.ReplaceAll(strings.ToLower(title), " ", "_")
	}
	filename += "_" + time.Now().Format("20060102_150405") + ".mmd"
	
	filePath := filepath.Join(t.outputDir, filename)

	// Write diagram to file
	if err := os.WriteFile(filePath, []byte(content), 0644); err != nil {
		return Result{Error: fmt.Errorf("failed to write diagram file: %w", err)}
	}

	output := fmt.Sprintf("✅ Mermaid diagram created successfully!\n\n")
	output += fmt.Sprintf("📁 File: %s\n", filePath)
	output += fmt.Sprintf("📊 Type: %s\n", diagramType)
	if title != "" {
		output += fmt.Sprintf("📝 Title: %s\n", title)
	}
	output += fmt.Sprintf("\n📋 Diagram Content:\n")
	output += strings.Repeat("-", 50) + "\n"
	output += content + "\n"
	output += strings.Repeat("-", 50) + "\n"

	metadata := map[string]interface{}{
		"action":       "create",
		"diagram_type": diagramType,
		"title":        title,
		"file_path":    filePath,
		"content_size": len(content),
		"timestamp":    time.Now(),
	}

	return Result{
		Output:   output,
		Metadata: metadata,
	}
}

// renderDiagram renders a Mermaid diagram to various formats
func (t *MermaidTool) renderDiagram(args map[string]interface{}) Result {
	content, _ := args["content"].(string)
	format, _ := args["format"].(string)
	theme, _ := args["theme"].(string)
	outputFile, _ := args["output_file"].(string)
	width := int(getFloatArg(args, "width", 800))
	height := int(getFloatArg(args, "height", 600))
	background, _ := args["background"].(string)

	if format == "" {
		format = "svg"
	}
	if theme == "" {
		theme = "default"
	}
	if outputFile == "" {
		outputFile = "diagram_" + time.Now().Format("20060102_150405")
	}

	// Ensure output directory exists
	if err := os.MkdirAll(t.outputDir, 0755); err != nil {
		return Result{Error: fmt.Errorf("failed to create output directory: %w", err)}
	}

	outputPath := filepath.Join(t.outputDir, outputFile+"."+format)

	// For demonstration, create an HTML file with embedded Mermaid
	htmlContent := t.generateHTMLWithMermaid(content, theme, width, height, background)
	htmlPath := filepath.Join(t.outputDir, outputFile+".html")

	if err := os.WriteFile(htmlPath, []byte(htmlContent), 0644); err != nil {
		return Result{Error: fmt.Errorf("failed to write HTML file: %w", err)}
	}

	output := fmt.Sprintf("🎨 Mermaid diagram rendered successfully!\n\n")
	output += fmt.Sprintf("📁 HTML File: %s\n", htmlPath)
	output += fmt.Sprintf("🎯 Format: %s\n", format)
	output += fmt.Sprintf("🎨 Theme: %s\n", theme)
	output += fmt.Sprintf("📐 Size: %dx%d\n", width, height)
	if background != "" {
		output += fmt.Sprintf("🎨 Background: %s\n", background)
	}
	output += fmt.Sprintf("\n💡 Open the HTML file in a browser to view the interactive diagram.\n")

	// Add rendering instructions
	output += fmt.Sprintf("\n🔧 To render to %s format:\n", format)
	output += "1. Install Mermaid CLI: npm install -g @mermaid-js/mermaid-cli\n"
	output += fmt.Sprintf("2. Run: mmdc -i %s -o %s\n", htmlPath, outputPath)

	metadata := map[string]interface{}{
		"action":      "render",
		"format":      format,
		"theme":       theme,
		"html_path":   htmlPath,
		"output_path": outputPath,
		"width":       width,
		"height":      height,
		"background":  background,
		"timestamp":   time.Now(),
	}

	return Result{
		Output:   output,
		Metadata: metadata,
	}
}

// validateDiagram validates Mermaid diagram syntax
func (t *MermaidTool) validateDiagram(args map[string]interface{}) Result {
	content, _ := args["content"].(string)

	// Basic validation
	errors := []string{}
	warnings := []string{}

	// Check for basic syntax
	if !strings.Contains(content, "graph") && !strings.Contains(content, "flowchart") &&
		!strings.Contains(content, "sequenceDiagram") && !strings.Contains(content, "classDiagram") &&
		!strings.Contains(content, "stateDiagram") && !strings.Contains(content, "erDiagram") &&
		!strings.Contains(content, "journey") && !strings.Contains(content, "gantt") &&
		!strings.Contains(content, "pie") && !strings.Contains(content, "requirementDiagram") &&
		!strings.Contains(content, "gitGraph") && !strings.Contains(content, "mindmap") {
		errors = append(errors, "No valid diagram type declaration found")
	}

	// Check for common syntax issues
	lines := strings.Split(content, "\n")
	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "%%") {
			continue // Skip empty lines and comments
		}

		// Check for unmatched brackets
		openBrackets := strings.Count(line, "[") + strings.Count(line, "(") + strings.Count(line, "{")
		closeBrackets := strings.Count(line, "]") + strings.Count(line, ")") + strings.Count(line, "}")
		if openBrackets != closeBrackets {
			warnings = append(warnings, fmt.Sprintf("Line %d: Potentially unmatched brackets", i+1))
		}

		// Check for invalid characters in node IDs
		if strings.Contains(line, "-->") || strings.Contains(line, "->") {
			parts := strings.Split(line, "-->")
			if len(parts) < 2 {
				parts = strings.Split(line, "->")
			}
			if len(parts) >= 2 {
				nodeId := strings.TrimSpace(parts[0])
				if strings.Contains(nodeId, " ") && !strings.Contains(nodeId, "[") {
					warnings = append(warnings, fmt.Sprintf("Line %d: Node ID contains spaces without brackets", i+1))
				}
			}
		}
	}

	// Generate output
	output := fmt.Sprintf("🔍 Mermaid Diagram Validation Results\n")
	output += strings.Repeat("=", 50) + "\n\n"

	if len(errors) == 0 && len(warnings) == 0 {
		output += "✅ Diagram syntax is valid!\n"
		output += "🎉 No errors or warnings found.\n"
	} else {
		if len(errors) > 0 {
			output += fmt.Sprintf("❌ Errors (%d):\n", len(errors))
			for i, err := range errors {
				output += fmt.Sprintf("  %d. %s\n", i+1, err)
			}
			output += "\n"
		}

		if len(warnings) > 0 {
			output += fmt.Sprintf("⚠️  Warnings (%d):\n", len(warnings))
			for i, warning := range warnings {
				output += fmt.Sprintf("  %d. %s\n", i+1, warning)
			}
			output += "\n"
		}
	}

	output += fmt.Sprintf("📊 Diagram Statistics:\n")
	output += fmt.Sprintf("  Lines: %d\n", len(lines))
	output += fmt.Sprintf("  Characters: %d\n", len(content))
	output += fmt.Sprintf("  Non-empty lines: %d\n", countNonEmptyLines(lines))

	metadata := map[string]interface{}{
		"action":           "validate",
		"is_valid":         len(errors) == 0,
		"error_count":      len(errors),
		"warning_count":    len(warnings),
		"errors":           errors,
		"warnings":         warnings,
		"line_count":       len(lines),
		"character_count":  len(content),
		"timestamp":        time.Now(),
	}

	return Result{
		Output:   output,
		Metadata: metadata,
	}
}

// showExamples shows example Mermaid diagrams
func (t *MermaidTool) showExamples(args map[string]interface{}) Result {
	diagramType, _ := args["diagram_type"].(string)

	output := fmt.Sprintf("📚 Mermaid Diagram Examples\n")
	output += strings.Repeat("=", 50) + "\n\n"

	if diagramType == "" || diagramType == "flowchart" {
		output += "🔄 Flowchart Example:\n"
		output += strings.Repeat("-", 30) + "\n"
		output += t.generateTemplate("flowchart", "Sample Process") + "\n\n"
	}

	if diagramType == "" || diagramType == "sequence" {
		output += "📞 Sequence Diagram Example:\n"
		output += strings.Repeat("-", 30) + "\n"
		output += t.generateTemplate("sequence", "API Call") + "\n\n"
	}

	if diagramType == "" || diagramType == "class" {
		output += "🏗️  Class Diagram Example:\n"
		output += strings.Repeat("-", 30) + "\n"
		output += t.generateTemplate("class", "User Management") + "\n\n"
	}

	if diagramType == "" || diagramType == "gantt" {
		output += "📅 Gantt Chart Example:\n"
		output += strings.Repeat("-", 30) + "\n"
		output += t.generateTemplate("gantt", "Project Timeline") + "\n\n"
	}

	output += "💡 Tips:\n"
	output += "• Use 'mermaid create' to generate a new diagram\n"
	output += "• Use 'mermaid render' to create visual output\n"
	output += "• Use 'mermaid validate' to check syntax\n"
	output += "• Visit https://mermaid.js.org for full documentation\n"

	metadata := map[string]interface{}{
		"action":       "examples",
		"diagram_type": diagramType,
		"timestamp":    time.Now(),
	}

	return Result{
		Output:   output,
		Metadata: metadata,
	}
}

// convertDiagram converts between different diagram formats
func (t *MermaidTool) convertDiagram(args map[string]interface{}) Result {
	content, _ := args["content"].(string)
	outputFile, _ := args["output_file"].(string)

	if outputFile == "" {
		outputFile = "converted_diagram_" + time.Now().Format("20060102_150405")
	}

	// For demonstration, show conversion capabilities
	output := fmt.Sprintf("🔄 Diagram Conversion\n")
	output += strings.Repeat("=", 50) + "\n\n"
	output += "📝 Original Mermaid Content:\n"
	output += strings.Repeat("-", 30) + "\n"
	output += content + "\n\n"

	// Analyze diagram type
	diagramType := t.detectDiagramType(content)
	output += fmt.Sprintf("🔍 Detected Type: %s\n\n", diagramType)

	// Show conversion options
	output += "🔄 Available Conversions:\n"
	output += "• HTML with interactive viewer\n"
	output += "• SVG for web embedding\n"
	output += "• PNG for documentation\n"
	output += "• PDF for reports\n"
	output += "• PlantUML format (basic)\n"
	output += "• DOT/Graphviz format (basic)\n\n"

	output += "💡 Use 'mermaid render' to generate specific formats\n"

	metadata := map[string]interface{}{
		"action":       "convert",
		"diagram_type": diagramType,
		"output_file":  outputFile,
		"timestamp":    time.Now(),
	}

	return Result{
		Output:   output,
		Metadata: metadata,
	}
}
