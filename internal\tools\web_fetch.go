/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"
)

// WebFetchToolImpl implements web page fetching
type WebFetchToolImpl struct{}

// NewWebFetchTool creates a new web fetch tool
func NewWebFetchTool() *WebFetchToolImpl {
	return &WebFetchToolImpl{}
}

// Name returns the tool name
func (t *WebFetchToolImpl) Name() string {
	return "web-fetch"
}

// Description returns the tool description
func (t *WebFetchToolImpl) Description() string {
	return "Fetch web pages and convert to Markdown"
}

// Parameters returns the tool parameter schema
func (t *WebFetchToolImpl) Parameters() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"url": StringParameter("URL to fetch", true),
			"format": map[string]interface{}{
				"type": "string",
				"description": "Output format: markdown, text, html",
				"enum": []string{"markdown", "text", "html"},
				"default": "markdown",
			},
			"timeout": IntParameter("Request timeout in seconds (default: 30)", 1, 120),
			"follow_redirects": BoolParameter("Follow HTTP redirects", true),
			"extract_content": BoolParameter("Extract main content only", true),
		},
		"required": []string{"url"},
	}
}

// Execute executes the web fetch tool
func (t *WebFetchToolImpl) Execute(ctx context.Context, args map[string]interface{}) Result {
	urlStr, ok := args["url"].(string)
	if !ok || urlStr == "" {
		return Result{Error: fmt.Errorf("url is required")}
	}

	format := "markdown"
	if f, ok := args["format"].(string); ok {
		format = f
	}

	timeout := 30
	if t, ok := args["timeout"].(float64); ok && t > 0 {
		timeout = int(t)
	}

	followRedirects := true
	if fr, ok := args["follow_redirects"].(bool); ok {
		followRedirects = fr
	}

	extractContent := true
	if ec, ok := args["extract_content"].(bool); ok {
		extractContent = ec
	}

	// Fetch and process the web page
	content, metadata, err := t.fetchWebPage(ctx, urlStr, format, timeout, followRedirects, extractContent)
	if err != nil {
		return Result{Error: err}
	}

	return Result{
		Output:   content,
		Metadata: metadata,
	}
}

// Validate validates the tool arguments
func (t *WebFetchToolImpl) Validate(args map[string]interface{}) error {
	urlStr, ok := args["url"].(string)
	if !ok || urlStr == "" {
		return fmt.Errorf("url is required")
	}

	// Validate URL format
	_, err := url.Parse(urlStr)
	if err != nil {
		return fmt.Errorf("invalid URL format: %w", err)
	}

	if format, ok := args["format"].(string); ok {
		validFormats := []string{"markdown", "text", "html"}
		valid := false
		for _, validFormat := range validFormats {
			if format == validFormat {
				valid = true
				break
			}
		}
		if !valid {
			return fmt.Errorf("invalid format: %s", format)
		}
	}

	if timeout, ok := args["timeout"].(float64); ok {
		if timeout < 1 || timeout > 120 {
			return fmt.Errorf("timeout must be between 1 and 120 seconds")
		}
	}

	return nil
}

// SupportsParallel returns true if the tool can be executed in parallel
func (t *WebFetchToolImpl) SupportsParallel() bool {
	return true
}

// fetchWebPage fetches and processes a web page
func (t *WebFetchToolImpl) fetchWebPage(ctx context.Context, urlStr, format string, timeout int, followRedirects, extractContent bool) (string, map[string]interface{}, error) {
	// Create HTTP client
	client := &http.Client{
		Timeout: time.Duration(timeout) * time.Second,
	}

	if !followRedirects {
		client.CheckRedirect = func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		}
	}

	// Create request
	req, err := http.NewRequestWithContext(ctx, "GET", urlStr, nil)
	if err != nil {
		return "", nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set user agent
	req.Header.Set("User-Agent", "Arien/1.0 (AI Assistant)")

	// Make request
	startTime := time.Now()
	resp, err := client.Do(req)
	if err != nil {
		return "", nil, fmt.Errorf("failed to fetch URL: %w", err)
	}
	defer resp.Body.Close()

	duration := time.Since(startTime)

	// Check status code
	if resp.StatusCode >= 400 {
		return "", nil, fmt.Errorf("HTTP error %d: %s", resp.StatusCode, resp.Status)
	}

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", nil, fmt.Errorf("failed to read response body: %w", err)
	}

	content := string(body)

	// Process content based on format
	var processedContent string
	switch format {
	case "html":
		processedContent = content
	case "text":
		processedContent = t.htmlToText(content, extractContent)
	case "markdown":
		processedContent = t.htmlToMarkdown(content, extractContent)
	default:
		processedContent = content
	}

	// Prepare metadata
	metadata := map[string]interface{}{
		"url":           urlStr,
		"status_code":   resp.StatusCode,
		"content_type":  resp.Header.Get("Content-Type"),
		"content_length": len(content),
		"duration":      duration.String(),
		"format":        format,
		"timestamp":     time.Now(),
	}

	return processedContent, metadata, nil
}

// htmlToText converts HTML to plain text
func (t *WebFetchToolImpl) htmlToText(html string, extractContent bool) string {
	// Remove script and style tags
	scriptRegex := regexp.MustCompile(`(?i)<script[^>]*>.*?</script>`)
	html = scriptRegex.ReplaceAllString(html, "")
	
	styleRegex := regexp.MustCompile(`(?i)<style[^>]*>.*?</style>`)
	html = styleRegex.ReplaceAllString(html, "")

	if extractContent {
		// Try to extract main content
		html = t.extractMainContent(html)
	}

	// Remove HTML tags
	tagRegex := regexp.MustCompile(`<[^>]*>`)
	text := tagRegex.ReplaceAllString(html, "")

	// Clean up whitespace
	text = regexp.MustCompile(`\s+`).ReplaceAllString(text, " ")
	text = strings.TrimSpace(text)

	return text
}

// htmlToMarkdown converts HTML to Markdown (basic implementation)
func (t *WebFetchToolImpl) htmlToMarkdown(html string, extractContent bool) string {
	// Remove script and style tags
	scriptRegex := regexp.MustCompile(`(?i)<script[^>]*>.*?</script>`)
	html = scriptRegex.ReplaceAllString(html, "")
	
	styleRegex := regexp.MustCompile(`(?i)<style[^>]*>.*?</style>`)
	html = styleRegex.ReplaceAllString(html, "")

	if extractContent {
		html = t.extractMainContent(html)
	}

	// Convert common HTML elements to Markdown
	// Headers
	html = regexp.MustCompile(`(?i)<h1[^>]*>(.*?)</h1>`).ReplaceAllString(html, "# $1\n\n")
	html = regexp.MustCompile(`(?i)<h2[^>]*>(.*?)</h2>`).ReplaceAllString(html, "## $1\n\n")
	html = regexp.MustCompile(`(?i)<h3[^>]*>(.*?)</h3>`).ReplaceAllString(html, "### $1\n\n")
	html = regexp.MustCompile(`(?i)<h4[^>]*>(.*?)</h4>`).ReplaceAllString(html, "#### $1\n\n")
	html = regexp.MustCompile(`(?i)<h5[^>]*>(.*?)</h5>`).ReplaceAllString(html, "##### $1\n\n")
	html = regexp.MustCompile(`(?i)<h6[^>]*>(.*?)</h6>`).ReplaceAllString(html, "###### $1\n\n")

	// Bold and italic
	html = regexp.MustCompile(`(?i)<strong[^>]*>(.*?)</strong>`).ReplaceAllString(html, "**$1**")
	html = regexp.MustCompile(`(?i)<b[^>]*>(.*?)</b>`).ReplaceAllString(html, "**$1**")
	html = regexp.MustCompile(`(?i)<em[^>]*>(.*?)</em>`).ReplaceAllString(html, "*$1*")
	html = regexp.MustCompile(`(?i)<i[^>]*>(.*?)</i>`).ReplaceAllString(html, "*$1*")

	// Links
	html = regexp.MustCompile(`(?i)<a[^>]*href="([^"]*)"[^>]*>(.*?)</a>`).ReplaceAllString(html, "[$2]($1)")

	// Code
	html = regexp.MustCompile(`(?i)<code[^>]*>(.*?)</code>`).ReplaceAllString(html, "`$1`")
	html = regexp.MustCompile(`(?i)<pre[^>]*>(.*?)</pre>`).ReplaceAllString(html, "```\n$1\n```\n")

	// Paragraphs and line breaks
	html = regexp.MustCompile(`(?i)<p[^>]*>(.*?)</p>`).ReplaceAllString(html, "$1\n\n")
	html = regexp.MustCompile(`(?i)<br[^>]*/?>`).ReplaceAllString(html, "\n")

	// Lists
	html = regexp.MustCompile(`(?i)<li[^>]*>(.*?)</li>`).ReplaceAllString(html, "- $1\n")
	html = regexp.MustCompile(`(?i)<ul[^>]*>(.*?)</ul>`).ReplaceAllString(html, "$1\n")
	html = regexp.MustCompile(`(?i)<ol[^>]*>(.*?)</ol>`).ReplaceAllString(html, "$1\n")

	// Remove remaining HTML tags
	tagRegex := regexp.MustCompile(`<[^>]*>`)
	markdown := tagRegex.ReplaceAllString(html, "")

	// Clean up whitespace
	markdown = regexp.MustCompile(`\n\s*\n\s*\n`).ReplaceAllString(markdown, "\n\n")
	markdown = strings.TrimSpace(markdown)

	return markdown
}

// extractMainContent attempts to extract the main content from HTML
func (t *WebFetchToolImpl) extractMainContent(html string) string {
	// Look for common content containers
	contentSelectors := []string{
		`<main[^>]*>(.*?)</main>`,
		`<article[^>]*>(.*?)</article>`,
		`<div[^>]*class="[^"]*content[^"]*"[^>]*>(.*?)</div>`,
		`<div[^>]*id="[^"]*content[^"]*"[^>]*>(.*?)</div>`,
		`<div[^>]*class="[^"]*main[^"]*"[^>]*>(.*?)</div>`,
		`<div[^>]*id="[^"]*main[^"]*"[^>]*>(.*?)</div>`,
	}

	for _, selector := range contentSelectors {
		regex := regexp.MustCompile(`(?i)` + selector)
		matches := regex.FindStringSubmatch(html)
		if len(matches) > 1 && len(matches[1]) > 100 {
			return matches[1]
		}
	}

	// If no main content found, remove common non-content elements
	html = regexp.MustCompile(`(?i)<nav[^>]*>.*?</nav>`).ReplaceAllString(html, "")
	html = regexp.MustCompile(`(?i)<header[^>]*>.*?</header>`).ReplaceAllString(html, "")
	html = regexp.MustCompile(`(?i)<footer[^>]*>.*?</footer>`).ReplaceAllString(html, "")
	html = regexp.MustCompile(`(?i)<aside[^>]*>.*?</aside>`).ReplaceAllString(html, "")

	return html
}
