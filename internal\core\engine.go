/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package core

import (
	"context"
	"fmt"
	"sync"

	"arien/internal/llm"
	"arien/internal/tools"

	"github.com/charmbracelet/log"
)

// Engine is the core orchestrator for Arien
type Engine struct {
	ctx           context.Context
	logger        *log.Logger
	config        *Config
	llmManager    *llm.Manager
	toolManager   *tools.Manager
	orchestrator  *Orchestrator
	memory        *Memory
	tasks         *TaskManager
	metrics       *Metrics
	mu            sync.RWMutex
	initialized   bool
}

// NewEngine creates a new core engine instance
func NewEngine(ctx context.Context, logger *log.Logger) (*Engine, error) {
	engine := &Engine{
		ctx:    ctx,
		logger: logger,
	}

	if err := engine.initialize(); err != nil {
		return nil, fmt.Errorf("failed to initialize engine: %w", err)
	}

	return engine, nil
}

// initialize sets up all engine components
func (e *Engine) initialize() error {
	e.mu.Lock()
	defer e.mu.Unlock()

	if e.initialized {
		return nil
	}

	// Initialize configuration
	config, err := NewConfig(e.logger)
	if err != nil {
		return fmt.Errorf("failed to initialize config: %w", err)
	}
	e.config = config

	// Initialize LLM manager
	llmManager, err := llm.NewManager(e.config, e.logger)
	if err != nil {
		return fmt.Errorf("failed to initialize LLM manager: %w", err)
	}
	e.llmManager = llmManager

	// Initialize tool manager
	toolManager, err := tools.NewManager(e.logger)
	if err != nil {
		return fmt.Errorf("failed to initialize tool manager: %w", err)
	}
	e.toolManager = toolManager

	// Initialize metrics
	e.metrics = NewMetrics(e.logger)

	// Initialize orchestrator
	orchestrator, err := NewOrchestrator(e.llmManager, e.toolManager, e.logger)
	if err != nil {
		return fmt.Errorf("failed to initialize orchestrator: %w", err)
	}
	e.orchestrator = orchestrator

	// Initialize memory system
	memory, err := NewMemory(e.config, e.logger)
	if err != nil {
		return fmt.Errorf("failed to initialize memory: %w", err)
	}
	e.memory = memory

	// Initialize task manager
	tasks, err := NewTaskManager(e.config, e.logger)
	if err != nil {
		return fmt.Errorf("failed to initialize task manager: %w", err)
	}
	e.tasks = tasks

	e.initialized = true
	e.logger.Info("Core engine initialized successfully")
	return nil
}

// Config returns the configuration manager
func (e *Engine) Config() *Config {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.config
}

// LLM returns the LLM manager
func (e *Engine) LLM() *llm.Manager {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.llmManager
}

// Tools returns the tool manager
func (e *Engine) Tools() *tools.Manager {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.toolManager
}

// Memory returns the memory manager
func (e *Engine) Memory() *Memory {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.memory
}

// Tasks returns the task manager
func (e *Engine) Tasks() *TaskManager {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.tasks
}

// Metrics returns the metrics system
func (e *Engine) Metrics() *Metrics {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.metrics
}

// ExecuteCommand executes a direct command without interactive mode
func (e *Engine) ExecuteCommand(ctx context.Context, command string) error {
	e.mu.RLock()
	defer e.mu.RUnlock()

	if !e.initialized {
		return fmt.Errorf("engine not initialized")
	}

	e.logger.Info("Executing direct command", "command", command)

	// Use orchestrator to process the command
	response, err := e.orchestrator.ProcessCommand(ctx, command)
	if err != nil {
		return fmt.Errorf("failed to execute command: %w", err)
	}

	// Output response
	fmt.Println(response)
	return nil
}

// ProcessMessage processes a message in interactive mode
func (e *Engine) ProcessMessage(ctx context.Context, message string) (*Response, error) {
	e.mu.RLock()
	defer e.mu.RUnlock()

	if !e.initialized {
		return nil, fmt.Errorf("engine not initialized")
	}

	e.logger.Debug("Processing message", "message", message)

	// Use orchestrator to process the message
	return e.orchestrator.ProcessMessage(ctx, message)
}

// Shutdown gracefully shuts down the engine
func (e *Engine) Shutdown(ctx context.Context) error {
	e.mu.Lock()
	defer e.mu.Unlock()

	if !e.initialized {
		return nil
	}

	e.logger.Info("Shutting down core engine...")

	// Shutdown components in reverse order
	if e.tasks != nil {
		if err := e.tasks.Shutdown(ctx); err != nil {
			e.logger.Error("Failed to shutdown task manager", "error", err)
		}
	}

	if e.memory != nil {
		if err := e.memory.Shutdown(ctx); err != nil {
			e.logger.Error("Failed to shutdown memory", "error", err)
		}
	}

	if e.orchestrator != nil {
		if err := e.orchestrator.Shutdown(ctx); err != nil {
			e.logger.Error("Failed to shutdown orchestrator", "error", err)
		}
	}

	if e.toolManager != nil {
		if err := e.toolManager.Shutdown(ctx); err != nil {
			e.logger.Error("Failed to shutdown tool manager", "error", err)
		}
	}

	if e.llmManager != nil {
		if err := e.llmManager.Shutdown(ctx); err != nil {
			e.logger.Error("Failed to shutdown LLM manager", "error", err)
		}
	}

	if e.metrics != nil {
		if err := e.metrics.Shutdown(ctx); err != nil {
			e.logger.Error("Failed to shutdown metrics", "error", err)
		}
	}

	e.initialized = false
	e.logger.Info("Core engine shutdown complete")
	return nil
}

// Response represents a response from the engine
type Response struct {
	Content     string                 `json:"content"`
	ToolResults []tools.Result         `json:"tool_results,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Error       error                  `json:"error,omitempty"`
}

// IsError returns true if the response contains an error
func (r *Response) IsError() bool {
	return r.Error != nil
}

// String returns the string representation of the response
func (r *Response) String() string {
	if r.IsError() {
		return fmt.Sprintf("Error: %v", r.Error)
	}
	return r.Content
}
