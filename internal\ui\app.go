/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package ui

import (
	"context"

	"arien/internal/core"
	"arien/internal/ui/auth"
	"arien/internal/ui/chat"
	"arien/internal/ui/tasks"

	"github.com/charmbracelet/log"
)

// App represents the base UI application
type App interface {
	Run(ctx context.Context) error
}

// NewAuthApp creates a new authentication app
func NewAuthApp(engine *core.Engine, logger *log.Logger) App {
	return auth.NewAuthApp(engine, logger)
}

// NewChatApp creates a new chat app
func NewChatApp(engine *core.Engine, logger *log.Logger) App {
	return chat.NewChatApp(engine, logger)
}

// NewTaskApp creates a new task management app
func NewTaskApp(engine *core.Engine, logger *log.Logger) App {
	return tasks.NewTaskApp(engine, logger)
}
