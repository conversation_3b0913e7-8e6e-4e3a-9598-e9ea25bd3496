/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package chat

import (
	"fmt"
	"time"

	"arien/internal/core"
	"arien/internal/security"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/log"
)

// NewChatModel creates a new chat model with all dependencies
func NewChatModel(engine *core.Engine, logger *log.Logger) (*ChatModel, error) {
	// Create dependencies
	deps := &Dependencies{
		Engine:    engine,
		Logger:    logger,
		Validator: security.NewValidator(),
		Config:    getDefaultChatConfig(),
	}

	// Initialize state
	state := &ChatState{
		ViewMode:        ViewModeNormal,
		SelectedMessage: -1,
		ShowAttachments: false,
		ShowMetadata:    false,
	}

	// Create components
	messageProcessor := NewMessageProcessor(deps)
	sessionManager := NewSessionManager(deps)
	attachmentMgr := NewAttachmentManager(deps)
	eventHandler := NewEventHandler(deps)

	// Initialize UI components
	viewport := &Viewport{
		Width:         80,
		Height:        20,
		ScrollOffset:  0,
		SelectedIndex: -1,
	}

	inputField := &InputField{
		MaxLength:    deps.Config.MaxMessageLength,
		Placeholder:  "Type your message...",
		Multiline:    false,
		History:      make([]string, 0),
		HistoryIndex: -1,
		Suggestions:  make([]string, 0),
	}

	statusBar := &StatusBar{
		Provider:    "Unknown",
		Model:       "Unknown",
		IsConnected: false,
		IsStreaming: false,
	}

	model := &ChatModel{
		deps:             deps,
		state:            state,
		messageProcessor: messageProcessor,
		sessionManager:   sessionManager,
		attachmentMgr:    attachmentMgr,
		eventHandler:     eventHandler,
		viewport:         viewport,
		inputField:       inputField,
		statusBar:        statusBar,
		keyMap:           getDefaultKeyMap(),
		lastUpdate:       time.Now(),
	}

	// Initialize the model
	if err := model.initialize(); err != nil {
		return nil, fmt.Errorf("failed to initialize chat model: %w", err)
	}

	return model, nil
}

// initialize sets up the chat model
func (m *ChatModel) initialize() error {
	m.deps.Logger.Debug("Initializing chat model")

	// Load or create default session
	session, err := m.sessionManager.LoadSession("default")
	if err != nil {
		// Create new default session
		session, err = m.sessionManager.CreateSession("Default Chat")
		if err != nil {
			return fmt.Errorf("failed to create default session: %w", err)
		}
	}

	m.state.CurrentSession = session
	m.sessionManager.SetCurrentSession(session)

	// Update status bar with provider info
	if provider, err := m.deps.Engine.LLM().GetDefaultProvider(); err == nil {
		m.statusBar.Provider = provider.Name()
		m.statusBar.IsConnected = true
		
		// Get model from config
		if config, exists := m.deps.Engine.Config().GetProvider(provider.Name()); exists {
			m.statusBar.Model = config.Model
		}
	}

	// Add welcome message if session is empty
	if len(session.Messages) == 0 {
		welcomeMsg := &Message{
			ID:        generateMessageID(),
			Role:      "assistant",
			Content:   "Hello! I'm Arien, your AI-powered software engineering assistant. How can I help you today?",
			Timestamp: time.Now(),
			Status:    MessageStatusDelivered,
			Metadata:  make(map[string]interface{}),
		}
		
		session.Messages = append(session.Messages, *welcomeMsg)
		m.sessionManager.SaveSession(session)
	}

	// Update viewport with messages
	m.viewport.Messages = session.Messages
	m.updateViewport()

	m.initialized = true
	m.deps.Logger.Info("Chat model initialized successfully")
	return nil
}

// Init implements tea.Model
func (m *ChatModel) Init() tea.Cmd {
	return tea.Batch(
		m.autoSaveCmd(),
		m.updateStatusCmd(),
	)
}

// autoSaveCmd returns a command for auto-saving
func (m *ChatModel) autoSaveCmd() tea.Cmd {
	return tea.Tick(m.deps.Config.AutoSaveInterval, func(t time.Time) tea.Msg {
		if m.state.CurrentSession != nil {
			m.sessionManager.AutoSave(m.state.CurrentSession)
		}
		return nil
	})
}

// updateStatusCmd returns a command for updating status
func (m *ChatModel) updateStatusCmd() tea.Cmd {
	return tea.Tick(time.Second*5, func(t time.Time) tea.Msg {
		// Update token count, connection status, etc.
		return nil
	})
}

// updateViewport updates the viewport state
func (m *ChatModel) updateViewport() {
	if m.state.CurrentSession == nil {
		return
	}

	m.viewport.Messages = m.state.CurrentSession.Messages
	
	// Auto-scroll to bottom for new messages
	if len(m.viewport.Messages) > 0 {
		maxScroll := len(m.viewport.Messages) - m.viewport.Height
		if maxScroll < 0 {
			maxScroll = 0
		}
		m.viewport.MaxScroll = maxScroll
		
		// Auto-scroll to bottom unless user has scrolled up
		if m.viewport.ScrollOffset >= m.viewport.MaxScroll-1 {
			m.viewport.ScrollOffset = m.viewport.MaxScroll
		}
	}
}

// getDefaultChatConfig returns default configuration
func getDefaultChatConfig() *ChatConfig {
	return &ChatConfig{
		MaxMessageLength:   10000,
		MaxHistorySize:     1000,
		AutoSaveInterval:   time.Minute * 2,
		StreamingEnabled:   true,
		AttachmentsEnabled: true,
		MaxAttachmentSize:  10 * 1024 * 1024, // 10MB
		AllowedMimeTypes: []string{
			"text/plain",
			"text/markdown",
			"application/json",
			"text/csv",
			"application/xml",
			"text/html",
			"text/css",
			"text/javascript",
			"application/javascript",
		},
		SessionTimeout: time.Hour * 24,
	}
}

// getDefaultKeyMap returns default keyboard shortcuts
func getDefaultKeyMap() KeyMap {
	return KeyMap{
		Send:            []string{"enter"},
		Quit:            []string{"ctrl+c", "esc"},
		ScrollUp:        []string{"up", "k"},
		ScrollDown:      []string{"down", "j"},
		ClearInput:      []string{"ctrl+l"},
		NewSession:      []string{"ctrl+n"},
		SaveSession:     []string{"ctrl+s"},
		LoadSession:     []string{"ctrl+o"},
		ToggleMetadata:  []string{"ctrl+m"},
		ToggleDebug:     []string{"ctrl+d"},
		AddAttachment:   []string{"ctrl+a"},
		PreviousMessage: []string{"ctrl+up"},
		NextMessage:     []string{"ctrl+down"},
	}
}

// generateMessageID generates a unique message ID
func generateMessageID() string {
	return fmt.Sprintf("msg_%d", time.Now().UnixNano())
}

// Utility methods for the model

// GetCurrentSession returns the current chat session
func (m *ChatModel) GetCurrentSession() *ChatSession {
	return m.state.CurrentSession
}

// SetViewMode sets the current view mode
func (m *ChatModel) SetViewMode(mode ViewMode) {
	m.state.ViewMode = mode
}

// GetViewMode returns the current view mode
func (m *ChatModel) GetViewMode() ViewMode {
	return m.state.ViewMode
}

// IsLoading returns whether the model is currently loading
func (m *ChatModel) IsLoading() bool {
	return m.state.IsLoading
}

// IsStreaming returns whether the model is currently streaming
func (m *ChatModel) IsStreaming() bool {
	return m.state.IsStreaming
}

// GetLastError returns the last error that occurred
func (m *ChatModel) GetLastError() error {
	return m.state.LastError
}

// ClearError clears the last error
func (m *ChatModel) ClearError() {
	m.state.LastError = nil
}

// GetMessageCount returns the number of messages in the current session
func (m *ChatModel) GetMessageCount() int {
	if m.state.CurrentSession == nil {
		return 0
	}
	return len(m.state.CurrentSession.Messages)
}

// GetInputBuffer returns the current input buffer
func (m *ChatModel) GetInputBuffer() string {
	return m.state.InputBuffer
}

// SetInputBuffer sets the input buffer
func (m *ChatModel) SetInputBuffer(input string) {
	m.state.InputBuffer = input
	m.inputField.Value = input
}
