/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"
)

// SearchTool implements recursive directory search
type SearchTool struct{}

// NewSearchTool creates a new search tool
func NewSearchTool() *SearchTool {
	return &SearchTool{}
}

// Name returns the tool name
func (t *SearchTool) Name() string {
	return "search"
}

// Description returns the tool description
func (t *SearchTool) Description() string {
	return "Recursive directory search with content filtering and result ranking"
}

// Parameters returns the tool parameter schema
func (t *SearchTool) Parameters() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"query": StringParameter("Search query or pattern", true),
			"path": StringParameter("Directory path to search (default: current directory)", false),
			"file_pattern": StringParameter("File name pattern to match (glob)", false),
			"content_search": BoolParameter("Search within file contents", true),
			"case_sensitive": BoolParameter("Case sensitive search", false),
			"max_results": IntParameter("Maximum number of results", 1, 1000),
			"max_depth": IntParameter("Maximum directory depth", 1, 20),
			"include_hidden": BoolParameter("Include hidden files and directories", false),
		},
		"required": []string{"query"},
	}
}

// Execute executes the search tool
func (t *SearchTool) Execute(ctx context.Context, args map[string]interface{}) Result {
	// Parse arguments
	query, ok := args["query"].(string)
	if !ok || query == "" {
		return Result{Error: fmt.Errorf("query is required")}
	}

	searchPath := "."
	if p, ok := args["path"].(string); ok && p != "" {
		searchPath = p
	}

	filePattern := "*"
	if fp, ok := args["file_pattern"].(string); ok && fp != "" {
		filePattern = fp
	}

	contentSearch := true
	if cs, ok := args["content_search"].(bool); ok {
		contentSearch = cs
	}

	caseSensitive := false
	if cs, ok := args["case_sensitive"].(bool); ok {
		caseSensitive = cs
	}

	maxResults := 100
	if mr, ok := args["max_results"].(float64); ok && mr > 0 {
		maxResults = int(mr)
	}

	maxDepth := 10
	if md, ok := args["max_depth"].(float64); ok && md > 0 {
		maxDepth = int(md)
	}

	includeHidden := false
	if ih, ok := args["include_hidden"].(bool); ok {
		includeHidden = ih
	}

	// Execute search
	results, metadata, err := t.performSearch(ctx, query, searchPath, filePattern, contentSearch, caseSensitive, maxResults, maxDepth, includeHidden)
	if err != nil {
		return Result{Error: err}
	}

	return Result{
		Output:   results,
		Metadata: metadata,
	}
}

// Validate validates the tool arguments
func (t *SearchTool) Validate(args map[string]interface{}) error {
	query, ok := args["query"].(string)
	if !ok || query == "" {
		return fmt.Errorf("query is required")
	}

	if path, ok := args["path"].(string); ok && path != "" {
		if _, err := os.Stat(path); err != nil {
			return fmt.Errorf("search path does not exist: %s", path)
		}
	}

	return nil
}

// SupportsParallel returns true if the tool can be executed in parallel
func (t *SearchTool) SupportsParallel() bool {
	return true
}

// SearchResult represents a search result
type SearchResult struct {
	Path     string
	Type     string // "file" or "directory"
	Matches  []Match
	Score    int
}

// Match represents a content match
type Match struct {
	Line    int
	Content string
	Context []string
}

// performSearch performs the actual search operation
func (t *SearchTool) performSearch(ctx context.Context, query, searchPath, filePattern string, contentSearch, caseSensitive bool, maxResults, maxDepth int, includeHidden bool) (string, map[string]interface{}, error) {
	var results []SearchResult
	var totalFiles, totalDirs int

	// Compile regex for content search
	var queryRegex *regexp.Regexp
	if contentSearch {
		flags := ""
		if !caseSensitive {
			flags = "(?i)"
		}
		var err error
		queryRegex, err = regexp.Compile(flags + regexp.QuoteMeta(query))
		if err != nil {
			return "", nil, fmt.Errorf("invalid query pattern: %w", err)
		}
	}

	// Walk directory tree
	err := filepath.Walk(searchPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil // Skip files with errors
		}

		// Check context cancellation
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// Check depth limit
		relPath, _ := filepath.Rel(searchPath, path)
		depth := strings.Count(relPath, string(filepath.Separator))
		if depth > maxDepth {
			if info.IsDir() {
				return filepath.SkipDir
			}
			return nil
		}

		// Skip hidden files/directories if not included
		if !includeHidden && strings.HasPrefix(info.Name(), ".") {
			if info.IsDir() {
				return filepath.SkipDir
			}
			return nil
		}

		// Check file pattern
		if matched, _ := filepath.Match(filePattern, info.Name()); !matched {
			return nil
		}

		// Count files and directories
		if info.IsDir() {
			totalDirs++
		} else {
			totalFiles++
		}

		// Check if we've reached max results
		if len(results) >= maxResults {
			return filepath.SkipDir
		}

		result := SearchResult{
			Path: path,
			Type: "file",
		}

		if info.IsDir() {
			result.Type = "directory"
		}

		// Check filename match
		filename := info.Name()
		if !caseSensitive {
			filename = strings.ToLower(filename)
			query = strings.ToLower(query)
		}

		filenameMatch := strings.Contains(filename, query)
		
		// Search content if it's a file and content search is enabled
		var contentMatches []Match
		if !info.IsDir() && contentSearch && queryRegex != nil {
			matches, err := t.searchFileContent(path, queryRegex)
			if err == nil {
				contentMatches = matches
			}
		}

		// Calculate score and add to results if relevant
		score := 0
		if filenameMatch {
			score += 10
		}
		score += len(contentMatches) * 5

		if score > 0 || filenameMatch || len(contentMatches) > 0 {
			result.Matches = contentMatches
			result.Score = score
			results = append(results, result)
		}

		return nil
	})

	if err != nil && err != ctx.Err() {
		return "", nil, fmt.Errorf("search failed: %w", err)
	}

	// Sort results by score (descending)
	for i := 0; i < len(results)-1; i++ {
		for j := i + 1; j < len(results); j++ {
			if results[i].Score < results[j].Score {
				results[i], results[j] = results[j], results[i]
			}
		}
	}

	// Format output
	output := t.formatResults(results, query)

	metadata := map[string]interface{}{
		"query":         query,
		"search_path":   searchPath,
		"total_results": len(results),
		"total_files":   totalFiles,
		"total_dirs":    totalDirs,
		"content_search": contentSearch,
		"case_sensitive": caseSensitive,
	}

	return output, metadata, nil
}

// searchFileContent searches for matches within a file
func (t *SearchTool) searchFileContent(filePath string, regex *regexp.Regexp) ([]Match, error) {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}

	lines := strings.Split(string(content), "\n")
	var matches []Match

	for i, line := range lines {
		if regex.MatchString(line) {
			match := Match{
				Line:    i + 1,
				Content: strings.TrimSpace(line),
			}

			// Add context lines
			start := i - 2
			if start < 0 {
				start = 0
			}
			end := i + 3
			if end > len(lines) {
				end = len(lines)
			}

			for j := start; j < end; j++ {
				if j != i {
					match.Context = append(match.Context, strings.TrimSpace(lines[j]))
				}
			}

			matches = append(matches, match)
		}
	}

	return matches, nil
}

// formatResults formats search results for display
func (t *SearchTool) formatResults(results []SearchResult, query string) string {
	var output strings.Builder

	output.WriteString(fmt.Sprintf("Search Results for '%s':\n\n", query))

	if len(results) == 0 {
		output.WriteString("No results found.\n")
		return output.String()
	}

	for i, result := range results {
		if i >= 50 { // Limit display to first 50 results
			output.WriteString(fmt.Sprintf("... and %d more results\n", len(results)-i))
			break
		}

		output.WriteString(fmt.Sprintf("%d. %s (%s) [Score: %d]\n", i+1, result.Path, result.Type, result.Score))

		if len(result.Matches) > 0 {
			for _, match := range result.Matches {
				output.WriteString(fmt.Sprintf("   Line %d: %s\n", match.Line, match.Content))
			}
		}
		output.WriteString("\n")
	}

	return output.String()
}
