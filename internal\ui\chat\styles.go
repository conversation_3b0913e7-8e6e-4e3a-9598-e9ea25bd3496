/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package chat

import (
	"github.com/charmbracelet/lipgloss"
)

// ChatStyles contains all styling for the chat interface
type ChatStyles struct {
	// Header styles
	Header           lipgloss.Style
	Debug            lipgloss.Style

	// Message styles
	UserMessage      lipgloss.Style
	AssistantMessage lipgloss.Style
	SystemMessage    lipgloss.Style
	DefaultMessage   lipgloss.Style
	SelectedMessage  lipgloss.Style
	MessageContent   lipgloss.Style
	MessageStatus    lipgloss.Style

	// Input styles
	InputLabel       lipgloss.Style
	Input            lipgloss.Style
	DisabledInput    lipgloss.Style

	// Status and indicators
	StatusBar        lipgloss.Style
	StreamingIndicator lipgloss.Style
	ScrollIndicator  lipgloss.Style
	LoadingIndicator lipgloss.Style

	// Content styles
	Metadata         lipgloss.Style
	Attachment       lipgloss.Style
	Error            lipgloss.Style
	Help             lipgloss.Style
	EmptyState       lipgloss.Style

	// Theme colors
	Colors           *ColorScheme
}

// ColorScheme defines the color palette
type ColorScheme struct {
	// Primary colors
	Primary      string
	Secondary    string
	Accent       string
	
	// Message colors
	UserColor      string
	AssistantColor string
	SystemColor    string
	
	// Status colors
	Success        string
	Warning        string
	Error          string
	Info           string
	
	// UI colors
	Background     string
	Foreground     string
	Border         string
	Muted          string
	Highlight      string
	
	// Input colors
	InputBg        string
	InputFg        string
	InputBorder    string
	InputDisabled  string
}

var (
	// Global styles instance
	chatStyles *ChatStyles
	
	// Default color schemes
	darkColorScheme = &ColorScheme{
		Primary:        "#7C3AED",
		Secondary:      "#3B82F6",
		Accent:         "#10B981",
		UserColor:      "#10B981",
		AssistantColor: "#3B82F6",
		SystemColor:    "#F59E0B",
		Success:        "#10B981",
		Warning:        "#F59E0B",
		Error:          "#EF4444",
		Info:           "#3B82F6",
		Background:     "#1F2937",
		Foreground:     "#FFFFFF",
		Border:         "#374151",
		Muted:          "#6B7280",
		Highlight:      "#FDE047",
		InputBg:        "#1F2937",
		InputFg:        "#FFFFFF",
		InputBorder:    "#374151",
		InputDisabled:  "#6B7280",
	}
	
	lightColorScheme = &ColorScheme{
		Primary:        "#7C3AED",
		Secondary:      "#3B82F6",
		Accent:         "#059669",
		UserColor:      "#059669",
		AssistantColor: "#3B82F6",
		SystemColor:    "#D97706",
		Success:        "#059669",
		Warning:        "#D97706",
		Error:          "#DC2626",
		Info:           "#3B82F6",
		Background:     "#FFFFFF",
		Foreground:     "#1F2937",
		Border:         "#E5E7EB",
		Muted:          "#6B7280",
		Highlight:      "#FEF3C7",
		InputBg:        "#F9FAFB",
		InputFg:        "#1F2937",
		InputBorder:    "#D1D5DB",
		InputDisabled:  "#9CA3AF",
	}
)

// GetStyles returns the global chat styles instance
func GetStyles() *ChatStyles {
	if chatStyles == nil {
		chatStyles = createDarkTheme()
	}
	return chatStyles
}

// SetTheme sets the chat theme
func SetTheme(theme string) {
	switch theme {
	case "light":
		chatStyles = createLightTheme()
	case "dark":
		chatStyles = createDarkTheme()
	default:
		chatStyles = createDarkTheme()
	}
}

// createDarkTheme creates the dark theme styles
func createDarkTheme() *ChatStyles {
	colors := darkColorScheme
	
	return &ChatStyles{
		Colors: colors,

		// Header styles
		Header: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Primary)).
			Bold(true).
			Padding(1, 2).
			Border(lipgloss.RoundedBorder()).
			BorderForeground(lipgloss.Color(colors.Primary)),

		Debug: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Muted)).
			Italic(true).
			Padding(0, 2),

		// Message styles
		UserMessage: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.UserColor)).
			Bold(true).
			Padding(0, 1).
			MarginLeft(2),

		AssistantMessage: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.AssistantColor)).
			Bold(true).
			Padding(0, 1),

		SystemMessage: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.SystemColor)).
			Bold(true).
			Padding(0, 1),

		DefaultMessage: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Foreground)).
			Bold(true).
			Padding(0, 1),

		SelectedMessage: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Highlight)).
			Background(lipgloss.Color(colors.Primary)).
			Bold(true).
			Padding(0, 1),

		MessageContent: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Foreground)).
			Padding(0, 2),

		MessageStatus: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Muted)).
			Italic(true).
			Padding(0, 2),
		
		// Input styles
		InputLabel: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Muted)).
			Bold(true),

		Input: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.InputFg)).
			Background(lipgloss.Color(colors.InputBg)).
			Border(lipgloss.NormalBorder()).
			BorderForeground(lipgloss.Color(colors.InputBorder)).
			Padding(0, 1).
			Width(80),

		DisabledInput: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.InputDisabled)).
			Background(lipgloss.Color(colors.Border)).
			Border(lipgloss.NormalBorder()).
			BorderForeground(lipgloss.Color(colors.Border)).
			Padding(0, 1).
			Width(80),

		// Status and indicators
		StatusBar: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Muted)).
			Background(lipgloss.Color(colors.Border)).
			Padding(0, 1),

		StreamingIndicator: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Warning)).
			Blink(true),

		ScrollIndicator: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Muted)).
			Italic(true).
			Align(lipgloss.Center),

		LoadingIndicator: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Warning)).
			Italic(true),

		// Content styles
		Metadata: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Muted)).
			Italic(true).
			Padding(0, 2),

		Attachment: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Info)).
			Padding(0, 2),

		Error: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Error)).
			Bold(true).
			Padding(0, 1),

		Help: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Muted)).
			Italic(true),

		EmptyState: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Muted)).
			Italic(true).
			Align(lipgloss.Center).
			Padding(2),
	}
}

// createLightTheme creates the light theme styles
func createLightTheme() *ChatStyles {
	colors := lightColorScheme
	
	return &ChatStyles{
		Colors: colors,

		// Header styles
		Header: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Primary)).
			Bold(true).
			Padding(1, 2).
			Border(lipgloss.RoundedBorder()).
			BorderForeground(lipgloss.Color(colors.Primary)),

		Debug: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Muted)).
			Italic(true).
			Padding(0, 2),
		
		// Message styles
		UserMessage: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.UserColor)).
			Bold(true).
			Padding(0, 1).
			MarginLeft(2),

		AssistantMessage: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.AssistantColor)).
			Bold(true).
			Padding(0, 1),

		SystemMessage: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.SystemColor)).
			Bold(true).
			Padding(0, 1),

		DefaultMessage: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Foreground)).
			Bold(true).
			Padding(0, 1),

		SelectedMessage: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Background)).
			Background(lipgloss.Color(colors.Primary)).
			Bold(true).
			Padding(0, 1),

		MessageContent: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Foreground)).
			Padding(0, 2),

		MessageStatus: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Muted)).
			Italic(true).
			Padding(0, 2),
		
		// Input styles
		InputLabel: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Muted)).
			Bold(true),

		Input: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.InputFg)).
			Background(lipgloss.Color(colors.InputBg)).
			Border(lipgloss.NormalBorder()).
			BorderForeground(lipgloss.Color(colors.InputBorder)).
			Padding(0, 1).
			Width(80),

		DisabledInput: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.InputDisabled)).
			Background(lipgloss.Color(colors.Border)).
			Border(lipgloss.NormalBorder()).
			BorderForeground(lipgloss.Color(colors.Border)).
			Padding(0, 1).
			Width(80),

		// Status and indicators
		StatusBar: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Muted)).
			Background(lipgloss.Color(colors.Border)).
			Padding(0, 1),

		StreamingIndicator: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Warning)).
			Blink(true),

		ScrollIndicator: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Muted)).
			Italic(true).
			Align(lipgloss.Center),

		LoadingIndicator: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Warning)).
			Italic(true),

		// Content styles
		Metadata: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Muted)).
			Italic(true).
			Padding(0, 2),

		Attachment: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Info)).
			Padding(0, 2),

		Error: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Error)).
			Bold(true).
			Padding(0, 1),

		Help: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Muted)).
			Italic(true),

		EmptyState: lipgloss.NewStyle().
			Foreground(lipgloss.Color(colors.Muted)).
			Italic(true).
			Align(lipgloss.Center).
			Padding(2),
	}
}
