/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// WriteTool implements file creation/modification with backup capabilities
type WriteTool struct{}

// NewWriteTool creates a new write tool
func NewWriteTool() *WriteTool {
	return &WriteTool{}
}

// Name returns the tool name
func (t *WriteTool) Name() string {
	return "write"
}

// Description returns the tool description
func (t *WriteTool) Description() string {
	return "Create or modify files with backup capabilities and conflict resolution"
}

// Parameters returns the tool parameter schema
func (t *WriteTool) Parameters() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"file": StringParameter("File path to write", true),
			"content": StringParameter("Content to write to the file", true),
			"mode": map[string]interface{}{
				"type": "string",
				"description": "Write mode: create, overwrite, append, insert",
				"enum": []string{"create", "overwrite", "append", "insert"},
				"default": "create",
			},
			"backup": BoolParameter("Create backup before writing", true),
			"create_dirs": BoolParameter("Create parent directories if they don't exist", true),
			"encoding": map[string]interface{}{
				"type": "string",
				"description": "File encoding",
				"default": "utf-8",
			},
			"line_number": IntParameter("Line number for insert mode", 1, 0),
			"permissions": map[string]interface{}{
				"type": "string",
				"description": "File permissions (e.g., '0644')",
				"default": "0644",
			},
		},
		"required": []string{"file", "content"},
	}
}

// Execute executes the write tool
func (t *WriteTool) Execute(ctx context.Context, args map[string]interface{}) Result {
	// Parse arguments
	filePath, ok := args["file"].(string)
	if !ok || filePath == "" {
		return Result{Error: fmt.Errorf("file path is required")}
	}

	content, ok := args["content"].(string)
	if !ok {
		return Result{Error: fmt.Errorf("content is required")}
	}

	mode := "create"
	if m, ok := args["mode"].(string); ok {
		mode = m
	}

	backup := true
	if b, ok := args["backup"].(bool); ok {
		backup = b
	}

	createDirs := true
	if cd, ok := args["create_dirs"].(bool); ok {
		createDirs = cd
	}

	encoding := "utf-8"
	if enc, ok := args["encoding"].(string); ok {
		encoding = enc
	}

	lineNumber := 1
	if ln, ok := args["line_number"].(float64); ok && ln > 0 {
		lineNumber = int(ln)
	}

	permissions := "0644"
	if p, ok := args["permissions"].(string); ok {
		permissions = p
	}

	// Execute write operation
	output, metadata, err := t.writeFile(filePath, content, mode, backup, createDirs, encoding, lineNumber, permissions)
	if err != nil {
		return Result{Error: err}
	}

	return Result{
		Output:   output,
		Metadata: metadata,
	}
}

// Validate validates the tool arguments
func (t *WriteTool) Validate(args map[string]interface{}) error {
	filePath, ok := args["file"].(string)
	if !ok || filePath == "" {
		return fmt.Errorf("file path is required")
	}

	_, ok = args["content"].(string)
	if !ok {
		return fmt.Errorf("content is required")
	}

	if mode, ok := args["mode"].(string); ok {
		validModes := []string{"create", "overwrite", "append", "insert"}
		valid := false
		for _, v := range validModes {
			if mode == v {
				valid = true
				break
			}
		}
		if !valid {
			return fmt.Errorf("invalid mode: %s", mode)
		}
	}

	// Check if parent directory exists or can be created
	dir := filepath.Dir(filePath)
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		createDirs := true
		if cd, ok := args["create_dirs"].(bool); ok {
			createDirs = cd
		}
		if !createDirs {
			return fmt.Errorf("parent directory does not exist: %s", dir)
		}
	}

	return nil
}

// SupportsParallel returns true if the tool can be executed in parallel
func (t *WriteTool) SupportsParallel() bool {
	return false // File writing should be sequential to avoid conflicts
}

// writeFile performs the actual file writing
func (t *WriteTool) writeFile(filePath, content, mode string, backup, createDirs bool, encoding string, lineNumber int, permissions string) (string, map[string]interface{}, error) {
	var output strings.Builder
	
	// Create parent directories if needed
	if createDirs {
		dir := filepath.Dir(filePath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			return "", nil, fmt.Errorf("failed to create directories: %w", err)
		}
		output.WriteString(fmt.Sprintf("Created directories: %s\n", dir))
	}

	// Check if file exists
	fileExists := false
	var originalContent string
	if info, err := os.Stat(filePath); err == nil {
		fileExists = true
		
		// Read original content for backup and insert mode
		if backup || mode == "insert" {
			originalBytes, err := os.ReadFile(filePath)
			if err != nil {
				return "", nil, fmt.Errorf("failed to read original file: %w", err)
			}
			originalContent = string(originalBytes)
		}
		
		output.WriteString(fmt.Sprintf("File exists: %s (size: %d bytes)\n", filePath, info.Size()))
	} else {
		output.WriteString(fmt.Sprintf("Creating new file: %s\n", filePath))
	}

	// Handle different modes
	var finalContent string
	switch mode {
	case "create":
		if fileExists {
			return "", nil, fmt.Errorf("file already exists (use 'overwrite' mode to replace): %s", filePath)
		}
		finalContent = content
		
	case "overwrite":
		finalContent = content
		
	case "append":
		if fileExists {
			finalContent = originalContent + content
		} else {
			finalContent = content
		}
		
	case "insert":
		if !fileExists {
			finalContent = content
		} else {
			lines := strings.Split(originalContent, "\n")
			if lineNumber > len(lines)+1 {
				return "", nil, fmt.Errorf("line number %d exceeds file length %d", lineNumber, len(lines))
			}
			
			// Insert content at specified line
			insertLines := strings.Split(content, "\n")
			
			// Convert to 0-based index
			insertIndex := lineNumber - 1
			if insertIndex < 0 {
				insertIndex = 0
			}
			
			newLines := make([]string, 0, len(lines)+len(insertLines))
			newLines = append(newLines, lines[:insertIndex]...)
			newLines = append(newLines, insertLines...)
			newLines = append(newLines, lines[insertIndex:]...)
			
			finalContent = strings.Join(newLines, "\n")
		}
	}

	// Create backup if requested and file exists
	var backupPath string
	if backup && fileExists {
		timestamp := time.Now().Format("20060102_150405")
		backupPath = fmt.Sprintf("%s.backup_%s", filePath, timestamp)
		
		if err := os.Rename(filePath, backupPath); err != nil {
			return "", nil, fmt.Errorf("failed to create backup: %w", err)
		}
		output.WriteString(fmt.Sprintf("Backup created: %s\n", backupPath))
	}

	// Parse permissions
	perm, err := parsePermissions(permissions)
	if err != nil {
		return "", nil, fmt.Errorf("invalid permissions: %w", err)
	}

	// Write the file
	if err := os.WriteFile(filePath, []byte(finalContent), perm); err != nil {
		// If backup was created, try to restore it
		if backupPath != "" {
			if restoreErr := os.Rename(backupPath, filePath); restoreErr != nil {
				output.WriteString(fmt.Sprintf("Failed to restore backup: %v\n", restoreErr))
			} else {
				output.WriteString("Backup restored due to write failure\n")
			}
		}
		return "", nil, fmt.Errorf("failed to write file: %w", err)
	}

	// Get final file info
	info, err := os.Stat(filePath)
	if err != nil {
		return "", nil, fmt.Errorf("failed to get file info after write: %w", err)
	}

	output.WriteString(fmt.Sprintf("Successfully wrote %d bytes to %s\n", info.Size(), filePath))
	output.WriteString(fmt.Sprintf("File permissions: %s\n", info.Mode().String()))

	// Prepare metadata
	metadata := map[string]interface{}{
		"file_path":     filePath,
		"mode":          mode,
		"bytes_written": info.Size(),
		"backup_path":   backupPath,
		"created_new":   !fileExists,
		"encoding":      encoding,
		"permissions":   info.Mode().String(),
		"modified":      info.ModTime(),
	}

	if mode == "insert" {
		metadata["insert_line"] = lineNumber
	}

	return output.String(), metadata, nil
}

// parsePermissions parses permission string to os.FileMode
func parsePermissions(permissions string) (os.FileMode, error) {
	// Default permissions
	if permissions == "" {
		return 0644, nil
	}

	// Handle octal notation (e.g., "0644")
	if strings.HasPrefix(permissions, "0") {
		var perm uint32
		if _, err := fmt.Sscanf(permissions, "%o", &perm); err != nil {
			return 0, fmt.Errorf("invalid octal permissions: %s", permissions)
		}
		return os.FileMode(perm), nil
	}

	// Handle decimal notation (e.g., "644")
	var perm uint32
	if _, err := fmt.Sscanf(permissions, "%d", &perm); err != nil {
		return 0, fmt.Errorf("invalid permissions: %s", permissions)
	}
	
	// Convert decimal to octal interpretation
	return os.FileMode(perm), nil
}
