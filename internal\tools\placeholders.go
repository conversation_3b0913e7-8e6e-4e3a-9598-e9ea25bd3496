/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

// All tools have been fully implemented in their respective files:
//
// File Operations:
// - ls.go - Enhanced directory listing
// - read.go - File reading with syntax highlighting
// - write.go - File creation/modification with backup
// - read_many_files.go - Batch file operations
//
// Search Tools:
// - grep.go - Text search with regex support
// - glob.go - File pattern matching
// - search.go - Recursive directory search
//
// Development Tools:
// - edit.go - Interactive file editing
// - diff.go - Real-time diff viewing
// - shell.go - Shell command execution
//
// Web & Browser:
// - web_search.go - Google Custom Search API
// - web_fetch.go - Web page fetching
// - browser.go - Playwright browser automation
//
// Productivity:
// - memory_tool.go - Persistent memory management
// - task_tool.go - Task management system
//
// Utilities:
// - performance.go - System monitoring
// - diagnostics.go - Code analysis
// - mermaid.go - Diagram generation
// - playwright.go - Advanced browser automation
