/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package components

import (
	"fmt"
	"strings"
	"time"

	"arien/internal/ui/themes"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// NavigationComponent represents a navigation component
type NavigationComponent struct {
	*BaseComponent
	items         []NavigationItem
	selected      int
	orientation   NavigationOrientation
	showIcons     bool
	showKeys      bool
	breadcrumbs   []Breadcrumb
	quickAccess   []QuickAccessItem
	searchMode    bool
	searchQuery   string
	filteredItems []int
}

// NavigationOrientation defines navigation layout
type NavigationOrientation string

const (
	NavigationHorizontal NavigationOrientation = "horizontal"
	NavigationVertical   NavigationOrientation = "vertical"
	NavigationTabs       NavigationOrientation = "tabs"
	NavigationSidebar    NavigationOrientation = "sidebar"
)

// NavigationItem represents a navigation item
type NavigationItem struct {
	ID          string
	Label       string
	Icon        string
	KeyBinding  string
	Command     string
	Enabled     bool
	Badge       string
	BadgeColor  lipgloss.Color
	Submenu     []NavigationItem
	LastUsed    time.Time
	UseCount    int
}

// Breadcrumb represents a breadcrumb item
type Breadcrumb struct {
	Label string
	Path  string
	Icon  string
}

// QuickAccessItem represents a quick access item
type QuickAccessItem struct {
	Label      string
	Command    string
	Icon       string
	KeyBinding string
	LastUsed   time.Time
	UseCount   int
}

// NewNavigationComponent creates a new navigation component
func NewNavigationComponent(id string, orientation NavigationOrientation) *NavigationComponent {
	return &NavigationComponent{
		BaseComponent: NewBaseComponent(id, TypeNavigation),
		items:         make([]NavigationItem, 0),
		selected:      0,
		orientation:   orientation,
		showIcons:     true,
		showKeys:      true,
		breadcrumbs:   make([]Breadcrumb, 0),
		quickAccess:   make([]QuickAccessItem, 0),
		searchMode:    false,
		filteredItems: make([]int, 0),
	}
}

// Init initializes the navigation component
func (nc *NavigationComponent) Init() tea.Cmd {
	nc.loadDefaultItems()
	nc.updateFilteredItems()
	return nil
}

// Update handles navigation component updates
func (nc *NavigationComponent) Update(msg tea.Msg) (Component, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		if nc.searchMode {
			return nc.handleSearchInput(msg)
		}
		return nc.handleNavigation(msg)
	case tea.WindowSizeMsg:
		nc.SetSize(msg.Width, msg.Height)
	}
	
	return nc, nil
}

// View renders the navigation component
func (nc *NavigationComponent) View() string {
	if nc.width == 0 || nc.height == 0 {
		return ""
	}
	
	var content strings.Builder
	
	// Render breadcrumbs if available
	if len(nc.breadcrumbs) > 0 {
		content.WriteString(nc.renderBreadcrumbs())
		content.WriteString("\n")
	}
	
	// Render search bar if in search mode
	if nc.searchMode {
		content.WriteString(nc.renderSearchBar())
		content.WriteString("\n")
	}
	
	// Render navigation items based on orientation
	switch nc.orientation {
	case NavigationHorizontal:
		content.WriteString(nc.renderHorizontal())
	case NavigationVertical:
		content.WriteString(nc.renderVertical())
	case NavigationTabs:
		content.WriteString(nc.renderTabs())
	case NavigationSidebar:
		content.WriteString(nc.renderSidebar())
	default:
		content.WriteString(nc.renderVertical())
	}
	
	// Render quick access if available
	if len(nc.quickAccess) > 0 && nc.orientation == NavigationSidebar {
		content.WriteString("\n")
		content.WriteString(nc.renderQuickAccess())
	}
	
	return content.String()
}

// AddItem adds a navigation item
func (nc *NavigationComponent) AddItem(item NavigationItem) {
	nc.items = append(nc.items, item)
	nc.updateFilteredItems()
}

// RemoveItem removes a navigation item by ID
func (nc *NavigationComponent) RemoveItem(id string) {
	for i, item := range nc.items {
		if item.ID == id {
			nc.items = append(nc.items[:i], nc.items[i+1:]...)
			break
		}
	}
	nc.updateFilteredItems()
	
	// Adjust selection if necessary
	if nc.selected >= len(nc.getVisibleItems()) {
		nc.selected = len(nc.getVisibleItems()) - 1
	}
	if nc.selected < 0 {
		nc.selected = 0
	}
}

// GetSelectedItem returns the currently selected item
func (nc *NavigationComponent) GetSelectedItem() *NavigationItem {
	visibleItems := nc.getVisibleItems()
	if nc.selected >= 0 && nc.selected < len(visibleItems) {
		return &visibleItems[nc.selected]
	}
	return nil
}

// SetSelected sets the selected item by index
func (nc *NavigationComponent) SetSelected(index int) {
	visibleItems := nc.getVisibleItems()
	if index >= 0 && index < len(visibleItems) {
		nc.selected = index
	}
}

// SetSelectedByID sets the selected item by ID
func (nc *NavigationComponent) SetSelectedByID(id string) {
	visibleItems := nc.getVisibleItems()
	for i, item := range visibleItems {
		if item.ID == id {
			nc.selected = i
			break
		}
	}
}

// AddBreadcrumb adds a breadcrumb
func (nc *NavigationComponent) AddBreadcrumb(breadcrumb Breadcrumb) {
	nc.breadcrumbs = append(nc.breadcrumbs, breadcrumb)
}

// ClearBreadcrumbs clears all breadcrumbs
func (nc *NavigationComponent) ClearBreadcrumbs() {
	nc.breadcrumbs = make([]Breadcrumb, 0)
}

// SetQuickAccess sets quick access items
func (nc *NavigationComponent) SetQuickAccess(items []QuickAccessItem) {
	nc.quickAccess = items
}

// ToggleSearch toggles search mode
func (nc *NavigationComponent) ToggleSearch() {
	nc.searchMode = !nc.searchMode
	if !nc.searchMode {
		nc.searchQuery = ""
		nc.updateFilteredItems()
	}
}

// Private methods

// handleNavigation handles navigation key input
func (nc *NavigationComponent) handleNavigation(msg tea.KeyMsg) (Component, tea.Cmd) {
	switch msg.String() {
	case "up", "k":
		nc.moveUp()
	case "down", "j":
		nc.moveDown()
	case "left", "h":
		if nc.orientation == NavigationHorizontal || nc.orientation == NavigationTabs {
			nc.moveLeft()
		}
	case "right", "l":
		if nc.orientation == NavigationHorizontal || nc.orientation == NavigationTabs {
			nc.moveRight()
		}
	case "enter", " ":
		return nc, nc.selectCurrentItem()
	case "/":
		nc.ToggleSearch()
	case "home":
		nc.selected = 0
	case "end":
		nc.selected = len(nc.getVisibleItems()) - 1
	default:
		// Check for key bindings
		if item := nc.findItemByKey(msg.String()); item != nil {
			nc.SetSelectedByID(item.ID)
			return nc, nc.selectCurrentItem()
		}
	}
	
	return nc, nil
}

// handleSearchInput handles search input
func (nc *NavigationComponent) handleSearchInput(msg tea.KeyMsg) (Component, tea.Cmd) {
	switch msg.String() {
	case "esc":
		nc.ToggleSearch()
	case "enter":
		nc.ToggleSearch()
		return nc, nc.selectCurrentItem()
	case "backspace":
		if len(nc.searchQuery) > 0 {
			nc.searchQuery = nc.searchQuery[:len(nc.searchQuery)-1]
			nc.updateFilteredItems()
		}
	default:
		if len(msg.String()) == 1 {
			nc.searchQuery += msg.String()
			nc.updateFilteredItems()
		}
	}
	
	return nc, nil
}

// moveUp moves selection up
func (nc *NavigationComponent) moveUp() {
	if nc.selected > 0 {
		nc.selected--
	}
}

// moveDown moves selection down
func (nc *NavigationComponent) moveDown() {
	visibleItems := nc.getVisibleItems()
	if nc.selected < len(visibleItems)-1 {
		nc.selected++
	}
}

// moveLeft moves selection left (for horizontal navigation)
func (nc *NavigationComponent) moveLeft() {
	nc.moveUp()
}

// moveRight moves selection right (for horizontal navigation)
func (nc *NavigationComponent) moveRight() {
	nc.moveDown()
}

// selectCurrentItem selects the current item
func (nc *NavigationComponent) selectCurrentItem() tea.Cmd {
	item := nc.GetSelectedItem()
	if item != nil && item.Enabled {
		// Update usage statistics
		item.LastUsed = time.Now()
		item.UseCount++
		
		// Return command to execute
		return func() tea.Msg {
			return NavigationSelectMsg{
				ItemID:  item.ID,
				Command: item.Command,
			}
		}
	}
	return nil
}

// findItemByKey finds an item by key binding
func (nc *NavigationComponent) findItemByKey(key string) *NavigationItem {
	for _, item := range nc.items {
		if item.KeyBinding == key && item.Enabled {
			return &item
		}
	}
	return nil
}

// getVisibleItems returns currently visible items (filtered or all)
func (nc *NavigationComponent) getVisibleItems() []NavigationItem {
	if nc.searchMode && len(nc.filteredItems) > 0 {
		var items []NavigationItem
		for _, index := range nc.filteredItems {
			if index < len(nc.items) {
				items = append(items, nc.items[index])
			}
		}
		return items
	}
	return nc.items
}

// updateFilteredItems updates the filtered items based on search query
func (nc *NavigationComponent) updateFilteredItems() {
	nc.filteredItems = make([]int, 0)
	
	if nc.searchQuery == "" {
		// No filter, show all items
		for i := range nc.items {
			nc.filteredItems = append(nc.filteredItems, i)
		}
	} else {
		// Filter items based on search query
		query := strings.ToLower(nc.searchQuery)
		for i, item := range nc.items {
			if strings.Contains(strings.ToLower(item.Label), query) ||
				strings.Contains(strings.ToLower(item.Command), query) {
				nc.filteredItems = append(nc.filteredItems, i)
			}
		}
	}
	
	// Reset selection if out of bounds
	if nc.selected >= len(nc.filteredItems) {
		nc.selected = 0
	}
}

// renderBreadcrumbs renders breadcrumb navigation
func (nc *NavigationComponent) renderBreadcrumbs() string {
	if len(nc.breadcrumbs) == 0 {
		return ""
	}
	
	var content strings.Builder
	
	breadcrumbStyle := lipgloss.NewStyle().
		Foreground(nc.theme.Colors.TextSecondary)
	
	separatorStyle := lipgloss.NewStyle().
		Foreground(nc.theme.Colors.TextMuted)
	
	for i, breadcrumb := range nc.breadcrumbs {
		if breadcrumb.Icon != "" {
			content.WriteString(breadcrumb.Icon + " ")
		}
		content.WriteString(breadcrumbStyle.Render(breadcrumb.Label))
		
		if i < len(nc.breadcrumbs)-1 {
			content.WriteString(separatorStyle.Render(" > "))
		}
	}
	
	return content.String()
}

// renderSearchBar renders the search bar
func (nc *NavigationComponent) renderSearchBar() string {
	searchStyle := lipgloss.NewStyle().
		Foreground(nc.theme.Colors.Text).
		Background(nc.theme.Colors.Surface).
		Border(lipgloss.NormalBorder()).
		BorderForeground(nc.theme.Colors.BorderFocus).
		Padding(0, 1).
		Width(nc.width - 4)
	
	prompt := "Search: "
	cursor := "│"
	
	content := prompt + nc.searchQuery + cursor
	
	return searchStyle.Render(content)
}

// renderHorizontal renders horizontal navigation
func (nc *NavigationComponent) renderHorizontal() string {
	visibleItems := nc.getVisibleItems()
	var items []string
	
	for i, item := range visibleItems {
		style := nc.getItemStyle(i, item)
		content := nc.formatItem(item)
		items = append(items, style.Render(content))
	}
	
	return lipgloss.JoinHorizontal(lipgloss.Center, items...)
}

// renderVertical renders vertical navigation
func (nc *NavigationComponent) renderVertical() string {
	visibleItems := nc.getVisibleItems()
	var content strings.Builder
	
	for i, item := range visibleItems {
		style := nc.getItemStyle(i, item)
		itemContent := nc.formatItem(item)
		
		if i == nc.selected {
			itemContent = "▶ " + itemContent
		} else {
			itemContent = "  " + itemContent
		}
		
		content.WriteString(style.Render(itemContent))
		if i < len(visibleItems)-1 {
			content.WriteString("\n")
		}
	}
	
	return content.String()
}

// renderTabs renders tab-style navigation
func (nc *NavigationComponent) renderTabs() string {
	visibleItems := nc.getVisibleItems()
	var tabs []string
	
	for i, item := range visibleItems {
		var style lipgloss.Style
		
		if i == nc.selected {
			style = lipgloss.NewStyle().
				Background(nc.theme.Colors.Primary).
				Foreground(nc.theme.Colors.Background).
				Bold(true).
				Padding(0, 2).
				Border(lipgloss.Border{
					Top:    "─",
					Bottom: " ",
					Left:   "│",
					Right:  "│",
				})
		} else {
			style = lipgloss.NewStyle().
				Background(nc.theme.Colors.Surface).
				Foreground(nc.theme.Colors.Text).
				Padding(0, 2).
				Border(lipgloss.Border{
					Top:    "─",
					Bottom: "─",
					Left:   "│",
					Right:  "│",
				})
		}
		
		content := nc.formatItem(item)
		tabs = append(tabs, style.Render(content))
	}
	
	return lipgloss.JoinHorizontal(lipgloss.Bottom, tabs...)
}

// renderSidebar renders sidebar-style navigation
func (nc *NavigationComponent) renderSidebar() string {
	var content strings.Builder
	
	// Title
	titleStyle := lipgloss.NewStyle().
		Foreground(nc.theme.Colors.Primary).
		Bold(true).
		Padding(0, 1)
	
	content.WriteString(titleStyle.Render("Navigation"))
	content.WriteString("\n\n")
	
	// Items
	content.WriteString(nc.renderVertical())
	
	return content.String()
}

// renderQuickAccess renders quick access items
func (nc *NavigationComponent) renderQuickAccess() string {
	if len(nc.quickAccess) == 0 {
		return ""
	}
	
	var content strings.Builder
	
	// Title
	titleStyle := lipgloss.NewStyle().
		Foreground(nc.theme.Colors.Secondary).
		Bold(true).
		Padding(0, 1)
	
	content.WriteString(titleStyle.Render("Quick Access"))
	content.WriteString("\n")
	
	// Items
	for _, item := range nc.quickAccess {
		itemStyle := lipgloss.NewStyle().
			Foreground(nc.theme.Colors.TextSecondary).
			Padding(0, 1)
		
		itemContent := ""
		if item.Icon != "" {
			itemContent += item.Icon + " "
		}
		itemContent += item.Label
		if item.KeyBinding != "" && nc.showKeys {
			itemContent += fmt.Sprintf(" (%s)", item.KeyBinding)
		}
		
		content.WriteString(itemStyle.Render(itemContent))
		content.WriteString("\n")
	}
	
	return content.String()
}

// getItemStyle returns the style for an item
func (nc *NavigationComponent) getItemStyle(index int, item NavigationItem) lipgloss.Style {
	if !item.Enabled {
		return lipgloss.NewStyle().
			Foreground(nc.theme.Colors.TextMuted).
			Strikethrough(true)
	}
	
	if index == nc.selected {
		return lipgloss.NewStyle().
			Background(nc.theme.Colors.Primary).
			Foreground(nc.theme.Colors.Background).
			Bold(true).
			Padding(0, 1)
	}
	
	return lipgloss.NewStyle().
		Foreground(nc.theme.Colors.Text).
		Padding(0, 1)
}

// formatItem formats an item for display
func (nc *NavigationComponent) formatItem(item NavigationItem) string {
	var content strings.Builder
	
	if item.Icon != "" && nc.showIcons {
		content.WriteString(item.Icon + " ")
	}
	
	content.WriteString(item.Label)
	
	if item.Badge != "" {
		badgeStyle := lipgloss.NewStyle().
			Background(item.BadgeColor).
			Foreground(nc.theme.Colors.Background).
			Padding(0, 1).
			Margin(0, 1)
		
		content.WriteString(badgeStyle.Render(item.Badge))
	}
	
	if item.KeyBinding != "" && nc.showKeys {
		content.WriteString(fmt.Sprintf(" (%s)", item.KeyBinding))
	}
	
	return content.String()
}

// loadDefaultItems loads default navigation items
func (nc *NavigationComponent) loadDefaultItems() {
	defaultItems := []NavigationItem{
		{ID: "chat", Label: "Chat", Icon: "💬", KeyBinding: "c", Command: "chat", Enabled: true},
		{ID: "tasks", Label: "Tasks", Icon: "📋", KeyBinding: "t", Command: "tasks", Enabled: true},
		{ID: "tools", Label: "Tools", Icon: "🔧", KeyBinding: "o", Command: "tools", Enabled: true},
		{ID: "config", Label: "Config", Icon: "⚙️", KeyBinding: "g", Command: "config", Enabled: true},
		{ID: "help", Label: "Help", Icon: "❓", KeyBinding: "h", Command: "help", Enabled: true},
	}
	
	nc.items = defaultItems
}

// NavigationSelectMsg represents a navigation selection message
type NavigationSelectMsg struct {
	ItemID  string
	Command string
}
