/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package core

import (
	"context"
	"testing"
	"time"

	"arien/internal/utils"
)

func TestNewEngine(t *testing.T) {
	logger := utils.NewLogger()
	ctx := context.Background()

	engine, err := NewEngine(ctx, logger)
	if err != nil {
		t.Fatalf("Failed to create engine: %v", err)
	}

	if engine == nil {
		t.Fatal("Engine is nil")
	}

	if engine.Config() == nil {
		t.Fatal("Config is nil")
	}

	if engine.LLM() == nil {
		t.Fatal("LLM manager is nil")
	}

	if engine.Tools() == nil {
		t.Fatal("Tool manager is nil")
	}

	if engine.Memory() == nil {
		t.Fatal("Memory is nil")
	}

	if engine.Tasks() == nil {
		t.Fatal("Task manager is nil")
	}
}

func TestEngineProcessMessage(t *testing.T) {
	logger := utils.NewLogger()
	ctx := context.Background()

	engine, err := NewEngine(ctx, logger)
	if err != nil {
		t.Fatalf("Failed to create engine: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// This will fail since we don't have real LLM providers configured
	// but it tests the basic flow
	_, err = engine.ProcessMessage(ctx, "Hello, world!")
	if err == nil {
		t.Log("ProcessMessage succeeded (unexpected but OK)")
	} else {
		t.Logf("ProcessMessage failed as expected: %v", err)
	}
}

func TestEngineShutdown(t *testing.T) {
	logger := utils.NewLogger()
	ctx := context.Background()

	engine, err := NewEngine(ctx, logger)
	if err != nil {
		t.Fatalf("Failed to create engine: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err = engine.Shutdown(ctx)
	if err != nil {
		t.Fatalf("Failed to shutdown engine: %v", err)
	}
}
