/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/schollz/progressbar/v3"
)

// ReadManyFilesTool implements batch file operations with progress indicators
type ReadManyFilesTool struct {
	maxConcurrent int
	maxFileSize   int64
}

// NewReadManyFilesTool creates a new read many files tool
func NewReadManyFilesTool() *ReadManyFilesTool {
	return &ReadManyFilesTool{
		maxConcurrent: 10,
		maxFileSize:   10 * 1024 * 1024, // 10MB default
	}
}

// Name returns the tool name
func (t *ReadManyFilesTool) Name() string {
	return "read-many-files"
}

// Description returns the tool description
func (t *ReadManyFilesTool) Description() string {
	return "Read multiple files with progress indicators and error aggregation"
}

// Parameters returns the tool parameter schema
func (t *ReadManyFilesTool) Parameters() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"files": map[string]interface{}{
				"type":        "array",
				"description": "Array of file paths to read",
				"items": map[string]interface{}{
					"type": "string",
				},
				"minItems": 1,
				"maxItems": 100,
			},
			"pattern": StringParameter("Glob pattern to match files (alternative to files array)", false),
			"recursive": BoolParameter("Search recursively when using pattern", false),
			"max_size": StringParameter("Maximum file size to read (e.g., '1MB', '500KB')", false),
			"encoding": StringParameter("Text encoding (utf-8, ascii, latin1)", false),
			"include_metadata": BoolParameter("Include file metadata in results", false),
			"show_progress": BoolParameter("Show progress bar during operation", true),
			"concurrent": IntParameter("Number of concurrent file reads", 1, 20),
		},
		"anyOf": []map[string]interface{}{
			{"required": []string{"files"}},
			{"required": []string{"pattern"}},
		},
	}
}

// FileResult represents the result of reading a single file
type FileResult struct {
	Path     string                 `json:"path"`
	Content  string                 `json:"content,omitempty"`
	Error    string                 `json:"error,omitempty"`
	Size     int64                  `json:"size"`
	Modified time.Time              `json:"modified,omitempty"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// BatchReadResult represents the result of reading multiple files
type BatchReadResult struct {
	Files       []FileResult `json:"files"`
	TotalFiles  int          `json:"total_files"`
	SuccessCount int         `json:"success_count"`
	ErrorCount   int         `json:"error_count"`
	TotalSize    int64       `json:"total_size"`
	Duration     time.Duration `json:"duration"`
	Errors       []string     `json:"errors,omitempty"`
}

// Execute performs the batch file reading operation
func (t *ReadManyFilesTool) Execute(ctx context.Context, args map[string]interface{}) Result {
	start := time.Now()
	
	// Parse arguments
	var filePaths []string
	var err error
	
	if files, ok := args["files"].([]interface{}); ok {
		filePaths = make([]string, len(files))
		for i, f := range files {
			if path, ok := f.(string); ok {
				filePaths[i] = path
			} else {
				return Result{Error: fmt.Errorf("invalid file path at index %d", i)}
			}
		}
	} else if pattern, ok := args["pattern"].(string); ok {
		recursive := getBoolArg(args, "recursive", false)
		filePaths, err = t.findFilesByPattern(pattern, recursive)
		if err != nil {
			return Result{Error: fmt.Errorf("failed to find files by pattern: %w", err)}
		}
	} else {
		return Result{Error: fmt.Errorf("either 'files' array or 'pattern' must be provided")}
	}
	
	if len(filePaths) == 0 {
		return Result{Output: "No files found to read"}
	}
	
	// Parse other arguments
	maxSize := t.parseMaxSize(getStringArg(args, "max_size", ""))
	encoding := getStringArg(args, "encoding", "utf-8")
	includeMetadata := getBoolArg(args, "include_metadata", false)
	showProgress := getBoolArg(args, "show_progress", true)
	concurrent := getIntArg(args, "concurrent", 5)
	
	if concurrent > t.maxConcurrent {
		concurrent = t.maxConcurrent
	}
	
	// Initialize progress bar
	var bar *progressbar.ProgressBar
	if showProgress {
		bar = progressbar.NewOptions(len(filePaths),
			progressbar.OptionSetDescription("Reading files..."),
			progressbar.OptionSetWidth(50),
			progressbar.OptionShowCount(),
			progressbar.OptionShowIts(),
			progressbar.OptionSetTheme(progressbar.Theme{
				Saucer:        "█",
				SaucerHead:    "█",
				SaucerPadding: "░",
				BarStart:      "│",
				BarEnd:        "│",
			}),
		)
	}
	
	// Perform batch read operation
	result := t.readFilesParallel(ctx, filePaths, maxSize, encoding, includeMetadata, concurrent, bar)
	result.Duration = time.Since(start)
	
	if bar != nil {
		bar.Finish()
		fmt.Println() // Add newline after progress bar
	}
	
	// Format output
	output := t.formatBatchResult(result)
	
	return Result{
		Output: output,
		Data:   result,
	}
}

// readFilesParallel reads multiple files concurrently
func (t *ReadManyFilesTool) readFilesParallel(ctx context.Context, filePaths []string, maxSize int64, encoding string, includeMetadata bool, concurrent int, bar *progressbar.ProgressBar) *BatchReadResult {
	result := &BatchReadResult{
		Files:      make([]FileResult, len(filePaths)),
		TotalFiles: len(filePaths),
	}
	
	// Create semaphore for concurrency control
	sem := make(chan struct{}, concurrent)
	var wg sync.WaitGroup
	var mu sync.Mutex
	
	for i, path := range filePaths {
		wg.Add(1)
		go func(index int, filePath string) {
			defer wg.Done()
			
			// Acquire semaphore
			sem <- struct{}{}
			defer func() { <-sem }()
			
			// Check context cancellation
			select {
			case <-ctx.Done():
				return
			default:
			}
			
			// Read file
			fileResult := t.readSingleFile(filePath, maxSize, encoding, includeMetadata)
			
			// Update results
			mu.Lock()
			result.Files[index] = fileResult
			result.TotalSize += fileResult.Size
			if fileResult.Error == "" {
				result.SuccessCount++
			} else {
				result.ErrorCount++
				result.Errors = append(result.Errors, fmt.Sprintf("%s: %s", fileResult.Path, fileResult.Error))
			}
			mu.Unlock()
			
			// Update progress bar
			if bar != nil {
				bar.Add(1)
			}
		}(i, path)
	}
	
	wg.Wait()
	return result
}

// readSingleFile reads a single file and returns the result
func (t *ReadManyFilesTool) readSingleFile(filePath string, maxSize int64, encoding string, includeMetadata bool) FileResult {
	result := FileResult{Path: filePath}
	
	// Get file info
	info, err := os.Stat(filePath)
	if err != nil {
		result.Error = fmt.Sprintf("failed to stat file: %v", err)
		return result
	}
	
	result.Size = info.Size()
	result.Modified = info.ModTime()
	
	// Check file size
	if maxSize > 0 && info.Size() > maxSize {
		result.Error = fmt.Sprintf("file size (%d bytes) exceeds maximum (%d bytes)", info.Size(), maxSize)
		return result
	}
	
	// Read file content
	file, err := os.Open(filePath)
	if err != nil {
		result.Error = fmt.Sprintf("failed to open file: %v", err)
		return result
	}
	defer file.Close()
	
	content, err := io.ReadAll(file)
	if err != nil {
		result.Error = fmt.Sprintf("failed to read file: %v", err)
		return result
	}
	
	// Convert content based on encoding
	result.Content = string(content)
	
	// Add metadata if requested
	if includeMetadata {
		result.Metadata = map[string]interface{}{
			"mode":    info.Mode().String(),
			"is_dir":  info.IsDir(),
			"ext":     filepath.Ext(filePath),
			"dir":     filepath.Dir(filePath),
			"base":    filepath.Base(filePath),
		}
	}
	
	return result
}

// findFilesByPattern finds files matching a glob pattern
func (t *ReadManyFilesTool) findFilesByPattern(pattern string, recursive bool) ([]string, error) {
	var files []string
	
	if recursive {
		// Use filepath.Walk for recursive search
		err := filepath.Walk(".", func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return nil // Skip files with errors
			}
			
			if !info.IsDir() {
				matched, err := filepath.Match(pattern, filepath.Base(path))
				if err != nil {
					return nil // Skip invalid patterns
				}
				if matched {
					files = append(files, path)
				}
			}
			return nil
		})
		return files, err
	} else {
		// Use filepath.Glob for non-recursive search
		return filepath.Glob(pattern)
	}
}

// parseMaxSize parses a size string like "1MB", "500KB" into bytes
func (t *ReadManyFilesTool) parseMaxSize(sizeStr string) int64 {
	if sizeStr == "" {
		return t.maxFileSize
	}
	
	sizeStr = strings.ToUpper(strings.TrimSpace(sizeStr))
	
	var multiplier int64 = 1
	if strings.HasSuffix(sizeStr, "KB") {
		multiplier = 1024
		sizeStr = strings.TrimSuffix(sizeStr, "KB")
	} else if strings.HasSuffix(sizeStr, "MB") {
		multiplier = 1024 * 1024
		sizeStr = strings.TrimSuffix(sizeStr, "MB")
	} else if strings.HasSuffix(sizeStr, "GB") {
		multiplier = 1024 * 1024 * 1024
		sizeStr = strings.TrimSuffix(sizeStr, "GB")
	}
	
	var size int64
	fmt.Sscanf(sizeStr, "%d", &size)
	return size * multiplier
}

// formatBatchResult formats the batch read result for display
func (t *ReadManyFilesTool) formatBatchResult(result *BatchReadResult) string {
	var output strings.Builder
	
	output.WriteString(fmt.Sprintf("📁 Batch File Read Results\n"))
	output.WriteString(fmt.Sprintf("Total Files: %d | Success: %d | Errors: %d\n", 
		result.TotalFiles, result.SuccessCount, result.ErrorCount))
	output.WriteString(fmt.Sprintf("Total Size: %s | Duration: %v\n\n", 
		formatBytes(result.TotalSize), result.Duration))
	
	// Show successful files
	if result.SuccessCount > 0 {
		output.WriteString("✅ Successfully Read Files:\n")
		for _, file := range result.Files {
			if file.Error == "" {
				output.WriteString(fmt.Sprintf("  • %s (%s)\n", file.Path, formatBytes(file.Size)))
			}
		}
		output.WriteString("\n")
	}
	
	// Show errors
	if result.ErrorCount > 0 {
		output.WriteString("❌ Errors:\n")
		for _, errMsg := range result.Errors {
			output.WriteString(fmt.Sprintf("  • %s\n", errMsg))
		}
	}
	
	return output.String()
}

// formatBytes formats byte count as human-readable string
func formatBytes(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

// Validate validates the tool arguments
func (t *ReadManyFilesTool) Validate(args map[string]interface{}) error {
	// Check that either files or pattern is provided
	_, hasFiles := args["files"]
	_, hasPattern := args["pattern"]
	
	if !hasFiles && !hasPattern {
		return fmt.Errorf("either 'files' array or 'pattern' must be provided")
	}
	
	if hasFiles && hasPattern {
		return fmt.Errorf("cannot specify both 'files' and 'pattern'")
	}
	
	// Validate files array if provided
	if hasFiles {
		files, ok := args["files"].([]interface{})
		if !ok {
			return fmt.Errorf("'files' must be an array")
		}
		
		if len(files) == 0 {
			return fmt.Errorf("'files' array cannot be empty")
		}
		
		if len(files) > 100 {
			return fmt.Errorf("'files' array cannot contain more than 100 items")
		}
		
		for i, f := range files {
			if _, ok := f.(string); !ok {
				return fmt.Errorf("file path at index %d must be a string", i)
			}
		}
	}
	
	// Validate concurrent parameter
	if concurrent, ok := args["concurrent"]; ok {
		if c, ok := concurrent.(int); ok {
			if c < 1 || c > 20 {
				return fmt.Errorf("concurrent must be between 1 and 20")
			}
		}
	}
	
	return nil
}

// SupportsParallel returns whether this tool supports parallel execution
func (t *ReadManyFilesTool) SupportsParallel() bool {
	return true
}
