/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"fmt"
	"os/exec"
	"runtime"
	"time"

	"arien/internal/security"
)

// ShellTool implements shell command execution
type ShellTool struct{
	validator *security.Validator
}

// NewShellTool creates a new shell tool
func NewShellTool() *ShellTool {
	return &ShellTool{
		validator: security.NewValidator(),
	}
}

// Name returns the tool name
func (t *ShellTool) Name() string {
	return "shell"
}

// Description returns the tool description
func (t *ShellTool) Description() string {
	return "Execute shell commands with process management and terminal interaction"
}

// Parameters returns the tool parameter schema
func (t *ShellTool) Parameters() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"command": StringParameter("Shell command to execute", true),
			"working_dir": StringParameter("Working directory for command execution", false),
			"timeout": IntParameter("Timeout in seconds (default: 30)", 1, 300),
			"background": <PERSON>olPara<PERSON>("Run command in background", false),
			"shell": StringParameter("Shell to use (auto-detected if not specified)", false),
			"env": map[string]interface{}{
				"type": "object",
				"description": "Environment variables to set",
				"additionalProperties": map[string]interface{}{
					"type": "string",
				},
			},
		},
		"required": []string{"command"},
	}
}

// Execute executes the shell tool
func (t *ShellTool) Execute(ctx context.Context, args map[string]interface{}) Result {
	// Parse arguments
	command, ok := args["command"].(string)
	if !ok || command == "" {
		return Result{Error: fmt.Errorf("command is required")}
	}

	workingDir := ""
	if wd, ok := args["working_dir"].(string); ok {
		workingDir = wd
	}

	timeout := 30
	if t, ok := args["timeout"].(float64); ok && t > 0 {
		timeout = int(t)
	}

	background := false
	if bg, ok := args["background"].(bool); ok {
		background = bg
	}

	shell := ""
	if sh, ok := args["shell"].(string); ok {
		shell = sh
	}

	env := make(map[string]string)
	if envMap, ok := args["env"].(map[string]interface{}); ok {
		for k, v := range envMap {
			if str, ok := v.(string); ok {
				env[k] = str
			}
		}
	}

	// Execute command
	output, metadata, err := t.executeCommand(ctx, command, workingDir, timeout, background, shell, env)
	if err != nil {
		return Result{Error: err}
	}

	return Result{
		Output:   output,
		Metadata: metadata,
	}
}

// Validate validates the tool arguments
func (t *ShellTool) Validate(args map[string]interface{}) error {
	command, ok := args["command"].(string)
	if !ok || command == "" {
		return fmt.Errorf("command is required")
	}

	// Use security validator for comprehensive command validation
	if err := t.validator.ValidateCommand(command); err != nil {
		return fmt.Errorf("command validation failed: %w", err)
	}

	if timeout, ok := args["timeout"].(float64); ok {
		if timeout <= 0 || timeout > 300 {
			return fmt.Errorf("timeout must be between 1 and 300 seconds")
		}
	}

	return nil
}

// SupportsParallel returns true if the tool can be executed in parallel
func (t *ShellTool) SupportsParallel() bool {
	return false // Shell commands should be sequential to avoid conflicts
}

// executeCommand performs the actual command execution
func (t *ShellTool) executeCommand(ctx context.Context, command, workingDir string, timeout int, background bool, shell string, env map[string]string) (string, map[string]interface{}, error) {
	// Determine shell and command format
	var cmd *exec.Cmd
	if shell == "" {
		shell = t.getDefaultShell()
	}

	switch runtime.GOOS {
	case "windows":
		if shell == "" || shell == "powershell" {
			cmd = exec.CommandContext(ctx, "powershell", "-Command", command)
		} else {
			cmd = exec.CommandContext(ctx, "cmd", "/C", command)
		}
	default:
		if shell == "" {
			shell = "/bin/sh"
		}
		cmd = exec.CommandContext(ctx, shell, "-c", command)
	}

	// Set working directory
	if workingDir != "" {
		cmd.Dir = workingDir
	}

	// Set environment variables
	if len(env) > 0 {
		cmd.Env = append(cmd.Env, cmd.Environ()...)
		for k, v := range env {
			cmd.Env = append(cmd.Env, fmt.Sprintf("%s=%s", k, v))
		}
	}

	// Create timeout context
	_, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Second)
	defer cancel()

	startTime := time.Now()

	if background {
		// Start command in background
		err := cmd.Start()
		if err != nil {
			return "", nil, fmt.Errorf("failed to start background command: %w", err)
		}

		metadata := map[string]interface{}{
			"command":     command,
			"shell":       shell,
			"working_dir": workingDir,
			"background":  true,
			"pid":         cmd.Process.Pid,
			"started_at":  startTime,
		}

		return fmt.Sprintf("Background command started with PID: %d", cmd.Process.Pid), metadata, nil
	}

	// Execute command and capture output
	output, err := cmd.CombinedOutput()
	duration := time.Since(startTime)

	metadata := map[string]interface{}{
		"command":     command,
		"shell":       shell,
		"working_dir": workingDir,
		"background":  false,
		"duration":    duration.String(),
		"exit_code":   0,
	}

	if err != nil {
		if exitError, ok := err.(*exec.ExitError); ok {
			metadata["exit_code"] = exitError.ExitCode()
		}
		return string(output), metadata, fmt.Errorf("command failed: %w", err)
	}

	return string(output), metadata, nil
}

// getDefaultShell returns the default shell for the current OS
func (t *ShellTool) getDefaultShell() string {
	switch runtime.GOOS {
	case "windows":
		return "powershell"
	case "darwin", "linux":
		return "/bin/bash"
	default:
		return "/bin/sh"
	}
}
