/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"
)

// WebSearchToolImpl implements web search functionality
type WebSearchToolImpl struct{}

// NewWebSearchTool creates a new web search tool
func NewWebSearchTool() *WebSearchToolImpl {
	return &WebSearchToolImpl{}
}

// Name returns the tool name
func (t *WebSearchToolImpl) Name() string {
	return "web-search"
}

// Description returns the tool description
func (t *WebSearchToolImpl) Description() string {
	return "Search the web using Google Custom Search API"
}

// Parameters returns the tool parameter schema
func (t *WebSearchToolImpl) Parameters() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"query": StringParameter("Search query", true),
			"num_results": IntParameter("Number of results to return (default: 5)", 1, 10),
			"site": StringParameter("Restrict search to specific site", false),
			"filetype": StringParameter("Restrict search to specific file type", false),
			"date_range": StringParameter("Date range filter (day, week, month, year)", false),
		},
		"required": []string{"query"},
	}
}

// Execute executes the web search tool
func (t *WebSearchToolImpl) Execute(ctx context.Context, args map[string]interface{}) Result {
	query, ok := args["query"].(string)
	if !ok || query == "" {
		return Result{Error: fmt.Errorf("query is required")}
	}

	numResults := 5
	if nr, ok := args["num_results"].(float64); ok && nr > 0 {
		numResults = int(nr)
	}

	site := ""
	if s, ok := args["site"].(string); ok {
		site = s
	}

	filetype := ""
	if ft, ok := args["filetype"].(string); ok {
		filetype = ft
	}

	dateRange := ""
	if dr, ok := args["date_range"].(string); ok {
		dateRange = dr
	}

	// Try to get API credentials from environment
	apiKey := os.Getenv("GOOGLE_SEARCH_API_KEY")
	searchEngineID := os.Getenv("GOOGLE_SEARCH_ENGINE_ID")

	var results string
	if apiKey != "" && searchEngineID != "" {
		// Use real Google Custom Search API
		searchResults, err := t.performRealSearch(ctx, query, numResults, apiKey, searchEngineID, site, filetype, dateRange)
		if err != nil {
			// Fall back to placeholder if API fails
			results = t.createPlaceholderResults(query, numResults, site, filetype, dateRange)
			results += fmt.Sprintf("\nNote: API search failed (%v), showing placeholder results.\n", err)
		} else {
			results = t.formatRealResults(searchResults, query)
		}
	} else {
		// Use placeholder results if no API credentials
		results = t.createPlaceholderResults(query, numResults, site, filetype, dateRange)
	}

	metadata := map[string]interface{}{
		"query":       query,
		"num_results": numResults,
		"site":        site,
		"filetype":    filetype,
		"date_range":  dateRange,
		"timestamp":   time.Now(),
	}

	return Result{
		Output:   results,
		Metadata: metadata,
	}
}

// Validate validates the tool arguments
func (t *WebSearchToolImpl) Validate(args map[string]interface{}) error {
	query, ok := args["query"].(string)
	if !ok || query == "" {
		return fmt.Errorf("query is required")
	}

	if numResults, ok := args["num_results"].(float64); ok {
		if numResults < 1 || numResults > 10 {
			return fmt.Errorf("num_results must be between 1 and 10")
		}
	}

	return nil
}

// SupportsParallel returns true if the tool can be executed in parallel
func (t *WebSearchToolImpl) SupportsParallel() bool {
	return true
}

// WebSearchResult represents a web search result
type WebSearchResult struct {
	Title   string `json:"title"`
	URL     string `json:"url"`
	Snippet string `json:"snippet"`
	Source  string `json:"source"`
}

// createPlaceholderResults creates placeholder search results for demonstration
func (t *WebSearchToolImpl) createPlaceholderResults(query string, numResults int, site, filetype, dateRange string) string {
	var output strings.Builder
	
	output.WriteString(fmt.Sprintf("Web Search Results for: '%s'\n", query))
	if site != "" {
		output.WriteString(fmt.Sprintf("Site: %s\n", site))
	}
	if filetype != "" {
		output.WriteString(fmt.Sprintf("File type: %s\n", filetype))
	}
	if dateRange != "" {
		output.WriteString(fmt.Sprintf("Date range: %s\n", dateRange))
	}
	output.WriteString(strings.Repeat("-", 50) + "\n\n")

	// Create placeholder results
	results := []WebSearchResult{
		{
			Title:   fmt.Sprintf("Example Result 1 for '%s'", query),
			URL:     "https://example.com/result1",
			Snippet: fmt.Sprintf("This is a placeholder search result for the query '%s'. In a real implementation, this would be actual search results from Google Custom Search API.", query),
			Source:  "example.com",
		},
		{
			Title:   fmt.Sprintf("Documentation for '%s'", query),
			URL:     "https://docs.example.com/guide",
			Snippet: fmt.Sprintf("Official documentation and guides related to '%s'. This would contain comprehensive information about the topic.", query),
			Source:  "docs.example.com",
		},
		{
			Title:   fmt.Sprintf("Tutorial: Getting Started with '%s'", query),
			URL:     "https://tutorial.example.com/getting-started",
			Snippet: fmt.Sprintf("A comprehensive tutorial covering the basics of '%s' with step-by-step instructions and examples.", query),
			Source:  "tutorial.example.com",
		},
	}

	// Limit results to requested number
	if numResults < len(results) {
		results = results[:numResults]
	}

	for i, result := range results {
		output.WriteString(fmt.Sprintf("%d. %s\n", i+1, result.Title))
		output.WriteString(fmt.Sprintf("   URL: %s\n", result.URL))
		output.WriteString(fmt.Sprintf("   %s\n", result.Snippet))
		output.WriteString(fmt.Sprintf("   Source: %s\n\n", result.Source))
	}

	output.WriteString("Note: This is a placeholder implementation. To enable real web search, configure Google Custom Search API credentials.\n")

	return output.String()
}

// performRealSearch performs actual web search using Google Custom Search API
func (t *WebSearchToolImpl) performRealSearch(ctx context.Context, query string, numResults int, apiKey, searchEngineID, site, filetype, dateRange string) ([]WebSearchResult, error) {
	baseURL := "https://www.googleapis.com/customsearch/v1"

	params := url.Values{}
	params.Set("key", apiKey)
	params.Set("cx", searchEngineID)
	params.Set("q", query)
	params.Set("num", fmt.Sprintf("%d", numResults))

	// Add optional parameters
	if site != "" {
		params.Set("siteSearch", site)
	}
	if filetype != "" {
		params.Set("fileType", filetype)
	}
	if dateRange != "" {
		params.Set("dateRestrict", dateRange)
	}

	searchURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())

	req, err := http.NewRequestWithContext(ctx, "GET", searchURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("search request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("search API returned status %d", resp.StatusCode)
	}

	var searchResponse struct {
		Items []struct {
			Title   string `json:"title"`
			Link    string `json:"link"`
			Snippet string `json:"snippet"`
		} `json:"items"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&searchResponse); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	var results []WebSearchResult
	for _, item := range searchResponse.Items {
		results = append(results, WebSearchResult{
			Title:   item.Title,
			URL:     item.Link,
			Snippet: item.Snippet,
			Source:  extractDomain(item.Link),
		})
	}

	return results, nil
}

// formatRealResults formats real search results
func (t *WebSearchToolImpl) formatRealResults(results []WebSearchResult, query string) string {
	var output strings.Builder

	output.WriteString(fmt.Sprintf("Web Search Results for: '%s'\n", query))
	output.WriteString(strings.Repeat("-", 50) + "\n\n")

	if len(results) == 0 {
		output.WriteString("No results found.\n")
		return output.String()
	}

	for i, result := range results {
		output.WriteString(fmt.Sprintf("%d. %s\n", i+1, result.Title))
		output.WriteString(fmt.Sprintf("   URL: %s\n", result.URL))
		output.WriteString(fmt.Sprintf("   %s\n", result.Snippet))
		output.WriteString(fmt.Sprintf("   Source: %s\n\n", result.Source))
	}

	output.WriteString(fmt.Sprintf("Found %d results using Google Custom Search API.\n", len(results)))

	return output.String()
}

// extractDomain extracts domain from URL
func extractDomain(urlStr string) string {
	u, err := url.Parse(urlStr)
	if err != nil {
		return urlStr
	}
	return u.Host
}
