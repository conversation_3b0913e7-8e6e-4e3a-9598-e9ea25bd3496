/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// Task represents a single task
type Task struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Status      string                 `json:"status"`
	Priority    string                 `json:"priority"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	DueDate     *time.Time             `json:"due_date,omitempty"`
	Tags        []string               `json:"tags"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// TaskStore represents the task storage structure
type TaskStore struct {
	Tasks   []Task `json:"tasks"`
	Version string `json:"version"`
}

// TaskToolImpl implements task management
type TaskToolImpl struct {
	taskFile string
}

// NewTaskTool creates a new task tool
func NewTaskTool() *TaskToolImpl {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		homeDir = "."
	}

	taskFile := filepath.Join(homeDir, ".arien-tasks.json")

	return &TaskToolImpl{
		taskFile: taskFile,
	}
}

// Name returns the tool name
func (t *TaskToolImpl) Name() string {
	return "tasks"
}

// Description returns the tool description
func (t *TaskToolImpl) Description() string {
	return "Manage tasks and project planning"
}

// Parameters returns the tool parameter schema
func (t *TaskToolImpl) Parameters() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"action": map[string]interface{}{
				"type": "string",
				"description": "Action to perform: create, list, update, delete, stats",
				"enum": []string{"create", "list", "update", "delete", "stats"},
			},
			"name": StringParameter("Task name (for create/update)", false),
			"description": StringParameter("Task description (for create/update)", false),
			"priority": map[string]interface{}{
				"type": "string",
				"description": "Task priority: low, medium, high, urgent",
				"enum": []string{"low", "medium", "high", "urgent"},
				"default": "medium",
			},
			"status": map[string]interface{}{
				"type": "string",
				"description": "Task status: not_started, in_progress, completed, cancelled, blocked",
				"enum": []string{"not_started", "in_progress", "completed", "cancelled", "blocked"},
			},
			"task_id": StringParameter("Task ID (for update/delete)", false),
			"filter_status": StringParameter("Filter tasks by status (for list)", false),
			"filter_priority": StringParameter("Filter tasks by priority (for list)", false),
		},
		"required": []string{"action"},
	}
}

// Execute executes the task tool
func (t *TaskToolImpl) Execute(ctx context.Context, args map[string]interface{}) Result {
	action, ok := args["action"].(string)
	if !ok {
		return Result{Error: fmt.Errorf("action is required")}
	}

	switch action {
	case "create":
		return t.createTask(args)
	case "list":
		return t.listTasks(args)
	case "update":
		return t.updateTask(args)
	case "delete":
		return t.deleteTask(args)
	case "stats":
		return t.getStats(args)
	default:
		return Result{Error: fmt.Errorf("unknown action: %s", action)}
	}
}

// Validate validates the tool arguments
func (t *TaskToolImpl) Validate(args map[string]interface{}) error {
	action, ok := args["action"].(string)
	if !ok {
		return fmt.Errorf("action is required")
	}

	validActions := []string{"create", "list", "update", "delete", "stats"}
	valid := false
	for _, validAction := range validActions {
		if action == validAction {
			valid = true
			break
		}
	}
	if !valid {
		return fmt.Errorf("invalid action: %s", action)
	}

	if action == "create" {
		if name, ok := args["name"].(string); !ok || name == "" {
			return fmt.Errorf("name is required for create action")
		}
	}

	if action == "update" || action == "delete" {
		if taskID, ok := args["task_id"].(string); !ok || taskID == "" {
			return fmt.Errorf("task_id is required for %s action", action)
		}
	}

	return nil
}

// SupportsParallel returns true if the tool can be executed in parallel
func (t *TaskToolImpl) SupportsParallel() bool {
	return true
}

// createTask creates a new task
func (t *TaskToolImpl) createTask(args map[string]interface{}) Result {
	name, _ := args["name"].(string)
	description, _ := args["description"].(string)
	priority, _ := args["priority"].(string)
	if priority == "" {
		priority = "medium"
	}

	store, err := t.loadTaskStore()
	if err != nil {
		return Result{Error: fmt.Errorf("failed to load task store: %w", err)}
	}

	task := Task{
		ID:          fmt.Sprintf("task_%d", time.Now().UnixNano()),
		Name:        name,
		Description: description,
		Status:      "not_started",
		Priority:    priority,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Tags:        []string{},
		Metadata:    make(map[string]interface{}),
	}

	// Add tags if provided
	if tags, ok := args["tags"].([]interface{}); ok {
		for _, tag := range tags {
			if tagStr, ok := tag.(string); ok {
				task.Tags = append(task.Tags, tagStr)
			}
		}
	}

	store.Tasks = append(store.Tasks, task)

	if err := t.saveTaskStore(store); err != nil {
		return Result{Error: fmt.Errorf("failed to save task: %w", err)}
	}

	output := fmt.Sprintf("Task created successfully:\n")
	output += fmt.Sprintf("  ID: %s\n", task.ID)
	output += fmt.Sprintf("  Name: %s\n", task.Name)
	output += fmt.Sprintf("  Description: %s\n", task.Description)
	output += fmt.Sprintf("  Priority: %s\n", task.Priority)
	output += fmt.Sprintf("  Status: %s\n", task.Status)
	output += fmt.Sprintf("  Created: %s\n", task.CreatedAt.Format("2006-01-02 15:04:05"))

	return Result{
		Output: output,
		Metadata: map[string]interface{}{
			"action":   "create",
			"task":     task,
			"task_id":  task.ID,
			"priority": task.Priority,
			"status":   task.Status,
		},
	}
}

// listTasks lists tasks with optional filtering
func (t *TaskToolImpl) listTasks(args map[string]interface{}) Result {
	filterStatus, _ := args["filter_status"].(string)
	filterPriority, _ := args["filter_priority"].(string)

	store, err := t.loadTaskStore()
	if err != nil {
		return Result{Error: fmt.Errorf("failed to load task store: %w", err)}
	}

	var output strings.Builder
	output.WriteString("Task List:\n")
	output.WriteString(strings.Repeat("-", 50) + "\n")

	if filterStatus != "" {
		output.WriteString(fmt.Sprintf("Filtered by status: %s\n", filterStatus))
	}
	if filterPriority != "" {
		output.WriteString(fmt.Sprintf("Filtered by priority: %s\n", filterPriority))
	}
	if filterStatus != "" || filterPriority != "" {
		output.WriteString("\n")
	}

	// Apply filters
	filteredTasks := []Task{}
	for _, task := range store.Tasks {
		if filterStatus != "" && task.Status != filterStatus {
			continue
		}
		if filterPriority != "" && task.Priority != filterPriority {
			continue
		}
		filteredTasks = append(filteredTasks, task)
	}

	if len(filteredTasks) == 0 {
		output.WriteString("No tasks found matching the criteria.\n")
	} else {
		for i, task := range filteredTasks {
			output.WriteString(fmt.Sprintf("%d. %s [%s]\n", i+1, task.Name, task.ID))
			output.WriteString(fmt.Sprintf("   Status: %s | Priority: %s\n", task.Status, task.Priority))
			if task.Description != "" {
				output.WriteString(fmt.Sprintf("   Description: %s\n", task.Description))
			}
			output.WriteString(fmt.Sprintf("   Created: %s\n", task.CreatedAt.Format("2006-01-02 15:04:05")))
			output.WriteString("\n")
		}
	}

	return Result{
		Output: output.String(),
		Metadata: map[string]interface{}{
			"action":          "list",
			"total_tasks":     len(filteredTasks),
			"filter_status":   filterStatus,
			"filter_priority": filterPriority,
		},
	}
}

// updateTask updates an existing task
func (t *TaskToolImpl) updateTask(args map[string]interface{}) Result {
	taskID, _ := args["task_id"].(string)
	name, _ := args["name"].(string)
	description, _ := args["description"].(string)
	priority, _ := args["priority"].(string)
	status, _ := args["status"].(string)

	store, err := t.loadTaskStore()
	if err != nil {
		return Result{Error: fmt.Errorf("failed to load task store: %w", err)}
	}

	// Find the task
	taskIndex := -1
	for i, task := range store.Tasks {
		if task.ID == taskID {
			taskIndex = i
			break
		}
	}

	if taskIndex == -1 {
		return Result{Error: fmt.Errorf("task with ID %s not found", taskID)}
	}

	// Update the task
	task := &store.Tasks[taskIndex]
	updatedFields := []string{}

	if name != "" {
		task.Name = name
		updatedFields = append(updatedFields, "name")
	}
	if description != "" {
		task.Description = description
		updatedFields = append(updatedFields, "description")
	}
	if priority != "" {
		task.Priority = priority
		updatedFields = append(updatedFields, "priority")
	}
	if status != "" {
		task.Status = status
		updatedFields = append(updatedFields, "status")
	}

	task.UpdatedAt = time.Now()

	if err := t.saveTaskStore(store); err != nil {
		return Result{Error: fmt.Errorf("failed to save task: %w", err)}
	}

	output := fmt.Sprintf("Task %s updated successfully:\n", taskID)
	output += fmt.Sprintf("  Name: %s\n", task.Name)
	output += fmt.Sprintf("  Description: %s\n", task.Description)
	output += fmt.Sprintf("  Priority: %s\n", task.Priority)
	output += fmt.Sprintf("  Status: %s\n", task.Status)
	output += fmt.Sprintf("  Updated: %s\n", task.UpdatedAt.Format("2006-01-02 15:04:05"))

	return Result{
		Output: output,
		Metadata: map[string]interface{}{
			"action":         "update",
			"task_id":        taskID,
			"updated_fields": updatedFields,
			"task":           *task,
		},
	}
}

// deleteTask deletes a task
func (t *TaskToolImpl) deleteTask(args map[string]interface{}) Result {
	taskID, _ := args["task_id"].(string)

	store, err := t.loadTaskStore()
	if err != nil {
		return Result{Error: fmt.Errorf("failed to load task store: %w", err)}
	}

	// Find and remove the task
	taskIndex := -1
	var deletedTask Task
	for i, task := range store.Tasks {
		if task.ID == taskID {
			taskIndex = i
			deletedTask = task
			break
		}
	}

	if taskIndex == -1 {
		return Result{Error: fmt.Errorf("task with ID %s not found", taskID)}
	}

	// Remove the task
	store.Tasks = append(store.Tasks[:taskIndex], store.Tasks[taskIndex+1:]...)

	if err := t.saveTaskStore(store); err != nil {
		return Result{Error: fmt.Errorf("failed to save task store: %w", err)}
	}

	output := fmt.Sprintf("Task deleted successfully:\n")
	output += fmt.Sprintf("  ID: %s\n", deletedTask.ID)
	output += fmt.Sprintf("  Name: %s\n", deletedTask.Name)

	return Result{
		Output: output,
		Metadata: map[string]interface{}{
			"action":       "delete",
			"task_id":      taskID,
			"deleted_task": deletedTask,
		},
	}
}

// getStats returns task statistics
func (t *TaskToolImpl) getStats(args map[string]interface{}) Result {
	store, err := t.loadTaskStore()
	if err != nil {
		return Result{Error: fmt.Errorf("failed to load task store: %w", err)}
	}

	// Calculate real statistics
	stats := map[string]int{
		"total":       len(store.Tasks),
		"not_started": 0,
		"in_progress": 0,
		"completed":   0,
		"cancelled":   0,
		"blocked":     0,
	}

	for _, task := range store.Tasks {
		if count, exists := stats[task.Status]; exists {
			stats[task.Status] = count + 1
		}
	}

	var output strings.Builder
	output.WriteString("Task Statistics:\n")
	output.WriteString(strings.Repeat("-", 30) + "\n")
	output.WriteString(fmt.Sprintf("Total Tasks: %d\n", stats["total"]))
	output.WriteString(fmt.Sprintf("Not Started: %d\n", stats["not_started"]))
	output.WriteString(fmt.Sprintf("In Progress: %d\n", stats["in_progress"]))
	output.WriteString(fmt.Sprintf("Completed: %d\n", stats["completed"]))
	output.WriteString(fmt.Sprintf("Cancelled: %d\n", stats["cancelled"]))
	output.WriteString(fmt.Sprintf("Blocked: %d\n", stats["blocked"]))

	// Calculate completion rate
	completionRate := float64(stats["completed"]) / float64(stats["total"]) * 100
	output.WriteString(fmt.Sprintf("\nCompletion Rate: %.1f%%\n", completionRate))

	return Result{
		Output: output.String(),
		Metadata: map[string]interface{}{
			"action": "stats",
			"stats":  stats,
			"completion_rate": completionRate,
		},
	}
}

// loadTaskStore loads the task store from file
func (t *TaskToolImpl) loadTaskStore() (*TaskStore, error) {
	store := &TaskStore{
		Tasks:   []Task{},
		Version: "1.0",
	}

	if _, err := os.Stat(t.taskFile); os.IsNotExist(err) {
		return store, nil
	}

	data, err := os.ReadFile(t.taskFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read task file: %w", err)
	}

	if err := json.Unmarshal(data, store); err != nil {
		return nil, fmt.Errorf("failed to parse task file: %w", err)
	}

	return store, nil
}

// saveTaskStore saves the task store to file
func (t *TaskToolImpl) saveTaskStore(store *TaskStore) error {
	// Ensure directory exists
	dir := filepath.Dir(t.taskFile)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create task directory: %w", err)
	}

	data, err := json.MarshalIndent(store, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal task store: %w", err)
	}

	if err := os.WriteFile(t.taskFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write task file: %w", err)
	}

	return nil
}
