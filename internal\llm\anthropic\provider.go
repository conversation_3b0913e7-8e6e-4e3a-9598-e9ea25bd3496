/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package anthropic

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"arien/internal/config"
	"arien/internal/interfaces"

	"github.com/charmbracelet/log"
	"golang.org/x/time/rate"
)

// AnthropicRequest represents a request to Anthropic API
type AnthropicRequest struct {
	Model         string                    `json:"model"`
	MaxTokens     int                       `json:"max_tokens"`
	Temperature   float64                   `json:"temperature,omitempty"`
	Messages      []AnthropicMessage        `json:"messages"`
	Tools         []AnthropicTool           `json:"tools,omitempty"`
	ToolChoice    interface{}               `json:"tool_choice,omitempty"`
	System        string                    `json:"system,omitempty"`
	StopSequences []string                  `json:"stop_sequences,omitempty"`
}

// AnthropicMessage represents a message in Anthropic format
type AnthropicMessage struct {
	Role    string                 `json:"role"`
	Content interface{}            `json:"content"`
}

// AnthropicContent represents content in Anthropic format
type AnthropicContent struct {
	Type     string                 `json:"type"`
	Text     string                 `json:"text,omitempty"`
	ToolUse  *AnthropicToolUse      `json:"tool_use,omitempty"`
	ToolResult *AnthropicToolResult `json:"tool_result,omitempty"`
}

// AnthropicToolUse represents a tool use
type AnthropicToolUse struct {
	ID    string                 `json:"id"`
	Name  string                 `json:"name"`
	Input map[string]interface{} `json:"input"`
}

// AnthropicToolResult represents a tool result
type AnthropicToolResult struct {
	ToolUseID string      `json:"tool_use_id"`
	Content   interface{} `json:"content"`
}

// AnthropicTool represents a tool definition
type AnthropicTool struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	InputSchema map[string]interface{} `json:"input_schema"`
}

// AnthropicResponse represents a response from Anthropic API
type AnthropicResponse struct {
	ID           string                 `json:"id"`
	Type         string                 `json:"type"`
	Role         string                 `json:"role"`
	Content      []AnthropicContent     `json:"content"`
	Model        string                 `json:"model"`
	StopReason   string                 `json:"stop_reason"`
	StopSequence string                 `json:"stop_sequence"`
	Usage        AnthropicUsage         `json:"usage"`
}

// AnthropicUsage represents usage information
type AnthropicUsage struct {
	InputTokens  int `json:"input_tokens"`
	OutputTokens int `json:"output_tokens"`
}

// Provider implements the Anthropic Claude LLM provider
type Provider struct {
	config     config.ProviderConfig
	logger     *log.Logger
	httpClient *http.Client
	limiter    *rate.Limiter
	baseURL    string
}

// NewProvider creates a new Anthropic provider
func NewProvider(providerConfig config.ProviderConfig, logger *log.Logger) (*Provider, error) {
	if providerConfig.APIKey == "" {
		return nil, fmt.Errorf("Anthropic API key is required")
	}

	return &Provider{
		config:  providerConfig,
		logger:  logger,
		baseURL: "https://api.anthropic.com/v1",
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		limiter: rate.NewLimiter(rate.Every(time.Minute/60), 60), // 60 requests per minute
	}, nil
}

// Name returns the provider name
func (p *Provider) Name() string {
	return "anthropic"
}

// Models returns available models for Anthropic
func (p *Provider) Models() []string {
	return []string{
		"claude-3-opus-20240229",
		"claude-3-sonnet-20240229",
		"claude-3-haiku-20240307",
		"claude-3-5-sonnet-20241022",
		"claude-3-5-haiku-20241022",
	}
}

// ProcessMessage processes a message using Anthropic API
func (p *Provider) ProcessMessage(ctx context.Context, request *interfaces.Request) (*interfaces.Response, error) {
	// Rate limiting
	if err := p.limiter.Wait(ctx); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	// Convert request to Anthropic format
	anthropicReq := p.convertRequest(request)

	// Make API call
	anthropicResp, err := p.makeAPICall(ctx, anthropicReq)
	if err != nil {
		return nil, fmt.Errorf("Anthropic API call failed: %w", err)
	}

	// Convert response to standard format
	response := p.convertResponse(anthropicResp)
	return response, nil
}

// SupportsToolCalling returns true if Anthropic supports function calling
func (p *Provider) SupportsToolCalling() bool {
	return true
}

// SupportsStreaming returns true if Anthropic supports streaming
func (p *Provider) SupportsStreaming() bool {
	return true
}

// convertRequest converts standard request to Anthropic format
func (p *Provider) convertRequest(request *interfaces.Request) *AnthropicRequest {
	anthropicReq := &AnthropicRequest{
		Model:       request.Model,
		MaxTokens:   request.MaxTokens,
		Temperature: request.Temperature,
		Messages:    []AnthropicMessage{},
	}

	var systemMessage string

	// Convert messages
	for _, msg := range request.Messages {
		if msg.Role == "system" {
			systemMessage = msg.Content
			continue
		}

		// Convert message content
		var content interface{}
		if msg.Content != "" && len(msg.ToolCalls) == 0 {
			content = msg.Content
		} else {
			contentArray := []AnthropicContent{}

			if msg.Content != "" {
				contentArray = append(contentArray, AnthropicContent{
					Type: "text",
					Text: msg.Content,
				})
			}

			// Add tool calls
			for _, toolCall := range msg.ToolCalls {
				contentArray = append(contentArray, AnthropicContent{
					Type: "tool_use",
					ToolUse: &AnthropicToolUse{
						ID:    toolCall.ID,
						Name:  toolCall.Name,
						Input: toolCall.Arguments,
					},
				})
			}

			content = contentArray
		}

		anthropicReq.Messages = append(anthropicReq.Messages, AnthropicMessage{
			Role:    msg.Role,
			Content: content,
		})
	}

	// Set system message
	if systemMessage != "" {
		anthropicReq.System = systemMessage
	}

	// Convert tools
	if len(request.Tools) > 0 {
		for _, tool := range request.Tools {
			if tool.Type == "function" {
				anthropicReq.Tools = append(anthropicReq.Tools, AnthropicTool{
					Name:        tool.Function.Name,
					Description: tool.Function.Description,
					InputSchema: tool.Function.Parameters,
				})
			}
		}
		anthropicReq.ToolChoice = "auto"
	}

	return anthropicReq
}

// makeAPICall makes an API call to Anthropic
func (p *Provider) makeAPICall(ctx context.Context, request *AnthropicRequest) (*AnthropicResponse, error) {
	// Prepare request body
	reqBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	url := fmt.Sprintf("%s/messages", p.baseURL)
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("x-api-key", p.config.APIKey)
	httpReq.Header.Set("anthropic-version", "2023-06-01")

	// Make request
	resp, err := p.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Check status code
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	// Parse response
	var anthropicResp AnthropicResponse
	if err := json.Unmarshal(respBody, &anthropicResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &anthropicResp, nil
}

// convertResponse converts Anthropic response to standard format
func (p *Provider) convertResponse(anthropicResp *AnthropicResponse) *interfaces.Response {
	response := &interfaces.Response{
		Model:     anthropicResp.Model,
		Provider:  p.Name(),
		Timestamp: time.Now(),
		Usage: interfaces.Usage{
			PromptTokens:     anthropicResp.Usage.InputTokens,
			CompletionTokens: anthropicResp.Usage.OutputTokens,
			TotalTokens:      anthropicResp.Usage.InputTokens + anthropicResp.Usage.OutputTokens,
		},
	}

	// Extract content and tool calls
	for _, content := range anthropicResp.Content {
		switch content.Type {
		case "text":
			response.Content += content.Text
		case "tool_use":
			if content.ToolUse != nil {
				toolCall := interfaces.ToolCall{
					ID:        content.ToolUse.ID,
					Type:      "function",
					Name:      content.ToolUse.Name,
					Arguments: content.ToolUse.Input,
				}
				response.ToolCalls = append(response.ToolCalls, toolCall)
			}
		}
	}

	return response
}

// Shutdown gracefully shuts down the provider
func (p *Provider) Shutdown(ctx context.Context) error {
	p.logger.Info("Shutting down Anthropic provider...")
	return nil
}
