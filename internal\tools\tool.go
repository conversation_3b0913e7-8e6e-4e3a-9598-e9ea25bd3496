/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"time"
)

// Tool defines the interface for all built-in tools
type Tool interface {
	// Name returns the tool name
	Name() string
	
	// Description returns the tool description
	Description() string
	
	// Parameters returns the tool parameter schema
	Parameters() map[string]interface{}
	
	// Execute executes the tool with given arguments
	Execute(ctx context.Context, args map[string]interface{}) Result
	
	// Validate validates the tool arguments
	Validate(args map[string]interface{}) error
	
	// SupportsParallel returns true if the tool can be executed in parallel
	SupportsParallel() bool
}

// Result represents the result of a tool execution
type Result struct {
	Output    string                 `json:"output"`
	Error     error                  `json:"error,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	Data      interface{}            `json:"data,omitempty"`
	Duration  time.Duration          `json:"duration"`
	Timestamp time.Time              `json:"timestamp"`
}

// IsSuccess returns true if the tool execution was successful
func (r Result) IsSuccess() bool {
	return r.Error == nil
}

// String returns the string representation of the result
func (r Result) String() string {
	if r.Error != nil {
		return r.Error.Error()
	}
	return r.Output
}

// ToolCategory represents different categories of tools
type ToolCategory string

const (
	CategoryFile   ToolCategory = "file"
	CategorySearch ToolCategory = "search"
	CategoryShell  ToolCategory = "shell"
	CategoryWeb    ToolCategory = "web"
	CategoryMemory ToolCategory = "memory"
	CategoryTask   ToolCategory = "task"
	CategoryUtil   ToolCategory = "util"
)

// ToolInfo provides metadata about a tool
type ToolInfo struct {
	Name        string       `json:"name"`
	Description string       `json:"description"`
	Category    ToolCategory `json:"category"`
	Parameters  map[string]interface{} `json:"parameters"`
	Examples    []string     `json:"examples,omitempty"`
	Parallel    bool         `json:"parallel"`
}

// BaseParameters returns common parameter schemas
func BaseParameters() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{},
		"required": []string{},
	}
}

// StringParameter creates a string parameter schema
func StringParameter(description string, required bool) map[string]interface{} {
	return map[string]interface{}{
		"type":        "string",
		"description": description,
	}
}

// BoolParameter creates a boolean parameter schema
func BoolParameter(description string, defaultValue bool) map[string]interface{} {
	return map[string]interface{}{
		"type":        "boolean",
		"description": description,
		"default":     defaultValue,
	}
}

// IntParameter creates an integer parameter schema
func IntParameter(description string, min, max int) map[string]interface{} {
	param := map[string]interface{}{
		"type":        "integer",
		"description": description,
	}
	if min != 0 {
		param["minimum"] = min
	}
	if max != 0 {
		param["maximum"] = max
	}
	return param
}

// ArrayParameter creates an array parameter schema
func ArrayParameter(description string, itemType string) map[string]interface{} {
	return map[string]interface{}{
		"type":        "array",
		"description": description,
		"items": map[string]interface{}{
			"type": itemType,
		},
	}
}

// Helper functions for argument parsing

// getStringArg safely extracts a string argument with a default value
func getStringArg(args map[string]interface{}, key string, defaultValue string) string {
	if value, ok := args[key].(string); ok {
		return value
	}
	return defaultValue
}

// getIntArg safely extracts an integer argument with a default value
func getIntArg(args map[string]interface{}, key string, defaultValue int) int {
	if value, ok := args[key].(float64); ok {
		return int(value)
	}
	if value, ok := args[key].(int); ok {
		return value
	}
	return defaultValue
}

// getBoolArg safely extracts a boolean argument with a default value
func getBoolArg(args map[string]interface{}, key string, defaultValue bool) bool {
	if value, ok := args[key].(bool); ok {
		return value
	}
	return defaultValue
}

// getFloat64Arg safely extracts a float64 argument with a default value
func getFloat64Arg(args map[string]interface{}, key string, defaultValue float64) float64 {
	if value, ok := args[key].(float64); ok {
		return value
	}
	if value, ok := args[key].(int); ok {
		return float64(value)
	}
	return defaultValue
}

// getFloatArg is an alias for getFloat64Arg for backward compatibility
func getFloatArg(args map[string]interface{}, key string, defaultValue float64) float64 {
	return getFloat64Arg(args, key, defaultValue)
}

// getStringSliceArg safely extracts a string slice argument
func getStringSliceArg(args map[string]interface{}, key string) []string {
	if value, ok := args[key].([]interface{}); ok {
		result := make([]string, 0, len(value))
		for _, v := range value {
			if str, ok := v.(string); ok {
				result = append(result, str)
			}
		}
		return result
	}
	return nil
}
