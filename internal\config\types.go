/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package config

// ProviderConfig represents LLM provider configuration
type ProviderConfig struct {
	APIKey    string `mapstructure:"api_key" json:"api_key"`
	Model     string `mapstructure:"model" json:"model"`
	BaseURL   string `mapstructure:"base_url" json:"base_url,omitempty"`
	RateLimit int    `mapstructure:"rate_limit" json:"rate_limit,omitempty"`
}

// UIConfig represents UI preferences
type UIConfig struct {
	Theme              string `mapstructure:"theme" json:"theme"`
	AutoSave           bool   `mapstructure:"auto_save" json:"auto_save"`
	SyntaxHighlighting bool   `mapstructure:"syntax_highlighting" json:"syntax_highlighting"`
	ShowLineNumbers    bool   `mapstructure:"show_line_numbers" json:"show_line_numbers"`
	WordWrap           bool   `mapstructure:"word_wrap" json:"word_wrap"`
}

// ToolConfig represents tool settings
type ToolConfig struct {
	ShellTimeout     int    `mapstructure:"shell_timeout" json:"shell_timeout"`
	MaxFileSize      string `mapstructure:"max_file_size" json:"max_file_size"`
	ConcurrentLimit  int    `mapstructure:"concurrent_limit" json:"concurrent_limit"`
	DefaultEditor    string `mapstructure:"default_editor" json:"default_editor"`
	BackupFiles      bool   `mapstructure:"backup_files" json:"backup_files"`
}

// ArienConfig represents the complete configuration
type ArienConfig struct {
	Providers map[string]ProviderConfig `mapstructure:"providers" json:"providers"`
	UI        UIConfig                  `mapstructure:"ui" json:"ui"`
	Tools     ToolConfig                `mapstructure:"tools" json:"tools"`
	Version   string                    `mapstructure:"version" json:"version"`
}
