/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package security

import (
	"context"
	"sync"
	"time"

	"golang.org/x/time/rate"
)

// RateLimiter provides rate limiting functionality
type RateLimiter struct {
	limiters map[string]*rate.Limiter
	mu       sync.RWMutex
	
	// Default limits
	defaultRate  rate.Limit
	defaultBurst int
	
	// Cleanup
	cleanupInterval time.Duration
	lastCleanup     time.Time
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter(defaultRate rate.Limit, defaultBurst int) *RateLimiter {
	return &RateLimiter{
		limiters:        make(map[string]*rate.Limiter),
		defaultRate:     defaultRate,
		defaultBurst:    defaultBurst,
		cleanupInterval: 5 * time.Minute,
		lastCleanup:     time.Now(),
	}
}

// Allow checks if an operation is allowed for the given key
func (rl *RateLimiter) Allow(key string) bool {
	limiter := rl.getLimiter(key)
	return limiter.Allow()
}

// AllowN checks if N operations are allowed for the given key
func (rl *RateLimiter) AllowN(key string, n int) bool {
	limiter := rl.getLimiter(key)
	return limiter.AllowN(time.Now(), n)
}

// Wait waits until an operation is allowed for the given key
func (rl *RateLimiter) Wait(ctx context.Context, key string) error {
	limiter := rl.getLimiter(key)
	return limiter.Wait(ctx)
}

// WaitN waits until N operations are allowed for the given key
func (rl *RateLimiter) WaitN(ctx context.Context, key string, n int) error {
	limiter := rl.getLimiter(key)
	return limiter.WaitN(ctx, n)
}

// Reserve reserves tokens for future use
func (rl *RateLimiter) Reserve(key string) *rate.Reservation {
	limiter := rl.getLimiter(key)
	return limiter.Reserve()
}

// ReserveN reserves N tokens for future use
func (rl *RateLimiter) ReserveN(key string, n int) *rate.Reservation {
	limiter := rl.getLimiter(key)
	return limiter.ReserveN(time.Now(), n)
}

// SetLimit sets a custom limit for a specific key
func (rl *RateLimiter) SetLimit(key string, limit rate.Limit, burst int) {
	rl.mu.Lock()
	defer rl.mu.Unlock()
	
	rl.limiters[key] = rate.NewLimiter(limit, burst)
}

// RemoveLimit removes the custom limit for a key (falls back to default)
func (rl *RateLimiter) RemoveLimit(key string) {
	rl.mu.Lock()
	defer rl.mu.Unlock()
	
	delete(rl.limiters, key)
}

// GetStats returns statistics about the rate limiter
func (rl *RateLimiter) GetStats() map[string]interface{} {
	rl.mu.RLock()
	defer rl.mu.RUnlock()
	
	stats := map[string]interface{}{
		"total_limiters":    len(rl.limiters),
		"default_rate":      float64(rl.defaultRate),
		"default_burst":     rl.defaultBurst,
		"cleanup_interval":  rl.cleanupInterval.String(),
		"last_cleanup":      rl.lastCleanup.Format(time.RFC3339),
	}
	
	// Add per-key stats
	keyStats := make(map[string]interface{})
	for key, limiter := range rl.limiters {
		keyStats[key] = map[string]interface{}{
			"limit": float64(limiter.Limit()),
			"burst": limiter.Burst(),
			"tokens": limiter.Tokens(),
		}
	}
	stats["key_stats"] = keyStats
	
	return stats
}

// Cleanup removes unused limiters to prevent memory leaks
func (rl *RateLimiter) Cleanup() {
	rl.mu.Lock()
	defer rl.mu.Unlock()
	
	now := time.Now()
	if now.Sub(rl.lastCleanup) < rl.cleanupInterval {
		return
	}
	
	// Remove limiters that haven't been used recently
	// This is a simple implementation - in production you might want
	// to track last access time for each limiter
	for key, limiter := range rl.limiters {
		// If the limiter has full tokens, it hasn't been used recently
		if limiter.Tokens() >= float64(limiter.Burst()) {
			delete(rl.limiters, key)
		}
	}
	
	rl.lastCleanup = now
}

// getLimiter gets or creates a limiter for the given key
func (rl *RateLimiter) getLimiter(key string) *rate.Limiter {
	rl.mu.RLock()
	limiter, exists := rl.limiters[key]
	rl.mu.RUnlock()
	
	if exists {
		return limiter
	}
	
	rl.mu.Lock()
	defer rl.mu.Unlock()
	
	// Double-check after acquiring write lock
	if limiter, exists := rl.limiters[key]; exists {
		return limiter
	}
	
	// Create new limiter with default settings
	limiter = rate.NewLimiter(rl.defaultRate, rl.defaultBurst)
	rl.limiters[key] = limiter
	
	// Perform cleanup if needed
	go rl.Cleanup()
	
	return limiter
}

// GlobalRateLimiter provides global rate limiting for different operations
type GlobalRateLimiter struct {
	// Different limiters for different operations
	llmRequests    *RateLimiter
	toolExecution  *RateLimiter
	fileOperations *RateLimiter
	webRequests    *RateLimiter
	shellCommands  *RateLimiter
}

// NewGlobalRateLimiter creates a new global rate limiter with sensible defaults
func NewGlobalRateLimiter() *GlobalRateLimiter {
	return &GlobalRateLimiter{
		// LLM requests: 60 per minute (1 per second)
		llmRequests: NewRateLimiter(rate.Every(time.Second), 5),
		
		// Tool execution: 120 per minute (2 per second)
		toolExecution: NewRateLimiter(rate.Every(500*time.Millisecond), 10),
		
		// File operations: 300 per minute (5 per second)
		fileOperations: NewRateLimiter(rate.Every(200*time.Millisecond), 20),
		
		// Web requests: 30 per minute (1 every 2 seconds)
		webRequests: NewRateLimiter(rate.Every(2*time.Second), 3),
		
		// Shell commands: 60 per minute (1 per second)
		shellCommands: NewRateLimiter(rate.Every(time.Second), 5),
	}
}

// AllowLLMRequest checks if an LLM request is allowed
func (grl *GlobalRateLimiter) AllowLLMRequest(provider string) bool {
	return grl.llmRequests.Allow(provider)
}

// WaitLLMRequest waits for an LLM request to be allowed
func (grl *GlobalRateLimiter) WaitLLMRequest(ctx context.Context, provider string) error {
	return grl.llmRequests.Wait(ctx, provider)
}

// AllowToolExecution checks if a tool execution is allowed
func (grl *GlobalRateLimiter) AllowToolExecution(toolName string) bool {
	return grl.toolExecution.Allow(toolName)
}

// WaitToolExecution waits for a tool execution to be allowed
func (grl *GlobalRateLimiter) WaitToolExecution(ctx context.Context, toolName string) error {
	return grl.toolExecution.Wait(ctx, toolName)
}

// AllowFileOperation checks if a file operation is allowed
func (grl *GlobalRateLimiter) AllowFileOperation(operation string) bool {
	return grl.fileOperations.Allow(operation)
}

// WaitFileOperation waits for a file operation to be allowed
func (grl *GlobalRateLimiter) WaitFileOperation(ctx context.Context, operation string) error {
	return grl.fileOperations.Wait(ctx, operation)
}

// AllowWebRequest checks if a web request is allowed
func (grl *GlobalRateLimiter) AllowWebRequest(domain string) bool {
	return grl.webRequests.Allow(domain)
}

// WaitWebRequest waits for a web request to be allowed
func (grl *GlobalRateLimiter) WaitWebRequest(ctx context.Context, domain string) error {
	return grl.webRequests.Wait(ctx, domain)
}

// AllowShellCommand checks if a shell command is allowed
func (grl *GlobalRateLimiter) AllowShellCommand(user string) bool {
	return grl.shellCommands.Allow(user)
}

// WaitShellCommand waits for a shell command to be allowed
func (grl *GlobalRateLimiter) WaitShellCommand(ctx context.Context, user string) error {
	return grl.shellCommands.Wait(ctx, user)
}

// SetLLMLimit sets a custom limit for LLM requests
func (grl *GlobalRateLimiter) SetLLMLimit(provider string, limit rate.Limit, burst int) {
	grl.llmRequests.SetLimit(provider, limit, burst)
}

// SetToolLimit sets a custom limit for tool execution
func (grl *GlobalRateLimiter) SetToolLimit(toolName string, limit rate.Limit, burst int) {
	grl.toolExecution.SetLimit(toolName, limit, burst)
}

// GetStats returns statistics for all rate limiters
func (grl *GlobalRateLimiter) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"llm_requests":    grl.llmRequests.GetStats(),
		"tool_execution":  grl.toolExecution.GetStats(),
		"file_operations": grl.fileOperations.GetStats(),
		"web_requests":    grl.webRequests.GetStats(),
		"shell_commands":  grl.shellCommands.GetStats(),
	}
}

// Cleanup performs cleanup on all rate limiters
func (grl *GlobalRateLimiter) Cleanup() {
	grl.llmRequests.Cleanup()
	grl.toolExecution.Cleanup()
	grl.fileOperations.Cleanup()
	grl.webRequests.Cleanup()
	grl.shellCommands.Cleanup()
}
