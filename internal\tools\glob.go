/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"github.com/bmatcuk/doublestar/v4"
)

// GlobTool implements file pattern matching with support for multiple glob patterns and exclusions
type GlobTool struct {
	maxResults int
}

// NewGlobTool creates a new glob tool
func NewGlobTool() *GlobTool {
	return &GlobTool{
		maxResults: 1000,
	}
}

// Name returns the tool name
func (t *GlobTool) Name() string {
	return "glob"
}

// Description returns the tool description
func (t *GlobTool) Description() string {
	return "File pattern matching with support for multiple glob patterns and exclusions"
}

// Parameters returns the tool parameter schema
func (t *GlobTool) Parameters() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"patterns": map[string]interface{}{
				"type":        "array",
				"description": "Array of glob patterns to match",
				"items": map[string]interface{}{
					"type": "string",
				},
				"minItems": 1,
			},
			"pattern": StringParameter("Single glob pattern (alternative to patterns array)", false),
			"exclude": map[string]interface{}{
				"type":        "array",
				"description": "Array of glob patterns to exclude",
				"items": map[string]interface{}{
					"type": "string",
				},
			},
			"exclude_pattern": StringParameter("Single exclude pattern (alternative to exclude array)", false),
			"base_path": StringParameter("Base directory to search from (default: current directory)", false),
			"include_dirs": BoolParameter("Include directories in results", false),
			"include_hidden": BoolParameter("Include hidden files and directories", false),
			"case_sensitive": BoolParameter("Case sensitive pattern matching", true),
			"follow_symlinks": BoolParameter("Follow symbolic links", false),
			"max_depth": IntParameter("Maximum directory depth to search", 0, 20),
			"max_results": IntParameter("Maximum number of results to return", 1, 1000),
			"sort_results": BoolParameter("Sort results alphabetically", true),
			"include_metadata": BoolParameter("Include file metadata in results", false),
		},
		"anyOf": []map[string]interface{}{
			{"required": []string{"patterns"}},
			{"required": []string{"pattern"}},
		},
	}
}

// GlobMatch represents a single file match
type GlobMatch struct {
	Path         string                 `json:"path"`
	RelativePath string                 `json:"relative_path"`
	IsDir        bool                   `json:"is_dir"`
	Size         int64                  `json:"size"`
	ModTime      time.Time              `json:"mod_time"`
	Mode         string                 `json:"mode,omitempty"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// GlobResult represents the complete glob operation result
type GlobResult struct {
	Patterns      []string    `json:"patterns"`
	ExcludePatterns []string  `json:"exclude_patterns,omitempty"`
	BasePath      string      `json:"base_path"`
	Matches       []GlobMatch `json:"matches"`
	TotalMatches  int         `json:"total_matches"`
	Duration      time.Duration `json:"duration"`
	Truncated     bool        `json:"truncated"`
}

// Execute performs the glob pattern matching operation
func (t *GlobTool) Execute(ctx context.Context, args map[string]interface{}) Result {
	start := time.Now()
	
	// Parse arguments
	var patterns []string
	if patternsArg, ok := args["patterns"].([]interface{}); ok {
		patterns = make([]string, len(patternsArg))
		for i, p := range patternsArg {
			if pattern, ok := p.(string); ok {
				patterns[i] = pattern
			} else {
				return Result{Error: fmt.Errorf("invalid pattern at index %d", i)}
			}
		}
	} else if pattern, ok := args["pattern"].(string); ok {
		patterns = []string{pattern}
	} else {
		return Result{Error: fmt.Errorf("either 'patterns' array or 'pattern' must be provided")}
	}
	
	var excludePatterns []string
	if excludeArg, ok := args["exclude"].([]interface{}); ok {
		excludePatterns = make([]string, len(excludeArg))
		for i, p := range excludeArg {
			if pattern, ok := p.(string); ok {
				excludePatterns[i] = pattern
			} else {
				return Result{Error: fmt.Errorf("invalid exclude pattern at index %d", i)}
			}
		}
	} else if excludePattern, ok := args["exclude_pattern"].(string); ok && excludePattern != "" {
		excludePatterns = []string{excludePattern}
	}
	
	basePath := getStringArg(args, "base_path", ".")
	includeDirs := getBoolArg(args, "include_dirs", false)
	includeHidden := getBoolArg(args, "include_hidden", false)
	caseSensitive := getBoolArg(args, "case_sensitive", true)
	followSymlinks := getBoolArg(args, "follow_symlinks", false)
	maxDepth := getIntArg(args, "max_depth", 0)
	maxResults := getIntArg(args, "max_results", 100)
	sortResults := getBoolArg(args, "sort_results", true)
	includeMetadata := getBoolArg(args, "include_metadata", false)
	
	if maxResults > t.maxResults {
		maxResults = t.maxResults
	}
	
	// Perform glob matching
	result, err := t.performGlobMatch(ctx, patterns, excludePatterns, basePath, 
		includeDirs, includeHidden, caseSensitive, followSymlinks, maxDepth, 
		maxResults, sortResults, includeMetadata)
	if err != nil {
		return Result{Error: err}
	}
	
	result.Duration = time.Since(start)
	
	// Format output
	output := t.formatGlobResult(result)
	
	return Result{
		Output: output,
		Data:   result,
	}
}

// performGlobMatch performs the actual glob pattern matching
func (t *GlobTool) performGlobMatch(ctx context.Context, patterns, excludePatterns []string, 
	basePath string, includeDirs, includeHidden, caseSensitive, followSymlinks bool, 
	maxDepth, maxResults int, sortResults, includeMetadata bool) (*GlobResult, error) {
	
	result := &GlobResult{
		Patterns:        patterns,
		ExcludePatterns: excludePatterns,
		BasePath:        basePath,
		Matches:         make([]GlobMatch, 0),
	}
	
	// Change to base path for relative pattern matching
	originalDir, err := os.Getwd()
	if err != nil {
		return nil, fmt.Errorf("failed to get current directory: %w", err)
	}
	
	if basePath != "." {
		if err := os.Chdir(basePath); err != nil {
			return nil, fmt.Errorf("failed to change to base path %s: %w", basePath, err)
		}
		defer os.Chdir(originalDir)
	}
	
	// Collect all matches from all patterns
	matchedPaths := make(map[string]bool)
	
	for _, pattern := range patterns {
		select {
		case <-ctx.Done():
			return result, ctx.Err()
		default:
		}
		
		// Use doublestar for advanced glob matching
		var matches []string
		if followSymlinks {
			matches, err = doublestar.FilepathGlob(pattern)
		} else {
			matches, err = doublestar.Glob(os.DirFS("."), pattern)
			if err == nil {
				// Convert to full paths
				fullMatches := make([]string, len(matches))
				for i, match := range matches {
					fullMatches[i] = match
				}
				matches = fullMatches
			}
		}
		
		if err != nil {
			return nil, fmt.Errorf("failed to match pattern %s: %w", pattern, err)
		}
		
		for _, match := range matches {
			// Skip if already matched
			if matchedPaths[match] {
				continue
			}
			
			// Check if we should include this match
			if t.shouldIncludeMatch(match, excludePatterns, includeDirs, includeHidden, 
				caseSensitive, maxDepth) {
				matchedPaths[match] = true
				
				// Check max results limit
				if len(result.Matches) >= maxResults {
					result.Truncated = true
					break
				}
			}
		}
		
		if result.Truncated {
			break
		}
	}
	
	// Convert matched paths to GlobMatch objects
	for path := range matchedPaths {
		match, err := t.createGlobMatch(path, basePath, includeMetadata)
		if err != nil {
			// Skip files with errors
			continue
		}
		result.Matches = append(result.Matches, match)
	}
	
	// Sort results if requested
	if sortResults {
		sort.Slice(result.Matches, func(i, j int) bool {
			return result.Matches[i].Path < result.Matches[j].Path
		})
	}
	
	result.TotalMatches = len(result.Matches)
	return result, nil
}

// shouldIncludeMatch determines if a matched path should be included in results
func (t *GlobTool) shouldIncludeMatch(path string, excludePatterns []string, 
	includeDirs, includeHidden, caseSensitive bool, maxDepth int) bool {
	
	// Check exclude patterns
	for _, excludePattern := range excludePatterns {
		matched, err := doublestar.PathMatch(excludePattern, path)
		if err == nil && matched {
			return false
		}
	}
	
	// Check hidden files
	if !includeHidden {
		parts := strings.Split(path, string(filepath.Separator))
		for _, part := range parts {
			if strings.HasPrefix(part, ".") && part != "." && part != ".." {
				return false
			}
		}
	}
	
	// Check directory depth
	if maxDepth > 0 {
		depth := strings.Count(path, string(filepath.Separator))
		if depth > maxDepth {
			return false
		}
	}
	
	// Check if it's a directory and if we should include directories
	info, err := os.Stat(path)
	if err != nil {
		return false
	}
	
	if info.IsDir() && !includeDirs {
		return false
	}
	
	return true
}

// createGlobMatch creates a GlobMatch object from a file path
func (t *GlobTool) createGlobMatch(path, basePath string, includeMetadata bool) (GlobMatch, error) {
	info, err := os.Stat(path)
	if err != nil {
		return GlobMatch{}, err
	}
	
	// Calculate relative path
	relativePath := path
	if basePath != "." {
		if rel, err := filepath.Rel(basePath, path); err == nil {
			relativePath = rel
		}
	}
	
	match := GlobMatch{
		Path:         path,
		RelativePath: relativePath,
		IsDir:        info.IsDir(),
		Size:         info.Size(),
		ModTime:      info.ModTime(),
	}
	
	if includeMetadata {
		match.Mode = info.Mode().String()
		match.Metadata = map[string]interface{}{
			"extension": filepath.Ext(path),
			"directory": filepath.Dir(path),
			"basename":  filepath.Base(path),
			"is_symlink": info.Mode()&os.ModeSymlink != 0,
		}
		
		// Add symlink target if it's a symlink
		if info.Mode()&os.ModeSymlink != 0 {
			if target, err := os.Readlink(path); err == nil {
				match.Metadata["symlink_target"] = target
			}
		}
	}
	
	return match, nil
}

// formatGlobResult formats the glob result for display
func (t *GlobTool) formatGlobResult(result *GlobResult) string {
	var output strings.Builder
	
	output.WriteString(fmt.Sprintf("🔍 Glob Pattern Matching Results\n"))
	output.WriteString(fmt.Sprintf("Patterns: %s\n", strings.Join(result.Patterns, ", ")))
	if len(result.ExcludePatterns) > 0 {
		output.WriteString(fmt.Sprintf("Exclude Patterns: %s\n", strings.Join(result.ExcludePatterns, ", ")))
	}
	output.WriteString(fmt.Sprintf("Base Path: %s\n", result.BasePath))
	output.WriteString(fmt.Sprintf("Total Matches: %d | Duration: %v\n", result.TotalMatches, result.Duration))
	
	if result.Truncated {
		output.WriteString("⚠️  Results truncated to maximum limit\n")
	}
	
	output.WriteString("\n")
	
	if len(result.Matches) == 0 {
		output.WriteString("No matches found.\n")
		return output.String()
	}
	
	// Group matches by type
	var files []GlobMatch
	var dirs []GlobMatch
	
	for _, match := range result.Matches {
		if match.IsDir {
			dirs = append(dirs, match)
		} else {
			files = append(files, match)
		}
	}
	
	// Display directories first
	if len(dirs) > 0 {
		output.WriteString(fmt.Sprintf("📁 Directories (%d):\n", len(dirs)))
		for _, dir := range dirs {
			output.WriteString(fmt.Sprintf("  %s/\n", dir.RelativePath))
		}
		output.WriteString("\n")
	}
	
	// Display files
	if len(files) > 0 {
		output.WriteString(fmt.Sprintf("📄 Files (%d):\n", len(files)))
		for _, file := range files {
			sizeStr := formatBytes(file.Size)
			output.WriteString(fmt.Sprintf("  %s (%s)\n", file.RelativePath, sizeStr))
		}
	}
	
	return output.String()
}

// Validate validates the tool arguments
func (t *GlobTool) Validate(args map[string]interface{}) error {
	// Check that either patterns or pattern is provided
	_, hasPatterns := args["patterns"]
	_, hasPattern := args["pattern"]
	
	if !hasPatterns && !hasPattern {
		return fmt.Errorf("either 'patterns' array or 'pattern' must be provided")
	}
	
	if hasPatterns && hasPattern {
		return fmt.Errorf("cannot specify both 'patterns' and 'pattern'")
	}
	
	// Validate patterns array if provided
	if hasPatterns {
		patterns, ok := args["patterns"].([]interface{})
		if !ok {
			return fmt.Errorf("'patterns' must be an array")
		}
		
		if len(patterns) == 0 {
			return fmt.Errorf("'patterns' array cannot be empty")
		}
		
		for i, p := range patterns {
			if _, ok := p.(string); !ok {
				return fmt.Errorf("pattern at index %d must be a string", i)
			}
		}
	}
	
	// Validate exclude patterns if provided
	if exclude, ok := args["exclude"]; ok {
		if excludePatterns, ok := exclude.([]interface{}); ok {
			for i, p := range excludePatterns {
				if _, ok := p.(string); !ok {
					return fmt.Errorf("exclude pattern at index %d must be a string", i)
				}
			}
		}
	}
	
	// Validate max_depth
	if maxDepth, ok := args["max_depth"]; ok {
		if depth, ok := maxDepth.(int); ok {
			if depth < 0 || depth > 20 {
				return fmt.Errorf("max_depth must be between 0 and 20")
			}
		}
	}
	
	// Validate max_results
	if maxResults, ok := args["max_results"]; ok {
		if results, ok := maxResults.(int); ok {
			if results < 1 || results > 1000 {
				return fmt.Errorf("max_results must be between 1 and 1000")
			}
		}
	}
	
	return nil
}

// SupportsParallel returns whether this tool supports parallel execution
func (t *GlobTool) SupportsParallel() bool {
	return true
}
