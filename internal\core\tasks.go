/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package core

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/charmbracelet/log"
)

// TaskManager manages tasks and project planning
type TaskManager struct {
	config   *Config
	logger   *log.Logger
	taskPath string
	tasks    []Task
	mu       sync.RWMutex
}

// Task represents a task in the system
type Task struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Status      TaskStatus             `json:"status"`
	Priority    TaskPriority           `json:"priority"`
	Tags        []string               `json:"tags,omitempty"`
	ParentID    string                 `json:"parent_id,omitempty"`
	SubTasks    []string               `json:"sub_tasks,omitempty"`
	Created     time.Time              `json:"created"`
	Updated     time.Time              `json:"updated"`
	DueDate     *time.Time             `json:"due_date,omitempty"`
	Completed   *time.Time             `json:"completed,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// TaskStatus represents the status of a task
type TaskStatus string

const (
	TaskStatusNotStarted TaskStatus = "not_started"
	TaskStatusInProgress TaskStatus = "in_progress"
	TaskStatusCompleted  TaskStatus = "completed"
	TaskStatusCancelled  TaskStatus = "cancelled"
	TaskStatusBlocked    TaskStatus = "blocked"
)

// TaskPriority represents the priority of a task
type TaskPriority string

const (
	TaskPriorityLow    TaskPriority = "low"
	TaskPriorityMedium TaskPriority = "medium"
	TaskPriorityHigh   TaskPriority = "high"
	TaskPriorityUrgent TaskPriority = "urgent"
)

// NewTaskManager creates a new task manager
func NewTaskManager(config *Config, logger *log.Logger) (*TaskManager, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return nil, fmt.Errorf("failed to get user home directory: %w", err)
	}

	taskPath := filepath.Join(homeDir, ".arien", "tasks.json")

	manager := &TaskManager{
		config:   config,
		logger:   logger,
		taskPath: taskPath,
		tasks:    make([]Task, 0),
	}

	if err := manager.load(); err != nil {
		logger.Warn("Failed to load tasks, starting fresh", "error", err)
	}

	return manager, nil
}

// CreateTask creates a new task
func (tm *TaskManager) CreateTask(name, description string, priority TaskPriority) (*Task, error) {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	task := Task{
		ID:          tm.generateID(),
		Name:        name,
		Description: description,
		Status:      TaskStatusNotStarted,
		Priority:    priority,
		Created:     time.Now(),
		Updated:     time.Now(),
		Metadata:    make(map[string]interface{}),
	}

	tm.tasks = append(tm.tasks, task)

	if err := tm.persist(); err != nil {
		return nil, fmt.Errorf("failed to persist task: %w", err)
	}

	tm.logger.Info("Task created", "id", task.ID, "name", task.Name)
	return &task, nil
}

// UpdateTask updates an existing task
func (tm *TaskManager) UpdateTask(id string, updates map[string]interface{}) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	for i, task := range tm.tasks {
		if task.ID == id {
			// Update fields
			if name, ok := updates["name"].(string); ok {
				tm.tasks[i].Name = name
			}
			if description, ok := updates["description"].(string); ok {
				tm.tasks[i].Description = description
			}
			if status, ok := updates["status"].(string); ok {
				tm.tasks[i].Status = TaskStatus(status)
				if status == string(TaskStatusCompleted) {
					now := time.Now()
					tm.tasks[i].Completed = &now
				}
			}
			if priority, ok := updates["priority"].(string); ok {
				tm.tasks[i].Priority = TaskPriority(priority)
			}
			if tags, ok := updates["tags"].([]string); ok {
				tm.tasks[i].Tags = tags
			}

			tm.tasks[i].Updated = time.Now()

			if err := tm.persist(); err != nil {
				return fmt.Errorf("failed to persist task update: %w", err)
			}

			tm.logger.Info("Task updated", "id", id)
			return nil
		}
	}

	return fmt.Errorf("task not found: %s", id)
}

// GetTask retrieves a task by ID
func (tm *TaskManager) GetTask(id string) (*Task, error) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	for _, task := range tm.tasks {
		if task.ID == id {
			return &task, nil
		}
	}

	return nil, fmt.Errorf("task not found: %s", id)
}

// ListTasks lists all tasks with optional filtering
func (tm *TaskManager) ListTasks(status TaskStatus, priority TaskPriority) []Task {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	var filtered []Task
	for _, task := range tm.tasks {
		if status != "" && task.Status != status {
			continue
		}
		if priority != "" && task.Priority != priority {
			continue
		}
		filtered = append(filtered, task)
	}

	return filtered
}

// DeleteTask deletes a task
func (tm *TaskManager) DeleteTask(id string) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	for i, task := range tm.tasks {
		if task.ID == id {
			// Remove task
			tm.tasks = append(tm.tasks[:i], tm.tasks[i+1:]...)

			if err := tm.persist(); err != nil {
				return fmt.Errorf("failed to persist task deletion: %w", err)
			}

			tm.logger.Info("Task deleted", "id", id)
			return nil
		}
	}

	return fmt.Errorf("task not found: %s", id)
}

// GetTaskStats returns task statistics
func (tm *TaskManager) GetTaskStats() map[string]int {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	stats := map[string]int{
		"total":       len(tm.tasks),
		"not_started": 0,
		"in_progress": 0,
		"completed":   0,
		"cancelled":   0,
		"blocked":     0,
	}

	for _, task := range tm.tasks {
		switch task.Status {
		case TaskStatusNotStarted:
			stats["not_started"]++
		case TaskStatusInProgress:
			stats["in_progress"]++
		case TaskStatusCompleted:
			stats["completed"]++
		case TaskStatusCancelled:
			stats["cancelled"]++
		case TaskStatusBlocked:
			stats["blocked"]++
		}
	}

	return stats
}

// load loads tasks from file
func (tm *TaskManager) load() error {
	if _, err := os.Stat(tm.taskPath); os.IsNotExist(err) {
		return nil // Task file doesn't exist, start fresh
	}

	data, err := os.ReadFile(tm.taskPath)
	if err != nil {
		return fmt.Errorf("failed to read task file: %w", err)
	}

	if len(data) == 0 {
		return nil // Empty file
	}

	if err := json.Unmarshal(data, &tm.tasks); err != nil {
		return fmt.Errorf("failed to unmarshal tasks: %w", err)
	}

	tm.logger.Debug("Tasks loaded", "count", len(tm.tasks))
	return nil
}

// persist saves tasks to file
func (tm *TaskManager) persist() error {
	// Create directory if it doesn't exist
	dir := filepath.Dir(tm.taskPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create task directory: %w", err)
	}

	data, err := json.MarshalIndent(tm.tasks, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal tasks: %w", err)
	}

	if err := os.WriteFile(tm.taskPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write task file: %w", err)
	}

	return nil
}

// generateID generates a unique ID for a task
func (tm *TaskManager) generateID() string {
	return fmt.Sprintf("task_%d", time.Now().UnixNano())
}

// Shutdown gracefully shuts down the task manager
func (tm *TaskManager) Shutdown(ctx context.Context) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	tm.logger.Info("Shutting down task manager...")

	// Persist any unsaved changes
	if err := tm.persist(); err != nil {
		tm.logger.Error("Failed to persist tasks during shutdown", "error", err)
		return err
	}

	tm.logger.Info("Task manager shutdown complete")
	return nil
}
