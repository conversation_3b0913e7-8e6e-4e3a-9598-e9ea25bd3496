/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package chat

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"sync"
	"time"
)

// SessionManagerImpl implements the SessionManager interface
type SessionManagerImpl struct {
	deps           *Dependencies
	currentSession *ChatSession
	sessionsPath   string
	mu             sync.RWMutex
}

// NewSessionManager creates a new session manager
func NewSessionManager(deps *Dependencies) SessionManager {
	homeDir, _ := os.UserHomeDir()
	sessionsPath := filepath.Join(homeDir, ".arien", "sessions")
	
	// Ensure sessions directory exists
	os.MkdirAll(sessionsPath, 0755)
	
	return &SessionManagerImpl{
		deps:         deps,
		sessionsPath: sessionsPath,
	}
}

// CreateSession creates a new chat session
func (sm *SessionManagerImpl) CreateSession(name string) (*ChatSession, error) {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	sm.deps.Logger.Debug("Creating new session", "name", name)

	// Get default provider and model
	provider := "unknown"
	model := "unknown"
	
	if defaultProvider, err := sm.deps.Engine.LLM().GetDefaultProvider(); err == nil {
		provider = defaultProvider.Name()
		if config, exists := sm.deps.Engine.Config().GetProvider(provider); exists {
			model = config.Model
		}
	}

	session := &ChatSession{
		ID:       generateSessionID(),
		Name:     name,
		Messages: make([]Message, 0),
		Created:  time.Now(),
		Updated:  time.Now(),
		Metadata: make(map[string]interface{}),
		Provider: provider,
		Model:    model,
		Settings: SessionSettings{
			Temperature:     0.7,
			MaxTokens:       4000,
			SystemPrompt:    "",
			AutoSave:        true,
			StreamResponses: sm.deps.Config.StreamingEnabled,
			EnableTools:     true,
			EnableMemory:    true,
			MaxHistorySize:  sm.deps.Config.MaxHistorySize,
		},
	}

	// Save the session
	if err := sm.saveSessionToDisk(session); err != nil {
		return nil, fmt.Errorf("failed to save new session: %w", err)
	}

	sm.deps.Logger.Info("Session created successfully", "id", session.ID, "name", session.Name)
	return session, nil
}

// LoadSession loads a session by ID
func (sm *SessionManagerImpl) LoadSession(id string) (*ChatSession, error) {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	sm.deps.Logger.Debug("Loading session", "id", id)

	sessionFile := filepath.Join(sm.sessionsPath, id+".json")
	
	// Check if file exists
	if _, err := os.Stat(sessionFile); os.IsNotExist(err) {
		return nil, fmt.Errorf("session not found: %s", id)
	}

	// Read session file
	data, err := os.ReadFile(sessionFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read session file: %w", err)
	}

	// Parse session
	var session ChatSession
	if err := json.Unmarshal(data, &session); err != nil {
		return nil, fmt.Errorf("failed to parse session: %w", err)
	}

	// Update access time
	session.Updated = time.Now()

	sm.deps.Logger.Info("Session loaded successfully", "id", session.ID, "name", session.Name, "messages", len(session.Messages))
	return &session, nil
}

// SaveSession saves a session
func (sm *SessionManagerImpl) SaveSession(session *ChatSession) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	if session == nil {
		return fmt.Errorf("session is nil")
	}

	sm.deps.Logger.Debug("Saving session", "id", session.ID, "name", session.Name)

	// Update timestamp
	session.Updated = time.Now()

	// Trim history if it exceeds max size
	if len(session.Messages) > session.Settings.MaxHistorySize {
		// Keep the first message (usually welcome) and the most recent messages
		keepCount := session.Settings.MaxHistorySize - 1
		if keepCount > 0 {
			session.Messages = append(
				session.Messages[:1],
				session.Messages[len(session.Messages)-keepCount:]...,
			)
		}
	}

	// Save to disk
	if err := sm.saveSessionToDisk(session); err != nil {
		return fmt.Errorf("failed to save session to disk: %w", err)
	}

	sm.deps.Logger.Info("Session saved successfully", "id", session.ID, "messages", len(session.Messages))
	return nil
}

// DeleteSession deletes a session
func (sm *SessionManagerImpl) DeleteSession(id string) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	sm.deps.Logger.Debug("Deleting session", "id", id)

	sessionFile := filepath.Join(sm.sessionsPath, id+".json")
	
	if err := os.Remove(sessionFile); err != nil {
		if os.IsNotExist(err) {
			return fmt.Errorf("session not found: %s", id)
		}
		return fmt.Errorf("failed to delete session file: %w", err)
	}

	// Clear current session if it's the one being deleted
	if sm.currentSession != nil && sm.currentSession.ID == id {
		sm.currentSession = nil
	}

	sm.deps.Logger.Info("Session deleted successfully", "id", id)
	return nil
}

// ListSessions returns all available sessions
func (sm *SessionManagerImpl) ListSessions() ([]*ChatSession, error) {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	sm.deps.Logger.Debug("Listing sessions")

	// Read sessions directory
	files, err := os.ReadDir(sm.sessionsPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read sessions directory: %w", err)
	}

	var sessions []*ChatSession

	for _, file := range files {
		if !file.IsDir() && filepath.Ext(file.Name()) == ".json" {
			sessionID := strings.TrimSuffix(file.Name(), ".json")
			
			// Load session metadata only (for performance)
			session, err := sm.loadSessionMetadata(sessionID)
			if err != nil {
				sm.deps.Logger.Warn("Failed to load session metadata", "id", sessionID, "error", err)
				continue
			}
			
			sessions = append(sessions, session)
		}
	}

	// Sort by updated time (most recent first)
	sort.Slice(sessions, func(i, j int) bool {
		return sessions[i].Updated.After(sessions[j].Updated)
	})

	sm.deps.Logger.Info("Sessions listed", "count", len(sessions))
	return sessions, nil
}

// GetCurrentSession returns the current session
func (sm *SessionManagerImpl) GetCurrentSession() *ChatSession {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	return sm.currentSession
}

// SetCurrentSession sets the current session
func (sm *SessionManagerImpl) SetCurrentSession(session *ChatSession) {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	sm.currentSession = session
}

// AutoSave automatically saves the session if auto-save is enabled
func (sm *SessionManagerImpl) AutoSave(session *ChatSession) error {
	if session == nil || !session.Settings.AutoSave {
		return nil
	}

	// Only auto-save if there have been changes since last save
	if time.Since(session.Updated) < time.Minute {
		return nil
	}

	sm.deps.Logger.Debug("Auto-saving session", "id", session.ID)
	return sm.SaveSession(session)
}

// Private helper methods

// saveSessionToDisk saves a session to disk
func (sm *SessionManagerImpl) saveSessionToDisk(session *ChatSession) error {
	sessionFile := filepath.Join(sm.sessionsPath, session.ID+".json")
	
	// Marshal session to JSON
	data, err := json.MarshalIndent(session, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal session: %w", err)
	}

	// Write to temporary file first
	tempFile := sessionFile + ".tmp"
	if err := os.WriteFile(tempFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write temporary session file: %w", err)
	}

	// Atomic rename
	if err := os.Rename(tempFile, sessionFile); err != nil {
		os.Remove(tempFile) // Clean up temp file
		return fmt.Errorf("failed to rename session file: %w", err)
	}

	return nil
}

// loadSessionMetadata loads only session metadata (without messages)
func (sm *SessionManagerImpl) loadSessionMetadata(id string) (*ChatSession, error) {
	sessionFile := filepath.Join(sm.sessionsPath, id+".json")
	
	data, err := os.ReadFile(sessionFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read session file: %w", err)
	}

	// Parse only the metadata fields
	var sessionData map[string]interface{}
	if err := json.Unmarshal(data, &sessionData); err != nil {
		return nil, fmt.Errorf("failed to parse session: %w", err)
	}

	session := &ChatSession{
		ID:       id,
		Name:     getStringField(sessionData, "name", "Unnamed Session"),
		Created:  getTimeField(sessionData, "created"),
		Updated:  getTimeField(sessionData, "updated"),
		Provider: getStringField(sessionData, "provider", "unknown"),
		Model:    getStringField(sessionData, "model", "unknown"),
		Messages: nil, // Don't load messages for metadata
	}

	// Count messages
	if messages, ok := sessionData["messages"].([]interface{}); ok {
		session.Metadata = map[string]interface{}{
			"message_count": len(messages),
		}
	}

	return session, nil
}

// generateSessionID generates a unique session ID
func generateSessionID() string {
	return fmt.Sprintf("session_%d", time.Now().UnixNano())
}

// Utility functions for parsing session data

func getStringField(data map[string]interface{}, field, defaultValue string) string {
	if value, ok := data[field].(string); ok {
		return value
	}
	return defaultValue
}

func getTimeField(data map[string]interface{}, field string) time.Time {
	if value, ok := data[field].(string); ok {
		if t, err := time.Parse(time.RFC3339, value); err == nil {
			return t
		}
	}
	return time.Now()
}

// Session cleanup and maintenance

// CleanupOldSessions removes sessions older than the specified duration
func (sm *SessionManagerImpl) CleanupOldSessions(maxAge time.Duration) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	sm.deps.Logger.Debug("Cleaning up old sessions", "max_age", maxAge)

	sessions, err := sm.ListSessions()
	if err != nil {
		return fmt.Errorf("failed to list sessions: %w", err)
	}

	cutoff := time.Now().Add(-maxAge)
	deletedCount := 0

	for _, session := range sessions {
		if session.Updated.Before(cutoff) {
			if err := sm.DeleteSession(session.ID); err != nil {
				sm.deps.Logger.Warn("Failed to delete old session", "id", session.ID, "error", err)
			} else {
				deletedCount++
			}
		}
	}

	sm.deps.Logger.Info("Old sessions cleaned up", "deleted", deletedCount)
	return nil
}

// ExportSession exports a session to a file
func (sm *SessionManagerImpl) ExportSession(sessionID, filePath string) error {
	session, err := sm.LoadSession(sessionID)
	if err != nil {
		return fmt.Errorf("failed to load session: %w", err)
	}

	data, err := json.MarshalIndent(session, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal session: %w", err)
	}

	if err := os.WriteFile(filePath, data, 0644); err != nil {
		return fmt.Errorf("failed to write export file: %w", err)
	}

	sm.deps.Logger.Info("Session exported", "id", sessionID, "file", filePath)
	return nil
}

// ImportSession imports a session from a file
func (sm *SessionManagerImpl) ImportSession(filePath string) (*ChatSession, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read import file: %w", err)
	}

	var session ChatSession
	if err := json.Unmarshal(data, &session); err != nil {
		return nil, fmt.Errorf("failed to parse session: %w", err)
	}

	// Generate new ID to avoid conflicts
	session.ID = generateSessionID()
	session.Updated = time.Now()

	// Save the imported session
	if err := sm.saveSessionToDisk(&session); err != nil {
		return nil, fmt.Errorf("failed to save imported session: %w", err)
	}

	sm.deps.Logger.Info("Session imported", "id", session.ID, "file", filePath)
	return &session, nil
}
