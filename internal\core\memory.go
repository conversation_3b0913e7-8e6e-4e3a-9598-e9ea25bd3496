/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package core

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/charmbracelet/log"
)

// Memory manages persistent memory for Arien
type Memory struct {
	config     *Config
	logger     *log.Logger
	memoryPath string
	facts      []MemoryFact
	mu         sync.RWMutex
}

// MemoryFact represents a stored fact in memory
type MemoryFact struct {
	ID        string                 `json:"id"`
	Content   string                 `json:"content"`
	Tags      []string               `json:"tags,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	Created   time.Time              `json:"created"`
	Updated   time.Time              `json:"updated"`
	Accessed  time.Time              `json:"accessed"`
	Score     float64                `json:"score,omitempty"`
}

// NewMemory creates a new memory manager
func NewMemory(config *Config, logger *log.Logger) (*Memory, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return nil, fmt.Errorf("failed to get user home directory: %w", err)
	}

	memoryPath := filepath.Join(homeDir, ".arien", ".arien-memory.json")

	memory := &Memory{
		config:     config,
		logger:     logger,
		memoryPath: memoryPath,
		facts:      make([]MemoryFact, 0),
	}

	if err := memory.load(); err != nil {
		logger.Warn("Failed to load memory, starting fresh", "error", err)
	}

	return memory, nil
}

// Save saves a fact to memory
func (m *Memory) Save(content string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	fact := MemoryFact{
		ID:       m.generateID(),
		Content:  strings.TrimSpace(content),
		Created:  time.Now(),
		Updated:  time.Now(),
		Accessed: time.Now(),
		Metadata: make(map[string]interface{}),
	}

	// Extract tags from content (simple implementation)
	fact.Tags = m.extractTags(content)

	m.facts = append(m.facts, fact)

	if err := m.persist(); err != nil {
		return fmt.Errorf("failed to persist memory: %w", err)
	}

	m.logger.Info("Fact saved to memory", "id", fact.ID, "content", m.truncateContent(fact.Content))
	fmt.Printf("Saved to memory: %s\n", m.truncateContent(fact.Content))
	
	return nil
}

// Recall recalls facts from memory based on query
func (m *Memory) Recall(query string) error {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if query == "" {
		// Show all facts
		fmt.Printf("Memory contains %d facts:\n\n", len(m.facts))
		for i, fact := range m.facts {
			fmt.Printf("%d. %s\n", i+1, m.truncateContent(fact.Content))
			if len(fact.Tags) > 0 {
				fmt.Printf("   Tags: %s\n", strings.Join(fact.Tags, ", "))
			}
			fmt.Printf("   Created: %s\n\n", fact.Created.Format("2006-01-02 15:04"))
		}
		return nil
	}

	// Search for matching facts
	matches := m.searchFacts(query)
	
	if len(matches) == 0 {
		fmt.Printf("No facts found matching: %s\n", query)
		return nil
	}

	fmt.Printf("Found %d matching facts:\n\n", len(matches))
	for i, fact := range matches {
		fmt.Printf("%d. %s\n", i+1, fact.Content)
		if len(fact.Tags) > 0 {
			fmt.Printf("   Tags: %s\n", strings.Join(fact.Tags, ", "))
		}
		fmt.Printf("   Created: %s\n", fact.Created.Format("2006-01-02 15:04"))
		if fact.Score > 0 {
			fmt.Printf("   Relevance: %.2f\n", fact.Score)
		}
		fmt.Println()
	}

	return nil
}

// Clear clears all memory
func (m *Memory) Clear() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.facts = make([]MemoryFact, 0)

	if err := m.persist(); err != nil {
		return fmt.Errorf("failed to persist cleared memory: %w", err)
	}

	m.logger.Info("Memory cleared")
	fmt.Println("Memory cleared successfully")
	
	return nil
}

// GetFacts returns all facts (for internal use)
func (m *Memory) GetFacts() []MemoryFact {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	facts := make([]MemoryFact, len(m.facts))
	copy(facts, m.facts)
	return facts
}

// SearchFacts searches for facts matching the query
func (m *Memory) SearchFacts(query string) []MemoryFact {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	return m.searchFacts(query)
}

// load loads memory from file
func (m *Memory) load() error {
	if _, err := os.Stat(m.memoryPath); os.IsNotExist(err) {
		return nil // Memory file doesn't exist, start fresh
	}

	data, err := os.ReadFile(m.memoryPath)
	if err != nil {
		return fmt.Errorf("failed to read memory file: %w", err)
	}

	if len(data) == 0 {
		return nil // Empty file
	}

	if err := json.Unmarshal(data, &m.facts); err != nil {
		return fmt.Errorf("failed to unmarshal memory: %w", err)
	}

	m.logger.Debug("Memory loaded", "facts", len(m.facts))
	return nil
}

// persist saves memory to file
func (m *Memory) persist() error {
	// Create directory if it doesn't exist
	dir := filepath.Dir(m.memoryPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create memory directory: %w", err)
	}

	data, err := json.MarshalIndent(m.facts, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal memory: %w", err)
	}

	if err := os.WriteFile(m.memoryPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write memory file: %w", err)
	}

	return nil
}

// generateID generates a unique ID for a fact
func (m *Memory) generateID() string {
	return fmt.Sprintf("fact_%d", time.Now().UnixNano())
}

// extractTags extracts tags from content (simple implementation)
func (m *Memory) extractTags(content string) []string {
	var tags []string
	
	// Look for hashtags
	words := strings.Fields(content)
	for _, word := range words {
		if strings.HasPrefix(word, "#") && len(word) > 1 {
			tag := strings.TrimPrefix(word, "#")
			tag = strings.ToLower(strings.Trim(tag, ".,!?;:"))
			if tag != "" {
				tags = append(tags, tag)
			}
		}
	}

	// Add some automatic tags based on content
	lowerContent := strings.ToLower(content)
	if strings.Contains(lowerContent, "password") || strings.Contains(lowerContent, "api key") {
		tags = append(tags, "security")
	}
	if strings.Contains(lowerContent, "preference") || strings.Contains(lowerContent, "setting") {
		tags = append(tags, "preference")
	}
	if strings.Contains(lowerContent, "project") || strings.Contains(lowerContent, "work") {
		tags = append(tags, "project")
	}

	return tags
}

// searchFacts searches for facts matching the query
func (m *Memory) searchFacts(query string) []MemoryFact {
	var matches []MemoryFact
	queryLower := strings.ToLower(query)

	for _, fact := range m.facts {
		score := 0.0
		
		// Check content match
		if strings.Contains(strings.ToLower(fact.Content), queryLower) {
			score += 1.0
		}

		// Check tag match
		for _, tag := range fact.Tags {
			if strings.Contains(strings.ToLower(tag), queryLower) {
				score += 0.5
			}
		}

		// Check exact tag match
		for _, tag := range fact.Tags {
			if strings.ToLower(tag) == queryLower {
				score += 1.0
			}
		}

		if score > 0 {
			factCopy := fact
			factCopy.Score = score
			factCopy.Accessed = time.Now()
			matches = append(matches, factCopy)
		}
	}

	// Sort by score (descending)
	for i := 0; i < len(matches)-1; i++ {
		for j := i + 1; j < len(matches); j++ {
			if matches[i].Score < matches[j].Score {
				matches[i], matches[j] = matches[j], matches[i]
			}
		}
	}

	return matches
}

// truncateContent truncates content for display
func (m *Memory) truncateContent(content string) string {
	if len(content) <= 100 {
		return content
	}
	return content[:97] + "..."
}

// Shutdown gracefully shuts down the memory system
func (m *Memory) Shutdown(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.logger.Info("Shutting down memory system...")
	
	// Persist any unsaved changes
	if err := m.persist(); err != nil {
		m.logger.Error("Failed to persist memory during shutdown", "error", err)
		return err
	}

	m.logger.Info("Memory system shutdown complete")
	return nil
}
