/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package components

import (
	"fmt"
	"strings"
	"unicode"

	"arien/internal/ui/themes"

	"github.com/charmbracelet/bubbles/textinput"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// InputType defines different types of input components
type InputType string

const (
	InputText     InputType = "text"
	InputPassword InputType = "password"
	InputEmail    InputType = "email"
	InputNumber   InputType = "number"
	InputSearch   InputType = "search"
	InputMultiline InputType = "multiline"
)

// InputComponent represents an enhanced input component
type InputComponent struct {
	*BaseComponent
	textInput     textinput.Model
	inputType     InputType
	label         string
	placeholder   string
	value         string
	validation    ValidationFunc
	suggestions   []string
	showSuggestions bool
	selectedSuggestion int
	maxLength     int
	required      bool
	errorMessage  string
	helpText      string
	multilineText []string
	currentLine   int
	showCharCount bool
}

// ValidationFunc defines a validation function
type ValidationFunc func(value string) error

// NewInputComponent creates a new input component
func NewInputComponent(id string, inputType InputType) *InputComponent {
	ti := textinput.New()
	ti.Focus()
	
	ic := &InputComponent{
		BaseComponent:      NewBaseComponent(id, TypeInput),
		textInput:          ti,
		inputType:          inputType,
		suggestions:        make([]string, 0),
		showSuggestions:    false,
		selectedSuggestion: 0,
		maxLength:          0,
		required:           false,
		multilineText:      make([]string, 0),
		currentLine:        0,
		showCharCount:      false,
	}
	
	ic.configureInput()
	return ic
}

// Init initializes the input component
func (ic *InputComponent) Init() tea.Cmd {
	return textinput.Blink
}

// Update handles input component updates
func (ic *InputComponent) Update(msg tea.Msg) (Component, tea.Cmd) {
	var cmd tea.Cmd
	
	switch msg := msg.(type) {
	case tea.KeyMsg:
		// Handle special keys first
		switch msg.String() {
		case "ctrl+c":
			return ic, tea.Quit
		case "esc":
			if ic.showSuggestions {
				ic.showSuggestions = false
				return ic, nil
			}
		case "tab":
			if ic.showSuggestions && len(ic.suggestions) > 0 {
				ic.applySuggestion()
				return ic, nil
			}
		case "up":
			if ic.inputType == InputMultiline {
				return ic, ic.handleMultilineUp()
			} else if ic.showSuggestions {
				ic.moveSuggestionUp()
				return ic, nil
			}
		case "down":
			if ic.inputType == InputMultiline {
				return ic, ic.handleMultilineDown()
			} else if ic.showSuggestions {
				ic.moveSuggestionDown()
				return ic, nil
			}
		case "enter":
			if ic.showSuggestions && len(ic.suggestions) > 0 {
				ic.applySuggestion()
				return ic, nil
			} else if ic.inputType == InputMultiline {
				return ic, ic.handleMultilineEnter()
			} else {
				return ic, ic.handleSubmit()
			}
		}
		
		// Update the underlying text input
		if ic.inputType != InputMultiline {
			ic.textInput, cmd = ic.textInput.Update(msg)
			ic.value = ic.textInput.Value()
		} else {
			cmd = ic.handleMultilineInput(msg)
		}
		
		// Validate input
		ic.validateInput()
		
		// Update suggestions
		ic.updateSuggestions()
		
	case tea.WindowSizeMsg:
		ic.SetSize(msg.Width, msg.Height)
		ic.textInput.Width = ic.width - 4 // Account for padding and borders
	}
	
	return ic, cmd
}

// View renders the input component
func (ic *InputComponent) View() string {
	var content strings.Builder
	
	// Label
	if ic.label != "" {
		labelStyle := lipgloss.NewStyle().
			Foreground(ic.theme.Colors.Text).
			Bold(true)
		
		label := ic.label
		if ic.required {
			label += " *"
		}
		
		content.WriteString(labelStyle.Render(label))
		content.WriteString("\n")
	}
	
	// Input field
	if ic.inputType == InputMultiline {
		content.WriteString(ic.renderMultilineInput())
	} else {
		content.WriteString(ic.renderSingleLineInput())
	}
	
	// Character count
	if ic.showCharCount {
		content.WriteString("\n")
		content.WriteString(ic.renderCharCount())
	}
	
	// Error message
	if ic.errorMessage != "" {
		content.WriteString("\n")
		errorStyle := lipgloss.NewStyle().
			Foreground(ic.theme.Colors.Error)
		content.WriteString(errorStyle.Render("Error: " + ic.errorMessage))
	}
	
	// Help text
	if ic.helpText != "" && ic.errorMessage == "" {
		content.WriteString("\n")
		helpStyle := lipgloss.NewStyle().
			Foreground(ic.theme.Colors.TextMuted).
			Italic(true)
		content.WriteString(helpStyle.Render(ic.helpText))
	}
	
	// Suggestions
	if ic.showSuggestions && len(ic.suggestions) > 0 {
		content.WriteString("\n")
		content.WriteString(ic.renderSuggestions())
	}
	
	return content.String()
}

// SetLabel sets the input label
func (ic *InputComponent) SetLabel(label string) {
	ic.label = label
}

// SetPlaceholder sets the input placeholder
func (ic *InputComponent) SetPlaceholder(placeholder string) {
	ic.placeholder = placeholder
	ic.textInput.Placeholder = placeholder
}

// SetValue sets the input value
func (ic *InputComponent) SetValue(value string) {
	ic.value = value
	ic.textInput.SetValue(value)
	if ic.inputType == InputMultiline {
		ic.multilineText = strings.Split(value, "\n")
	}
}

// GetValue returns the current input value
func (ic *InputComponent) GetValue() string {
	if ic.inputType == InputMultiline {
		return strings.Join(ic.multilineText, "\n")
	}
	return ic.value
}

// SetValidation sets the validation function
func (ic *InputComponent) SetValidation(validation ValidationFunc) {
	ic.validation = validation
}

// SetSuggestions sets the suggestions list
func (ic *InputComponent) SetSuggestions(suggestions []string) {
	ic.suggestions = suggestions
}

// SetMaxLength sets the maximum input length
func (ic *InputComponent) SetMaxLength(maxLength int) {
	ic.maxLength = maxLength
	ic.textInput.CharLimit = maxLength
}

// SetRequired sets whether the input is required
func (ic *InputComponent) SetRequired(required bool) {
	ic.required = required
}

// SetHelpText sets the help text
func (ic *InputComponent) SetHelpText(helpText string) {
	ic.helpText = helpText
}

// SetShowCharCount sets whether to show character count
func (ic *InputComponent) SetShowCharCount(show bool) {
	ic.showCharCount = show
}

// IsValid returns whether the current input is valid
func (ic *InputComponent) IsValid() bool {
	return ic.errorMessage == ""
}

// Focus sets focus to the input
func (ic *InputComponent) Focus() tea.Cmd {
	ic.BaseComponent.Focus()
	ic.textInput.Focus()
	return textinput.Blink
}

// Blur removes focus from the input
func (ic *InputComponent) Blur() tea.Cmd {
	ic.BaseComponent.Blur()
	ic.textInput.Blur()
	ic.showSuggestions = false
	return nil
}

// Private methods

// configureInput configures the input based on type
func (ic *InputComponent) configureInput() {
	switch ic.inputType {
	case InputPassword:
		ic.textInput.EchoMode = textinput.EchoPassword
		ic.textInput.EchoCharacter = '•'
	case InputEmail:
		ic.textInput.Placeholder = "<EMAIL>"
	case InputNumber:
		ic.textInput.Placeholder = "0"
	case InputSearch:
		ic.textInput.Placeholder = "Search..."
	case InputMultiline:
		ic.multilineText = []string{""}
		ic.currentLine = 0
	}
}

// validateInput validates the current input value
func (ic *InputComponent) validateInput() {
	ic.errorMessage = ""
	
	value := ic.GetValue()
	
	// Required validation
	if ic.required && strings.TrimSpace(value) == "" {
		ic.errorMessage = "This field is required"
		return
	}
	
	// Type-specific validation
	switch ic.inputType {
	case InputEmail:
		if value != "" && !isValidEmail(value) {
			ic.errorMessage = "Please enter a valid email address"
			return
		}
	case InputNumber:
		if value != "" && !isValidNumber(value) {
			ic.errorMessage = "Please enter a valid number"
			return
		}
	}
	
	// Custom validation
	if ic.validation != nil {
		if err := ic.validation(value); err != nil {
			ic.errorMessage = err.Error()
			return
		}
	}
	
	// Length validation
	if ic.maxLength > 0 && len(value) > ic.maxLength {
		ic.errorMessage = fmt.Sprintf("Maximum length is %d characters", ic.maxLength)
		return
	}
}

// updateSuggestions updates the suggestions based on current input
func (ic *InputComponent) updateSuggestions() {
	if len(ic.suggestions) == 0 || ic.inputType == InputPassword {
		ic.showSuggestions = false
		return
	}
	
	value := strings.ToLower(ic.GetValue())
	if value == "" {
		ic.showSuggestions = false
		return
	}
	
	// Filter suggestions
	var filteredSuggestions []string
	for _, suggestion := range ic.suggestions {
		if strings.HasPrefix(strings.ToLower(suggestion), value) {
			filteredSuggestions = append(filteredSuggestions, suggestion)
		}
	}
	
	ic.suggestions = filteredSuggestions
	ic.showSuggestions = len(filteredSuggestions) > 0
	ic.selectedSuggestion = 0
}

// applySuggestion applies the selected suggestion
func (ic *InputComponent) applySuggestion() {
	if ic.selectedSuggestion >= 0 && ic.selectedSuggestion < len(ic.suggestions) {
		suggestion := ic.suggestions[ic.selectedSuggestion]
		ic.SetValue(suggestion)
		ic.showSuggestions = false
	}
}

// moveSuggestionUp moves suggestion selection up
func (ic *InputComponent) moveSuggestionUp() {
	if ic.selectedSuggestion > 0 {
		ic.selectedSuggestion--
	}
}

// moveSuggestionDown moves suggestion selection down
func (ic *InputComponent) moveSuggestionDown() {
	if ic.selectedSuggestion < len(ic.suggestions)-1 {
		ic.selectedSuggestion++
	}
}

// renderSingleLineInput renders a single-line input
func (ic *InputComponent) renderSingleLineInput() string {
	var style lipgloss.Style
	
	if ic.focused {
		style = lipgloss.NewStyle().
			Border(lipgloss.NormalBorder()).
			BorderForeground(ic.theme.Colors.BorderFocus).
			Padding(0, 1).
			Width(ic.width - 4)
	} else {
		style = lipgloss.NewStyle().
			Border(lipgloss.NormalBorder()).
			BorderForeground(ic.theme.Colors.Border).
			Padding(0, 1).
			Width(ic.width - 4)
	}
	
	if ic.errorMessage != "" {
		style = style.BorderForeground(ic.theme.Colors.Error)
	}
	
	return style.Render(ic.textInput.View())
}

// renderMultilineInput renders a multiline input
func (ic *InputComponent) renderMultilineInput() string {
	var content strings.Builder
	
	for i, line := range ic.multilineText {
		var lineStyle lipgloss.Style
		
		if i == ic.currentLine && ic.focused {
			lineStyle = lipgloss.NewStyle().
				Background(ic.theme.Colors.Selection).
				Foreground(ic.theme.Colors.Text).
				Width(ic.width - 4)
		} else {
			lineStyle = lipgloss.NewStyle().
				Foreground(ic.theme.Colors.Text).
				Width(ic.width - 4)
		}
		
		displayLine := line
		if i == ic.currentLine && ic.focused {
			displayLine += "│" // Cursor
		}
		
		content.WriteString(lineStyle.Render(displayLine))
		if i < len(ic.multilineText)-1 {
			content.WriteString("\n")
		}
	}
	
	var style lipgloss.Style
	if ic.focused {
		style = lipgloss.NewStyle().
			Border(lipgloss.NormalBorder()).
			BorderForeground(ic.theme.Colors.BorderFocus).
			Padding(1).
			Width(ic.width - 2).
			Height(ic.height - 4)
	} else {
		style = lipgloss.NewStyle().
			Border(lipgloss.NormalBorder()).
			BorderForeground(ic.theme.Colors.Border).
			Padding(1).
			Width(ic.width - 2).
			Height(ic.height - 4)
	}
	
	if ic.errorMessage != "" {
		style = style.BorderForeground(ic.theme.Colors.Error)
	}
	
	return style.Render(content.String())
}

// renderCharCount renders the character count
func (ic *InputComponent) renderCharCount() string {
	value := ic.GetValue()
	count := len(value)
	
	var countText string
	if ic.maxLength > 0 {
		countText = fmt.Sprintf("%d/%d", count, ic.maxLength)
	} else {
		countText = fmt.Sprintf("%d", count)
	}
	
	var style lipgloss.Style
	if ic.maxLength > 0 && count > ic.maxLength {
		style = lipgloss.NewStyle().Foreground(ic.theme.Colors.Error)
	} else {
		style = lipgloss.NewStyle().Foreground(ic.theme.Colors.TextMuted)
	}
	
	return style.Render(countText)
}

// renderSuggestions renders the suggestions dropdown
func (ic *InputComponent) renderSuggestions() string {
	var content strings.Builder
	
	maxSuggestions := 5
	if len(ic.suggestions) < maxSuggestions {
		maxSuggestions = len(ic.suggestions)
	}
	
	for i := 0; i < maxSuggestions; i++ {
		suggestion := ic.suggestions[i]
		
		var style lipgloss.Style
		if i == ic.selectedSuggestion {
			style = lipgloss.NewStyle().
				Background(ic.theme.Colors.Primary).
				Foreground(ic.theme.Colors.Background).
				Padding(0, 1)
		} else {
			style = lipgloss.NewStyle().
				Background(ic.theme.Colors.Surface).
				Foreground(ic.theme.Colors.Text).
				Padding(0, 1)
		}
		
		content.WriteString(style.Render(suggestion))
		if i < maxSuggestions-1 {
			content.WriteString("\n")
		}
	}
	
	containerStyle := lipgloss.NewStyle().
		Border(lipgloss.NormalBorder()).
		BorderForeground(ic.theme.Colors.Border).
		Width(ic.width - 4)
	
	return containerStyle.Render(content.String())
}

// handleMultilineInput handles multiline input
func (ic *InputComponent) handleMultilineInput(msg tea.KeyMsg) tea.Cmd {
	switch msg.String() {
	case "backspace":
		if ic.currentLine < len(ic.multilineText) {
			line := ic.multilineText[ic.currentLine]
			if len(line) > 0 {
				ic.multilineText[ic.currentLine] = line[:len(line)-1]
			} else if ic.currentLine > 0 {
				// Remove current line and move to previous
				ic.multilineText = append(ic.multilineText[:ic.currentLine], ic.multilineText[ic.currentLine+1:]...)
				ic.currentLine--
			}
		}
	default:
		if len(msg.String()) == 1 {
			if ic.currentLine < len(ic.multilineText) {
				ic.multilineText[ic.currentLine] += msg.String()
			}
		}
	}
	
	return nil
}

// handleMultilineUp handles up arrow in multiline input
func (ic *InputComponent) handleMultilineUp() tea.Cmd {
	if ic.currentLine > 0 {
		ic.currentLine--
	}
	return nil
}

// handleMultilineDown handles down arrow in multiline input
func (ic *InputComponent) handleMultilineDown() tea.Cmd {
	if ic.currentLine < len(ic.multilineText)-1 {
		ic.currentLine++
	}
	return nil
}

// handleMultilineEnter handles enter in multiline input
func (ic *InputComponent) handleMultilineEnter() tea.Cmd {
	// Insert new line
	newLines := make([]string, len(ic.multilineText)+1)
	copy(newLines[:ic.currentLine+1], ic.multilineText[:ic.currentLine+1])
	newLines[ic.currentLine+1] = ""
	copy(newLines[ic.currentLine+2:], ic.multilineText[ic.currentLine+1:])
	
	ic.multilineText = newLines
	ic.currentLine++
	
	return nil
}

// handleSubmit handles input submission
func (ic *InputComponent) handleSubmit() tea.Cmd {
	if ic.IsValid() {
		return func() tea.Msg {
			return InputSubmitMsg{
				ComponentID: ic.ID(),
				Value:       ic.GetValue(),
			}
		}
	}
	return nil
}

// Helper functions

// isValidEmail validates email format
func isValidEmail(email string) bool {
	// Simple email validation
	return strings.Contains(email, "@") && strings.Contains(email, ".")
}

// isValidNumber validates number format
func isValidNumber(number string) bool {
	for _, char := range number {
		if !unicode.IsDigit(char) && char != '.' && char != '-' {
			return false
		}
	}
	return true
}

// InputSubmitMsg represents an input submission message
type InputSubmitMsg struct {
	ComponentID string
	Value       string
}
