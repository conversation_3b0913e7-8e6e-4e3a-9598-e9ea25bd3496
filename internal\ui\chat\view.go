/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package chat

import (
	"fmt"
	"strings"
	"time"

	"arien/internal/utils"
	"github.com/charmbracelet/lipgloss"
)

// View implements tea.Model
func (m *ChatModel) View() string {
	if !m.initialized {
		return "Initializing chat..."
	}

	var sections []string

	// Header
	sections = append(sections, m.renderHeader())

	// Messages viewport
	sections = append(sections, m.renderMessages())

	// Input area
	sections = append(sections, m.renderInput())

	// Status bar
	sections = append(sections, m.renderStatusBar())

	// Error display
	if m.state.LastError != nil {
		sections = append(sections, m.renderError())
	}

	return strings.Join(sections, "\n")
}

// renderHeader renders the chat header
func (m *ChatModel) renderHeader() string {
	var title string
	if m.state.CurrentSession != nil {
		title = fmt.Sprintf("🤖 Arien - %s", m.state.CurrentSession.Name)
	} else {
		title = "🤖 Arien - AI Assistant"
	}

	header := GetStyles().Header.Render(title)

	// Add session info in debug mode
	if m.state.ViewMode == ViewModeDebug {
		var info []string
		if m.state.CurrentSession != nil {
			info = append(info, fmt.Sprintf("Session: %s", m.state.CurrentSession.ID))
			info = append(info, fmt.Sprintf("Messages: %d", len(m.state.CurrentSession.Messages)))
			info = append(info, fmt.Sprintf("Provider: %s", m.state.CurrentSession.Provider))
			info = append(info, fmt.Sprintf("Model: %s", m.state.CurrentSession.Model))
		}

		if len(info) > 0 {
			debugInfo := GetStyles().Debug.Render(strings.Join(info, " | "))
			header += "\n" + debugInfo
		}
	}

	return header
}

// renderMessages renders the message viewport
func (m *ChatModel) renderMessages() string {
	if m.state.CurrentSession == nil || len(m.state.CurrentSession.Messages) == 0 {
		return GetStyles().EmptyState.Render("No messages yet. Start a conversation!")
	}

	var messageViews []string
	messages := m.state.CurrentSession.Messages

	// Calculate visible range based on scroll offset
	startIdx := m.viewport.ScrollOffset
	endIdx := startIdx + m.viewport.Height
	if endIdx > len(messages) {
		endIdx = len(messages)
	}
	if startIdx >= len(messages) {
		startIdx = len(messages) - 1
	}
	if startIdx < 0 {
		startIdx = 0
	}

	// Render visible messages
	for i := startIdx; i < endIdx; i++ {
		msg := messages[i]
		messageView := m.renderMessage(&msg, i)
		messageViews = append(messageViews, messageView)
	}

	// Add scroll indicators
	content := strings.Join(messageViews, "\n\n")

	if m.viewport.ScrollOffset > 0 {
		content = GetStyles().ScrollIndicator.Render("▲ More messages above") + "\n" + content
	}

	if endIdx < len(messages) {
		content = content + "\n" + GetStyles().ScrollIndicator.Render("▼ More messages below")
	}

	return content
}

// renderMessage renders a single message
func (m *ChatModel) renderMessage(msg *Message, index int) string {
	var content string
	
	// Message header with role and timestamp
	timestamp := msg.Timestamp.Format("15:04")
	var roleStyle lipgloss.Style
	var roleName string

	switch msg.Role {
	case "user":
		roleStyle = GetStyles().UserMessage
		roleName = "You"
	case "assistant":
		roleStyle = GetStyles().AssistantMessage
		roleName = "Arien"
	case "system":
		roleStyle = GetStyles().SystemMessage
		roleName = "System"
	default:
		roleStyle = GetStyles().DefaultMessage
		roleName = strings.Title(msg.Role)
	}

	// Highlight selected message
	if index == m.state.SelectedMessage {
		roleStyle = GetStyles().SelectedMessage
	}

	// Message header
	header := fmt.Sprintf("%s (%s)", roleName, timestamp)
	if m.state.ViewMode == ViewModeDebug {
		header += fmt.Sprintf(" [%s]", msg.ID)
	}

	content = roleStyle.Render(header) + "\n"

	// Message content
	messageContent := msg.Content
	if msg.Status == MessageStatusStreaming {
		messageContent += GetStyles().StreamingIndicator.Render(" ▋")
	}

	// Apply word wrapping
	wrappedContent := m.wrapText(messageContent, m.viewport.Width-4)
	content += GetStyles().MessageContent.Render(wrappedContent)

	// Show status if not delivered
	if msg.Status != MessageStatusDelivered && msg.Status != MessageStatusStreaming {
		statusText := m.getStatusText(msg.Status)
		content += "\n" + GetStyles().MessageStatus.Render(statusText)
	}

	// Show error if present
	if msg.Error != "" {
		content += "\n" + GetStyles().Error.Render("Error: " + msg.Error)
	}

	// Show tool results in debug mode
	if m.state.ViewMode == ViewModeDebug && len(msg.ToolResults) > 0 {
		content += "\n" + GetStyles().Debug.Render(fmt.Sprintf("Tools: %d results", len(msg.ToolResults)))
	}

	// Show metadata if enabled
	if m.state.ShowMetadata && len(msg.Metadata) > 0 {
		metadataStr := m.formatMetadata(msg.Metadata)
		content += "\n" + GetStyles().Metadata.Render("Metadata: " + metadataStr)
	}

	// Show attachments if present
	if len(msg.Attachments) > 0 {
		attachmentStr := m.formatAttachments(msg.Attachments)
		content += "\n" + GetStyles().Attachment.Render("📎 " + attachmentStr)
	}

	return content
}

// renderInput renders the input area
func (m *ChatModel) renderInput() string {
	var content []string

	// Input label
	label := "Message:"
	if m.state.IsLoading {
		label = "Processing..."
	} else if m.state.IsStreaming {
		label = "Streaming..."
	}

	content = append(content, GetStyles().InputLabel.Render(label))

	// Input field
	inputValue := m.inputField.Value
	if !m.state.IsLoading && !m.state.IsStreaming {
		// Add cursor
		if m.inputField.CursorPos >= len(inputValue) {
			inputValue += "█"
		} else {
			inputValue = inputValue[:m.inputField.CursorPos] + "█" + inputValue[m.inputField.CursorPos:]
		}
	}

	var inputStyle lipgloss.Style
	if m.state.IsLoading || m.state.IsStreaming {
		inputStyle = GetStyles().DisabledInput
	} else {
		inputStyle = GetStyles().Input
	}

	content = append(content, inputStyle.Render(inputValue))

	// Input help
	var helpText string
	if m.state.IsLoading {
		helpText = "Please wait..."
	} else if m.state.IsStreaming {
		helpText = "Receiving response..."
	} else {
		helpText = "enter: send • ctrl+c: quit • ctrl+n: new session"
		if m.deps.Config.AttachmentsEnabled {
			helpText += " • ctrl+a: attach file"
		}
	}

	content = append(content, GetStyles().Help.Render(helpText))

	return strings.Join(content, "\n")
}

// renderStatusBar renders the status bar
func (m *ChatModel) renderStatusBar() string {
	var statusItems []string

	// Connection status
	if m.statusBar.IsConnected {
		statusItems = append(statusItems, "🟢 Connected")
	} else {
		statusItems = append(statusItems, "🔴 Disconnected")
	}

	// Provider and model
	if m.statusBar.Provider != "" && m.statusBar.Model != "" {
		statusItems = append(statusItems, fmt.Sprintf("%s/%s", m.statusBar.Provider, m.statusBar.Model))
	}

	// Session info
	if m.state.CurrentSession != nil {
		statusItems = append(statusItems, fmt.Sprintf("Session: %s", m.state.CurrentSession.Name))
		statusItems = append(statusItems, fmt.Sprintf("Messages: %d", len(m.state.CurrentSession.Messages)))
	}

	// Token count (if available)
	if m.statusBar.TokenCount > 0 {
		statusItems = append(statusItems, fmt.Sprintf("Tokens: %d", m.statusBar.TokenCount))
	}

	// Last saved
	if !m.statusBar.LastSaved.IsZero() {
		timeSince := time.Since(m.statusBar.LastSaved)
		if timeSince < time.Minute {
			statusItems = append(statusItems, "Saved: just now")
		} else {
			statusItems = append(statusItems, fmt.Sprintf("Saved: %s ago", m.formatDuration(timeSince)))
		}
	}

	// Streaming indicator
	if m.statusBar.IsStreaming {
		statusItems = append(statusItems, "📡 Streaming")
	}

	status := strings.Join(statusItems, " | ")
	return GetStyles().StatusBar.Render(status)
}

// renderError renders error messages
func (m *ChatModel) renderError() string {
	if m.state.LastError == nil {
		return ""
	}

	errorText := fmt.Sprintf("❌ Error: %v", m.state.LastError)
	return GetStyles().Error.Render(errorText)
}

// Utility functions

// wrapText wraps text to fit within the specified width
func (m *ChatModel) wrapText(text string, width int) string {
	if width <= 0 {
		return text
	}

	words := strings.Fields(text)
	if len(words) == 0 {
		return text
	}

	var lines []string
	var currentLine []string
	currentLength := 0

	for _, word := range words {
		wordLength := len(word)
		
		// If adding this word would exceed the width, start a new line
		if currentLength > 0 && currentLength+1+wordLength > width {
			lines = append(lines, strings.Join(currentLine, " "))
			currentLine = []string{word}
			currentLength = wordLength
		} else {
			currentLine = append(currentLine, word)
			if currentLength > 0 {
				currentLength += 1 // Space
			}
			currentLength += wordLength
		}
	}

	// Add the last line
	if len(currentLine) > 0 {
		lines = append(lines, strings.Join(currentLine, " "))
	}

	return strings.Join(lines, "\n")
}

// getStatusText returns a human-readable status text
func (m *ChatModel) getStatusText(status MessageStatus) string {
	switch status {
	case MessageStatusPending:
		return "Pending"
	case MessageStatusSending:
		return "Sending..."
	case MessageStatusSent:
		return "Sent"
	case MessageStatusDelivered:
		return "Delivered"
	case MessageStatusFailed:
		return "Failed"
	case MessageStatusStreaming:
		return "Streaming..."
	default:
		return "Unknown"
	}
}

// formatMetadata formats metadata for display
func (m *ChatModel) formatMetadata(metadata map[string]interface{}) string {
	var items []string
	for key, value := range metadata {
		items = append(items, fmt.Sprintf("%s: %v", key, value))
	}
	return strings.Join(items, ", ")
}

// formatAttachments formats attachments for display
func (m *ChatModel) formatAttachments(attachments []Attachment) string {
	var names []string
	for _, att := range attachments {
		size := utils.TruncateString(m.formatFileSize(att.Size), 10)
		names = append(names, fmt.Sprintf("%s (%s)", att.Name, size))
	}
	return strings.Join(names, ", ")
}

// formatFileSize formats file size for display
func (m *ChatModel) formatFileSize(size int64) string {
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}
	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(size)/float64(div), "KMGTPE"[exp])
}

// formatDuration formats duration for display
func (m *ChatModel) formatDuration(d time.Duration) string {
	if d < time.Minute {
		return fmt.Sprintf("%ds", int(d.Seconds()))
	} else if d < time.Hour {
		return fmt.Sprintf("%dm", int(d.Minutes()))
	} else {
		return fmt.Sprintf("%dh", int(d.Hours()))
	}
}
