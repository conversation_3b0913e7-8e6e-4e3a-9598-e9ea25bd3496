/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package core

import (
	"fmt"
	"runtime"
	"time"
)

// ErrorType represents different types of errors
type ErrorType string

const (
	ErrorTypeValidation    ErrorType = "validation"
	ErrorTypeAuthentication ErrorType = "authentication"
	ErrorTypeAuthorization ErrorType = "authorization"
	ErrorTypeRateLimit     ErrorType = "rate_limit"
	ErrorTypeNetwork       ErrorType = "network"
	ErrorTypeFileSystem    ErrorType = "filesystem"
	ErrorTypeConfiguration ErrorType = "configuration"
	ErrorTypeLLM           ErrorType = "llm"
	ErrorTypeTool          ErrorType = "tool"
	ErrorTypeInternal      ErrorType = "internal"
	ErrorTypeTimeout       ErrorType = "timeout"
	ErrorTypeResource      ErrorType = "resource"
)

// ArienError represents a structured error with additional context
type ArienError struct {
	Type        ErrorType              `json:"type"`
	Code        string                 `json:"code"`
	Message     string                 `json:"message"`
	Details     string                 `json:"details,omitempty"`
	Cause       error                  `json:"cause,omitempty"`
	Context     map[string]interface{} `json:"context,omitempty"`
	Timestamp   time.Time              `json:"timestamp"`
	StackTrace  string                 `json:"stack_trace,omitempty"`
	Recoverable bool                   `json:"recoverable"`
}

// Error implements the error interface
func (e *ArienError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("[%s:%s] %s: %s", e.Type, e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("[%s:%s] %s", e.Type, e.Code, e.Message)
}

// Unwrap returns the underlying cause
func (e *ArienError) Unwrap() error {
	return e.Cause
}

// Is checks if the error matches the target
func (e *ArienError) Is(target error) bool {
	if t, ok := target.(*ArienError); ok {
		return e.Type == t.Type && e.Code == t.Code
	}
	return false
}

// NewError creates a new ArienError
func NewError(errorType ErrorType, code, message string) *ArienError {
	return &ArienError{
		Type:        errorType,
		Code:        code,
		Message:     message,
		Timestamp:   time.Now(),
		Recoverable: true,
		Context:     make(map[string]interface{}),
	}
}

// NewErrorWithCause creates a new ArienError with an underlying cause
func NewErrorWithCause(errorType ErrorType, code, message string, cause error) *ArienError {
	err := NewError(errorType, code, message)
	err.Cause = cause
	return err
}

// WithDetails adds details to the error
func (e *ArienError) WithDetails(details string) *ArienError {
	e.Details = details
	return e
}

// WithContext adds context to the error
func (e *ArienError) WithContext(key string, value interface{}) *ArienError {
	if e.Context == nil {
		e.Context = make(map[string]interface{})
	}
	e.Context[key] = value
	return e
}

// WithStackTrace adds a stack trace to the error
func (e *ArienError) WithStackTrace() *ArienError {
	buf := make([]byte, 4096)
	n := runtime.Stack(buf, false)
	e.StackTrace = string(buf[:n])
	return e
}

// AsUnrecoverable marks the error as unrecoverable
func (e *ArienError) AsUnrecoverable() *ArienError {
	e.Recoverable = false
	return e
}

// Common error constructors

// NewValidationError creates a validation error
func NewValidationError(field, message string) *ArienError {
	return NewError(ErrorTypeValidation, "VALIDATION_FAILED", message).
		WithContext("field", field)
}

// NewAuthenticationError creates an authentication error
func NewAuthenticationError(message string) *ArienError {
	return NewError(ErrorTypeAuthentication, "AUTH_FAILED", message).
		AsUnrecoverable()
}

// NewAuthorizationError creates an authorization error
func NewAuthorizationError(resource, action string) *ArienError {
	return NewError(ErrorTypeAuthorization, "ACCESS_DENIED", "Access denied").
		WithContext("resource", resource).
		WithContext("action", action).
		AsUnrecoverable()
}

// NewRateLimitError creates a rate limit error
func NewRateLimitError(operation string, retryAfter time.Duration) *ArienError {
	return NewError(ErrorTypeRateLimit, "RATE_LIMITED", "Rate limit exceeded").
		WithContext("operation", operation).
		WithContext("retry_after", retryAfter.String())
}

// NewNetworkError creates a network error
func NewNetworkError(operation string, cause error) *ArienError {
	return NewErrorWithCause(ErrorTypeNetwork, "NETWORK_ERROR", "Network operation failed", cause).
		WithContext("operation", operation)
}

// NewFileSystemError creates a filesystem error
func NewFileSystemError(operation, path string, cause error) *ArienError {
	return NewErrorWithCause(ErrorTypeFileSystem, "FS_ERROR", "File system operation failed", cause).
		WithContext("operation", operation).
		WithContext("path", path)
}

// NewConfigurationError creates a configuration error
func NewConfigurationError(setting, message string) *ArienError {
	return NewError(ErrorTypeConfiguration, "CONFIG_ERROR", message).
		WithContext("setting", setting).
		AsUnrecoverable()
}

// NewLLMError creates an LLM-related error
func NewLLMError(provider, operation string, cause error) *ArienError {
	return NewErrorWithCause(ErrorTypeLLM, "LLM_ERROR", "LLM operation failed", cause).
		WithContext("provider", provider).
		WithContext("operation", operation)
}

// NewToolError creates a tool execution error
func NewToolError(toolName, operation string, cause error) *ArienError {
	return NewErrorWithCause(ErrorTypeTool, "TOOL_ERROR", "Tool execution failed", cause).
		WithContext("tool", toolName).
		WithContext("operation", operation)
}

// NewInternalError creates an internal error
func NewInternalError(component, message string) *ArienError {
	return NewError(ErrorTypeInternal, "INTERNAL_ERROR", message).
		WithContext("component", component).
		WithStackTrace().
		AsUnrecoverable()
}

// NewTimeoutError creates a timeout error
func NewTimeoutError(operation string, timeout time.Duration) *ArienError {
	return NewError(ErrorTypeTimeout, "TIMEOUT", "Operation timed out").
		WithContext("operation", operation).
		WithContext("timeout", timeout.String())
}

// NewResourceError creates a resource-related error
func NewResourceError(resource, message string) *ArienError {
	return NewError(ErrorTypeResource, "RESOURCE_ERROR", message).
		WithContext("resource", resource)
}

// ErrorHandler provides centralized error handling
type ErrorHandler struct {
	logger interface {
		Error(msg string, args ...interface{})
		Warn(msg string, args ...interface{})
		Debug(msg string, args ...interface{})
	}
}

// NewErrorHandler creates a new error handler
func NewErrorHandler(logger interface {
	Error(msg string, args ...interface{})
	Warn(msg string, args ...interface{})
	Debug(msg string, args ...interface{})
}) *ErrorHandler {
	return &ErrorHandler{logger: logger}
}

// Handle handles an error appropriately based on its type and severity
func (eh *ErrorHandler) Handle(err error) {
	if err == nil {
		return
	}

	if arienErr, ok := err.(*ArienError); ok {
		eh.handleArienError(arienErr)
	} else {
		eh.handleGenericError(err)
	}
}

// handleArienError handles ArienError instances
func (eh *ErrorHandler) handleArienError(err *ArienError) {
	logArgs := []interface{}{
		"type", err.Type,
		"code", err.Code,
		"message", err.Message,
		"recoverable", err.Recoverable,
		"timestamp", err.Timestamp,
	}

	if err.Details != "" {
		logArgs = append(logArgs, "details", err.Details)
	}

	if err.Context != nil && len(err.Context) > 0 {
		logArgs = append(logArgs, "context", err.Context)
	}

	if err.Cause != nil {
		logArgs = append(logArgs, "cause", err.Cause.Error())
	}

	switch err.Type {
	case ErrorTypeInternal:
		eh.logger.Error("Internal error occurred", logArgs...)
		if err.StackTrace != "" {
			eh.logger.Debug("Stack trace", "trace", err.StackTrace)
		}
	case ErrorTypeAuthentication, ErrorTypeAuthorization:
		eh.logger.Warn("Security error", logArgs...)
	case ErrorTypeValidation:
		eh.logger.Debug("Validation error", logArgs...)
	case ErrorTypeRateLimit:
		eh.logger.Warn("Rate limit exceeded", logArgs...)
	case ErrorTypeTimeout:
		eh.logger.Warn("Operation timed out", logArgs...)
	default:
		if err.Recoverable {
			eh.logger.Warn("Recoverable error", logArgs...)
		} else {
			eh.logger.Error("Unrecoverable error", logArgs...)
		}
	}
}

// handleGenericError handles generic error instances
func (eh *ErrorHandler) handleGenericError(err error) {
	eh.logger.Error("Unhandled error", "error", err.Error())
}

// IsRecoverable checks if an error is recoverable
func IsRecoverable(err error) bool {
	if arienErr, ok := err.(*ArienError); ok {
		return arienErr.Recoverable
	}
	return true // Assume generic errors are recoverable
}

// GetErrorType returns the error type if it's an ArienError
func GetErrorType(err error) ErrorType {
	if arienErr, ok := err.(*ArienError); ok {
		return arienErr.Type
	}
	return ErrorTypeInternal
}

// GetErrorCode returns the error code if it's an ArienError
func GetErrorCode(err error) string {
	if arienErr, ok := err.(*ArienError); ok {
		return arienErr.Code
	}
	return "UNKNOWN"
}
