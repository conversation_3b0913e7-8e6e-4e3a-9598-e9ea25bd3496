/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package chat

import (
	"context"
	"fmt"
	"strings"
	"time"

	"arien/internal/interfaces"
)

// MessageProcessorImpl implements the MessageProcessor interface
type MessageProcessorImpl struct {
	deps *Dependencies
}

// NewMessageProcessor creates a new message processor
func NewMessageProcessor(deps *Dependencies) MessageProcessor {
	return &MessageProcessorImpl{
		deps: deps,
	}
}

// ProcessMessage processes a user message and returns an AI response
func (mp *MessageProcessorImpl) ProcessMessage(ctx context.Context, message string, session *ChatSession) (*Message, error) {
	mp.deps.Logger.Debug("Processing message", "message", message, "session", session.ID)

	// Validate input
	if err := mp.ValidateInput(message); err != nil {
		return nil, fmt.Errorf("input validation failed: %w", err)
	}

	// Sanitize input
	sanitizedMessage := mp.SanitizeInput(message)

	// Create user message
	userMsg := &Message{
		ID:        generateMessageID(),
		Role:      "user",
		Content:   sanitizedMessage,
		Timestamp: time.Now(),
		Status:    MessageStatusSent,
		Metadata:  make(map[string]interface{}),
	}

	// Add user message to session
	session.Messages = append(session.Messages, *userMsg)

	// Check if this is a command
	if strings.HasPrefix(sanitizedMessage, "/") {
		return mp.ProcessCommand(ctx, sanitizedMessage, session)
	}

	// Process with AI engine
	response, err := mp.deps.Engine.ProcessMessage(ctx, sanitizedMessage)
	if err != nil {
		// Create error message
		errorMsg := &Message{
			ID:        generateMessageID(),
			Role:      "assistant",
			Content:   "I apologize, but I encountered an error processing your message. Please try again.",
			Timestamp: time.Now(),
			Status:    MessageStatusFailed,
			Error:     err.Error(),
			Metadata:  make(map[string]interface{}),
		}
		return errorMsg, nil
	}

	// Create assistant message
	assistantMsg := &Message{
		ID:        generateMessageID(),
		Role:      "assistant",
		Content:   response.Content,
		Timestamp: time.Now(),
		Status:    MessageStatusDelivered,
		Metadata:  make(map[string]interface{}),
	}

	// Add tool results if present
	if len(response.ToolResults) > 0 {
		// Convert tools.Result to interfaces.ToolCall for compatibility
		for i, toolResult := range response.ToolResults {
			toolCall := interfaces.ToolCall{
				ID:        fmt.Sprintf("tool_%d", i),
				Type:      "function",
				Name:      "tool_execution", // Generic name since tools.Result doesn't have a name field
				Arguments: map[string]interface{}{
					"output": toolResult.Output,
					"error":  toolResult.Error,
				},
			}
			assistantMsg.ToolResults = append(assistantMsg.ToolResults, toolCall)
		}
		assistantMsg.Metadata["tool_count"] = len(response.ToolResults)
	}

	// Add response metadata
	if response.Metadata != nil {
		for key, value := range response.Metadata {
			assistantMsg.Metadata[key] = value
		}
	}

	// Note: core.Response doesn't have Usage field, so we skip token usage for now
	// This would need to be added to the core.Response type if needed

	mp.deps.Logger.Info("Message processed successfully", 
		"user_message_id", userMsg.ID, 
		"assistant_message_id", assistantMsg.ID,
		"content_length", len(assistantMsg.Content))

	return assistantMsg, nil
}

// ProcessCommand processes a command message
func (mp *MessageProcessorImpl) ProcessCommand(ctx context.Context, command string, session *ChatSession) (*Message, error) {
	mp.deps.Logger.Debug("Processing command", "command", command, "session", session.ID)

	// Remove the leading slash
	cmd := strings.TrimPrefix(command, "/")
	parts := strings.Fields(cmd)
	
	if len(parts) == 0 {
		return mp.createErrorMessage("Invalid command format"), nil
	}

	commandName := parts[0]
	args := parts[1:]

	var content string
	var metadata = make(map[string]interface{})

	switch commandName {
	case "help":
		content = mp.getHelpText()
		
	case "clear":
		// Clear session messages (except welcome message)
		if len(session.Messages) > 1 {
			session.Messages = session.Messages[:1] // Keep only the first message (welcome)
		}
		content = "Chat history cleared."
		
	case "save":
		sessionName := "Saved Chat"
		if len(args) > 0 {
			sessionName = strings.Join(args, " ")
		}
		session.Name = sessionName
		content = fmt.Sprintf("Session saved as '%s'", sessionName)
		
	case "export":
		exportData, err := mp.exportSession(session)
		if err != nil {
			return mp.createErrorMessage(fmt.Sprintf("Export failed: %v", err)), nil
		}
		content = fmt.Sprintf("Session exported. Data length: %d characters", len(exportData))
		metadata["export_data"] = exportData
		
	case "stats":
		content = mp.getSessionStats(session)
		
	case "model":
		if len(args) > 0 {
			modelName := args[0]
			content = fmt.Sprintf("Model switching to '%s' is not yet implemented", modelName)
		} else {
			// Show current model
			if provider, err := mp.deps.Engine.LLM().GetDefaultProvider(); err == nil {
				if config, exists := mp.deps.Engine.Config().GetProvider(provider.Name()); exists {
					content = fmt.Sprintf("Current model: %s (%s)", config.Model, provider.Name())
				} else {
					content = "Current model: Unknown"
				}
			} else {
				content = "No provider configured"
			}
		}
		
	case "debug":
		content = mp.getDebugInfo(session)
		
	case "memory":
		if len(args) > 0 {
			subCmd := args[0]
			switch subCmd {
			case "save":
				if len(args) > 1 {
					memoryContent := strings.Join(args[1:], " ")
					if err := mp.deps.Engine.Memory().Save(memoryContent); err != nil {
						return mp.createErrorMessage(fmt.Sprintf("Failed to save memory: %v", err)), nil
					}
					content = "Memory saved successfully."
				} else {
					content = "Usage: /memory save <content>"
				}
			case "search":
				if len(args) > 1 {
					query := strings.Join(args[1:], " ")
					// Use the memory recall functionality instead of Search method
					// Since Memory doesn't have a Search method, we'll simulate it
					content = fmt.Sprintf("Memory search for '%s' is not yet implemented. Use the memory tool instead.", query)
				} else {
					content = "Usage: /memory search <query>"
				}
			default:
				content = "Memory commands: save, search"
			}
		} else {
			content = "Memory commands: save, search"
		}
		
	default:
		content = fmt.Sprintf("Unknown command: %s. Type /help for available commands.", commandName)
	}

	// Create command response message
	responseMsg := &Message{
		ID:        generateMessageID(),
		Role:      "system",
		Content:   content,
		Timestamp: time.Now(),
		Status:    MessageStatusDelivered,
		Metadata:  metadata,
	}

	mp.deps.Logger.Info("Command processed", "command", commandName, "response_length", len(content))
	return responseMsg, nil
}

// StreamMessage processes a message with streaming response
func (mp *MessageProcessorImpl) StreamMessage(ctx context.Context, message string, session *ChatSession, callback func(chunk string)) error {
	// This would implement streaming functionality
	// For now, we'll simulate it by calling ProcessMessage and invoking callback with chunks
	
	response, err := mp.ProcessMessage(ctx, message, session)
	if err != nil {
		return err
	}

	// Simulate streaming by sending chunks
	content := response.Content
	chunkSize := 10
	
	for i := 0; i < len(content); i += chunkSize {
		end := i + chunkSize
		if end > len(content) {
			end = len(content)
		}
		
		chunk := content[i:end]
		callback(chunk)
		
		// Small delay to simulate streaming
		time.Sleep(time.Millisecond * 50)
	}

	return nil
}

// ValidateInput validates user input
func (mp *MessageProcessorImpl) ValidateInput(input string) error {
	return mp.deps.Validator.ValidateInput(input)
}

// SanitizeInput sanitizes user input
func (mp *MessageProcessorImpl) SanitizeInput(input string) string {
	return mp.deps.Validator.SanitizeInput(input)
}

// Helper methods

// createErrorMessage creates an error message
func (mp *MessageProcessorImpl) createErrorMessage(errorText string) *Message {
	return &Message{
		ID:        generateMessageID(),
		Role:      "system",
		Content:   errorText,
		Timestamp: time.Now(),
		Status:    MessageStatusFailed,
		Metadata:  make(map[string]interface{}),
	}
}

// getHelpText returns help text for commands
func (mp *MessageProcessorImpl) getHelpText() string {
	return `Available commands:
/help - Show this help message
/clear - Clear chat history
/save [name] - Save current session with optional name
/export - Export session data
/stats - Show session statistics
/model [name] - Show current model or switch to specified model
/debug - Show debug information
/memory save <content> - Save content to memory
/memory search <query> - Search memory for content

Keyboard shortcuts:
Ctrl+C - Quit
Ctrl+N - New session
Ctrl+S - Save session
Ctrl+A - Add attachment (if enabled)
Ctrl+M - Toggle metadata display
Ctrl+D - Toggle debug mode
Up/Down - Scroll messages
Ctrl+Up/Down - Navigate message history`
}

// exportSession exports session data
func (mp *MessageProcessorImpl) exportSession(session *ChatSession) (string, error) {
	var export strings.Builder
	
	export.WriteString(fmt.Sprintf("# Chat Session: %s\n", session.Name))
	export.WriteString(fmt.Sprintf("Created: %s\n", session.Created.Format(time.RFC3339)))
	export.WriteString(fmt.Sprintf("Updated: %s\n", session.Updated.Format(time.RFC3339)))
	export.WriteString(fmt.Sprintf("Provider: %s\n", session.Provider))
	export.WriteString(fmt.Sprintf("Model: %s\n\n", session.Model))
	
	for _, msg := range session.Messages {
		export.WriteString(fmt.Sprintf("## %s (%s)\n", strings.Title(msg.Role), msg.Timestamp.Format("15:04:05")))
		export.WriteString(fmt.Sprintf("%s\n\n", msg.Content))
		
		if len(msg.ToolResults) > 0 {
			export.WriteString("### Tool Results:\n")
			for _, tool := range msg.ToolResults {
				export.WriteString(fmt.Sprintf("- %s\n", tool.Name))
			}
			export.WriteString("\n")
		}
	}
	
	return export.String(), nil
}

// getSessionStats returns session statistics
func (mp *MessageProcessorImpl) getSessionStats(session *ChatSession) string {
	userMsgCount := 0
	assistantMsgCount := 0
	totalChars := 0
	
	for _, msg := range session.Messages {
		switch msg.Role {
		case "user":
			userMsgCount++
		case "assistant":
			assistantMsgCount++
		}
		totalChars += len(msg.Content)
	}
	
	return fmt.Sprintf(`Session Statistics:
- Total messages: %d
- User messages: %d
- Assistant messages: %d
- Total characters: %d
- Session duration: %s
- Provider: %s
- Model: %s`,
		len(session.Messages),
		userMsgCount,
		assistantMsgCount,
		totalChars,
		time.Since(session.Created).Round(time.Second),
		session.Provider,
		session.Model)
}

// getDebugInfo returns debug information
func (mp *MessageProcessorImpl) getDebugInfo(session *ChatSession) string {
	var info strings.Builder
	
	info.WriteString(fmt.Sprintf("Session ID: %s\n", session.ID))
	info.WriteString(fmt.Sprintf("Session Name: %s\n", session.Name))
	info.WriteString(fmt.Sprintf("Created: %s\n", session.Created.Format(time.RFC3339)))
	info.WriteString(fmt.Sprintf("Updated: %s\n", session.Updated.Format(time.RFC3339)))
	info.WriteString(fmt.Sprintf("Provider: %s\n", session.Provider))
	info.WriteString(fmt.Sprintf("Model: %s\n", session.Model))
	info.WriteString(fmt.Sprintf("Message count: %d\n", len(session.Messages)))
	
	// Engine info
	if provider, err := mp.deps.Engine.LLM().GetDefaultProvider(); err == nil {
		info.WriteString(fmt.Sprintf("Active provider: %s\n", provider.Name()))
		info.WriteString(fmt.Sprintf("Supports streaming: %t\n", provider.SupportsStreaming()))
		info.WriteString(fmt.Sprintf("Supports tools: %t\n", provider.SupportsToolCalling()))
	}
	
	// Memory info - simplified since Memory doesn't have GetStats method
	info.WriteString("Memory system: Active\n")
	
	return info.String()
}
