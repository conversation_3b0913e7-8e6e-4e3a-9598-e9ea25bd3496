/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// MemoryEntry represents a single memory entry
type MemoryEntry struct {
	ID        string                 `json:"id"`
	Content   string                 `json:"content"`
	Tags      []string               `json:"tags"`
	Timestamp time.Time              `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// MemoryStore represents the memory storage structure
type MemoryStore struct {
	Entries []MemoryEntry `json:"entries"`
	Version string        `json:"version"`
}

// MemoryToolImpl implements persistent memory management
type MemoryToolImpl struct {
	memoryFile string
}

// NewMemoryTool creates a new memory tool
func NewMemoryTool() *MemoryToolImpl {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		homeDir = "."
	}

	memoryFile := filepath.Join(homeDir, ".arien-memory.json")

	return &MemoryToolImpl{
		memoryFile: memoryFile,
	}
}

// Name returns the tool name
func (t *MemoryToolImpl) Name() string {
	return "memory"
}

// Description returns the tool description
func (t *MemoryToolImpl) Description() string {
	return "Save and retrieve information from persistent memory"
}

// Parameters returns the tool parameter schema
func (t *MemoryToolImpl) Parameters() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"action": map[string]interface{}{
				"type": "string",
				"description": "Action to perform: save, recall, clear",
				"enum": []string{"save", "recall", "clear"},
			},
			"content": StringParameter("Content to save (for save action)", false),
			"query": StringParameter("Query to search for (for recall action)", false),
		},
		"required": []string{"action"},
	}
}

// Execute executes the memory tool
func (t *MemoryToolImpl) Execute(ctx context.Context, args map[string]interface{}) Result {
	action, ok := args["action"].(string)
	if !ok {
		return Result{Error: fmt.Errorf("action is required")}
	}

	switch action {
	case "save":
		content, ok := args["content"].(string)
		if !ok || content == "" {
			return Result{Error: fmt.Errorf("content is required for save action")}
		}

		tags := []string{}
		if t, ok := args["tags"].([]interface{}); ok {
			for _, tag := range t {
				if tagStr, ok := tag.(string); ok {
					tags = append(tags, tagStr)
				}
			}
		}

		return t.saveMemory(content, tags)

	case "recall":
		query := ""
		if q, ok := args["query"].(string); ok {
			query = q
		}
		return t.recallMemory(query)

	case "clear":
		return t.clearMemory()

	default:
		return Result{Error: fmt.Errorf("unknown action: %s", action)}
	}
}

// Validate validates the tool arguments
func (t *MemoryToolImpl) Validate(args map[string]interface{}) error {
	action, ok := args["action"].(string)
	if !ok {
		return fmt.Errorf("action is required")
	}

	validActions := []string{"save", "recall", "clear"}
	valid := false
	for _, validAction := range validActions {
		if action == validAction {
			valid = true
			break
		}
	}
	if !valid {
		return fmt.Errorf("invalid action: %s", action)
	}

	if action == "save" {
		if content, ok := args["content"].(string); !ok || content == "" {
			return fmt.Errorf("content is required for save action")
		}
	}

	return nil
}

// loadMemoryStore loads the memory store from file
func (t *MemoryToolImpl) loadMemoryStore() (*MemoryStore, error) {
	store := &MemoryStore{
		Entries: []MemoryEntry{},
		Version: "1.0",
	}

	if _, err := os.Stat(t.memoryFile); os.IsNotExist(err) {
		return store, nil
	}

	data, err := os.ReadFile(t.memoryFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read memory file: %w", err)
	}

	if err := json.Unmarshal(data, store); err != nil {
		return nil, fmt.Errorf("failed to parse memory file: %w", err)
	}

	return store, nil
}

// saveMemoryStore saves the memory store to file
func (t *MemoryToolImpl) saveMemoryStore(store *MemoryStore) error {
	// Ensure directory exists
	dir := filepath.Dir(t.memoryFile)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create memory directory: %w", err)
	}

	data, err := json.MarshalIndent(store, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal memory store: %w", err)
	}

	if err := os.WriteFile(t.memoryFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write memory file: %w", err)
	}

	return nil
}

// saveMemory saves a memory entry
func (t *MemoryToolImpl) saveMemory(content string, tags []string) Result {
	store, err := t.loadMemoryStore()
	if err != nil {
		return Result{Error: fmt.Errorf("failed to load memory store: %w", err)}
	}

	entry := MemoryEntry{
		ID:        fmt.Sprintf("mem_%d", time.Now().UnixNano()),
		Content:   content,
		Tags:      tags,
		Timestamp: time.Now(),
		Metadata:  make(map[string]interface{}),
	}

	store.Entries = append(store.Entries, entry)

	if err := t.saveMemoryStore(store); err != nil {
		return Result{Error: fmt.Errorf("failed to save memory: %w", err)}
	}

	return Result{
		Output: fmt.Sprintf("Memory saved successfully:\nID: %s\nContent: %s\nTags: %v\nTimestamp: %s",
			entry.ID, entry.Content, entry.Tags, entry.Timestamp.Format(time.RFC3339)),
		Metadata: map[string]interface{}{
			"action":    "save",
			"id":        entry.ID,
			"content":   entry.Content,
			"tags":      entry.Tags,
			"timestamp": entry.Timestamp,
		},
	}
}

// recallMemory searches and recalls memory entries
func (t *MemoryToolImpl) recallMemory(query string) Result {
	store, err := t.loadMemoryStore()
	if err != nil {
		return Result{Error: fmt.Errorf("failed to load memory store: %w", err)}
	}

	if len(store.Entries) == 0 {
		return Result{
			Output: "No memories found.",
			Metadata: map[string]interface{}{
				"action": "recall",
				"query":  query,
				"count":  0,
			},
		}
	}

	var matches []MemoryEntry
	queryLower := strings.ToLower(query)

	// Search through entries
	for _, entry := range store.Entries {
		if query == "" ||
		   strings.Contains(strings.ToLower(entry.Content), queryLower) ||
		   t.containsTag(entry.Tags, queryLower) {
			matches = append(matches, entry)
		}
	}

	if len(matches) == 0 {
		return Result{
			Output: fmt.Sprintf("No memories found matching query: %s", query),
			Metadata: map[string]interface{}{
				"action": "recall",
				"query":  query,
				"count":  0,
			},
		}
	}

	// Format output
	output := fmt.Sprintf("Found %d memory entries", len(matches))
	if query != "" {
		output += fmt.Sprintf(" matching '%s'", query)
	}
	output += ":\n\n"

	for i, entry := range matches {
		output += fmt.Sprintf("%d. [%s] %s\n", i+1, entry.ID, entry.Content)
		if len(entry.Tags) > 0 {
			output += fmt.Sprintf("   Tags: %v\n", entry.Tags)
		}
		output += fmt.Sprintf("   Saved: %s\n\n", entry.Timestamp.Format("2006-01-02 15:04:05"))
	}

	return Result{
		Output: output,
		Metadata: map[string]interface{}{
			"action":  "recall",
			"query":   query,
			"count":   len(matches),
			"entries": matches,
		},
	}
}

// clearMemory clears all memory entries
func (t *MemoryToolImpl) clearMemory() Result {
	store := &MemoryStore{
		Entries: []MemoryEntry{},
		Version: "1.0",
	}

	if err := t.saveMemoryStore(store); err != nil {
		return Result{Error: fmt.Errorf("failed to clear memory: %w", err)}
	}

	return Result{
		Output: "All memories have been cleared successfully.",
		Metadata: map[string]interface{}{
			"action": "clear",
		},
	}
}

// containsTag checks if tags contain the query
func (t *MemoryToolImpl) containsTag(tags []string, query string) bool {
	for _, tag := range tags {
		if strings.Contains(strings.ToLower(tag), query) {
			return true
		}
	}
	return false
}

// SupportsParallel returns true if the tool can be executed in parallel
func (t *MemoryToolImpl) SupportsParallel() bool {
	return true
}
