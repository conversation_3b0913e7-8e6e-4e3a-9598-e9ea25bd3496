/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package chat

import (
	"fmt"
	"io"
	"mime"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

// AttachmentManagerImpl implements the AttachmentManager interface
type AttachmentManagerImpl struct {
	deps            *Dependencies
	attachments     map[string]*Attachment
	attachmentsPath string
	mu              sync.RWMutex
}

// NewAttachmentManager creates a new attachment manager
func NewAttachmentManager(deps *Dependencies) AttachmentManager {
	homeDir, _ := os.UserHomeDir()
	attachmentsPath := filepath.Join(homeDir, ".arien", "attachments")
	
	// Ensure attachments directory exists
	os.MkdirAll(attachmentsPath, 0755)
	
	return &AttachmentManagerImpl{
		deps:            deps,
		attachments:     make(map[string]*Attachment),
		attachmentsPath: attachmentsPath,
	}
}

// AddAttachment adds a file attachment
func (am *AttachmentManagerImpl) AddAttachment(filePath string) (*Attachment, error) {
	am.mu.Lock()
	defer am.mu.Unlock()

	am.deps.Logger.Debug("Adding attachment", "file", filePath)

	// Validate the attachment
	if err := am.ValidateAttachment(filePath); err != nil {
		return nil, fmt.Errorf("attachment validation failed: %w", err)
	}

	// Get file info
	info, err := os.Stat(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to get file info: %w", err)
	}

	// Check file size
	if info.Size() > am.deps.Config.MaxAttachmentSize {
		return nil, fmt.Errorf("file size (%d bytes) exceeds maximum allowed size (%d bytes)", 
			info.Size(), am.deps.Config.MaxAttachmentSize)
	}

	// Determine MIME type
	mimeType := am.getMimeType(filePath)
	if !am.isMimeTypeAllowed(mimeType) {
		return nil, fmt.Errorf("file type not allowed: %s", mimeType)
	}

	// Create attachment
	attachment := &Attachment{
		ID:       generateAttachmentID(),
		Name:     filepath.Base(filePath),
		Path:     filePath,
		Size:     info.Size(),
		MimeType: mimeType,
		Created:  time.Now(),
	}

	// Copy file to attachments directory if it's not already there
	if !strings.HasPrefix(filePath, am.attachmentsPath) {
		destPath := filepath.Join(am.attachmentsPath, attachment.ID+"_"+attachment.Name)
		if err := am.copyFile(filePath, destPath); err != nil {
			return nil, fmt.Errorf("failed to copy attachment: %w", err)
		}
		attachment.Path = destPath
	}

	// Store attachment
	am.attachments[attachment.ID] = attachment

	am.deps.Logger.Info("Attachment added successfully", 
		"id", attachment.ID, 
		"name", attachment.Name, 
		"size", attachment.Size,
		"mime_type", attachment.MimeType)

	return attachment, nil
}

// RemoveAttachment removes an attachment
func (am *AttachmentManagerImpl) RemoveAttachment(id string) error {
	am.mu.Lock()
	defer am.mu.Unlock()

	am.deps.Logger.Debug("Removing attachment", "id", id)

	attachment, exists := am.attachments[id]
	if !exists {
		return fmt.Errorf("attachment not found: %s", id)
	}

	// Remove file if it's in our attachments directory
	if strings.HasPrefix(attachment.Path, am.attachmentsPath) {
		if err := os.Remove(attachment.Path); err != nil && !os.IsNotExist(err) {
			am.deps.Logger.Warn("Failed to remove attachment file", "path", attachment.Path, "error", err)
		}
	}

	// Remove from memory
	delete(am.attachments, id)

	am.deps.Logger.Info("Attachment removed successfully", "id", id, "name", attachment.Name)
	return nil
}

// GetAttachment retrieves an attachment by ID
func (am *AttachmentManagerImpl) GetAttachment(id string) (*Attachment, error) {
	am.mu.RLock()
	defer am.mu.RUnlock()

	attachment, exists := am.attachments[id]
	if !exists {
		return nil, fmt.Errorf("attachment not found: %s", id)
	}

	return attachment, nil
}

// ValidateAttachment validates a file for attachment
func (am *AttachmentManagerImpl) ValidateAttachment(filePath string) error {
	// Check if attachments are enabled
	if !am.deps.Config.AttachmentsEnabled {
		return fmt.Errorf("attachments are disabled")
	}

	// Validate file path
	if err := am.deps.Validator.ValidateFilePath(filePath); err != nil {
		return fmt.Errorf("invalid file path: %w", err)
	}

	// Check if file exists
	if _, err := os.Stat(filePath); err != nil {
		if os.IsNotExist(err) {
			return fmt.Errorf("file does not exist: %s", filePath)
		}
		return fmt.Errorf("failed to access file: %w", err)
	}

	// Check if it's a regular file
	info, err := os.Stat(filePath)
	if err != nil {
		return fmt.Errorf("failed to get file info: %w", err)
	}

	if !info.Mode().IsRegular() {
		return fmt.Errorf("not a regular file: %s", filePath)
	}

	// Check file size
	if info.Size() > am.deps.Config.MaxAttachmentSize {
		return fmt.Errorf("file too large: %d bytes (max: %d bytes)", 
			info.Size(), am.deps.Config.MaxAttachmentSize)
	}

	// Check MIME type
	mimeType := am.getMimeType(filePath)
	if !am.isMimeTypeAllowed(mimeType) {
		return fmt.Errorf("file type not allowed: %s", mimeType)
	}

	return nil
}

// ProcessAttachment processes an attachment and returns its content
func (am *AttachmentManagerImpl) ProcessAttachment(attachment *Attachment) (string, error) {
	am.deps.Logger.Debug("Processing attachment", "id", attachment.ID, "mime_type", attachment.MimeType)

	// Check if file still exists
	if _, err := os.Stat(attachment.Path); err != nil {
		return "", fmt.Errorf("attachment file not found: %s", attachment.Path)
	}

	// Read file content based on MIME type
	switch {
	case strings.HasPrefix(attachment.MimeType, "text/"):
		return am.readTextFile(attachment.Path)
		
	case attachment.MimeType == "application/json":
		return am.readTextFile(attachment.Path)
		
	case attachment.MimeType == "application/xml" || attachment.MimeType == "text/xml":
		return am.readTextFile(attachment.Path)
		
	case strings.HasPrefix(attachment.MimeType, "image/"):
		return am.processImageFile(attachment)
		
	default:
		return "", fmt.Errorf("unsupported file type for processing: %s", attachment.MimeType)
	}
}

// Private helper methods

// getMimeType determines the MIME type of a file
func (am *AttachmentManagerImpl) getMimeType(filePath string) string {
	// First try to detect by extension
	ext := filepath.Ext(filePath)
	if mimeType := mime.TypeByExtension(ext); mimeType != "" {
		return mimeType
	}

	// Try to detect by reading file content
	file, err := os.Open(filePath)
	if err != nil {
		return "application/octet-stream"
	}
	defer file.Close()

	// Read first 512 bytes for content detection
	buffer := make([]byte, 512)
	n, err := file.Read(buffer)
	if err != nil && err != io.EOF {
		return "application/octet-stream"
	}

	// Use Go's built-in content type detection
	return http.DetectContentType(buffer[:n])
}

// isMimeTypeAllowed checks if a MIME type is allowed
func (am *AttachmentManagerImpl) isMimeTypeAllowed(mimeType string) bool {
	for _, allowed := range am.deps.Config.AllowedMimeTypes {
		if allowed == mimeType {
			return true
		}
		
		// Check for wildcard matches (e.g., "text/*")
		if strings.HasSuffix(allowed, "/*") {
			prefix := strings.TrimSuffix(allowed, "/*")
			if strings.HasPrefix(mimeType, prefix+"/") {
				return true
			}
		}
	}
	return false
}

// copyFile copies a file from src to dst
func (am *AttachmentManagerImpl) copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return fmt.Errorf("failed to open source file: %w", err)
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return fmt.Errorf("failed to create destination file: %w", err)
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	if err != nil {
		return fmt.Errorf("failed to copy file content: %w", err)
	}

	return nil
}

// readTextFile reads a text file and returns its content
func (am *AttachmentManagerImpl) readTextFile(filePath string) (string, error) {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to read file: %w", err)
	}

	// Limit content size for display
	maxSize := 50000 // 50KB
	if len(content) > maxSize {
		return string(content[:maxSize]) + "\n\n[Content truncated - file is larger than 50KB]", nil
	}

	return string(content), nil
}

// processImageFile processes an image file
func (am *AttachmentManagerImpl) processImageFile(attachment *Attachment) (string, error) {
	// For images, we return metadata and a description
	info, err := os.Stat(attachment.Path)
	if err != nil {
		return "", fmt.Errorf("failed to get image info: %w", err)
	}

	return fmt.Sprintf(`Image File: %s
Size: %d bytes
Type: %s
Created: %s
Path: %s

[This is an image file. The AI can see image metadata but cannot process the visual content directly.]`,
		attachment.Name,
		info.Size(),
		attachment.MimeType,
		attachment.Created.Format(time.RFC3339),
		attachment.Path), nil
}

// generateAttachmentID generates a unique attachment ID
func generateAttachmentID() string {
	return fmt.Sprintf("att_%d", time.Now().UnixNano())
}

// Cleanup methods

// CleanupOrphanedAttachments removes attachment files that are no longer referenced
func (am *AttachmentManagerImpl) CleanupOrphanedAttachments() error {
	am.mu.Lock()
	defer am.mu.Unlock()

	am.deps.Logger.Debug("Cleaning up orphaned attachments")

	// Read attachments directory
	files, err := os.ReadDir(am.attachmentsPath)
	if err != nil {
		return fmt.Errorf("failed to read attachments directory: %w", err)
	}

	deletedCount := 0
	for _, file := range files {
		if file.IsDir() {
			continue
		}

		filePath := filepath.Join(am.attachmentsPath, file.Name())
		
		// Check if this file is referenced by any attachment
		isReferenced := false
		for _, attachment := range am.attachments {
			if attachment.Path == filePath {
				isReferenced = true
				break
			}
		}

		// If not referenced, delete it
		if !isReferenced {
			if err := os.Remove(filePath); err != nil {
				am.deps.Logger.Warn("Failed to remove orphaned attachment", "file", filePath, "error", err)
			} else {
				deletedCount++
			}
		}
	}

	am.deps.Logger.Info("Orphaned attachments cleaned up", "deleted", deletedCount)
	return nil
}

// GetAttachmentStats returns statistics about attachments
func (am *AttachmentManagerImpl) GetAttachmentStats() map[string]interface{} {
	am.mu.RLock()
	defer am.mu.RUnlock()

	totalSize := int64(0)
	mimeTypeCounts := make(map[string]int)

	for _, attachment := range am.attachments {
		totalSize += attachment.Size
		mimeTypeCounts[attachment.MimeType]++
	}

	return map[string]interface{}{
		"total_attachments": len(am.attachments),
		"total_size":        totalSize,
		"mime_type_counts":  mimeTypeCounts,
	}
}
