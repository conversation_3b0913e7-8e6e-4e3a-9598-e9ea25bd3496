package main

import (
	"fmt"
	"log"

	"arien/internal/core"
	"arien/internal/utils"
)

func main() {
	logger := utils.NewLogger()
	
	// Create config manager
	config, err := core.NewConfig(logger)
	if err != nil {
		log.Fatalf("Failed to create config: %v", err)
	}

	// Get OpenAI provider config
	providerConfig, exists := config.GetProvider("openai")
	if !exists {
		log.Fatal("OpenAI provider not found in config")
	}

	fmt.Printf("Provider config loaded:\n")
	fmt.Printf("  Model: %s\n", providerConfig.Model)
	fmt.Printf("  API Key length: %d\n", len(providerConfig.APIKey))
	fmt.Printf("  API Key (first 10 chars): %s...\n", providerConfig.APIKey[:min(10, len(providerConfig.APIKey))])
	
	// Check if the API key looks valid
	if len(providerConfig.APIKey) < 20 {
		fmt.Printf("WARNING: API key seems too short!\n")
	}
	
	if providerConfig.APIKey == "" {
		fmt.Printf("ERROR: API key is empty!\n")
	}
	
	// Test if it starts with expected OpenAI prefix
	if len(providerConfig.APIKey) > 3 && providerConfig.APIKey[:3] == "sk-" {
		fmt.Printf("✓ API key has correct OpenAI format\n")
	} else {
		fmt.Printf("⚠ API key doesn't start with 'sk-' (might be encrypted or invalid)\n")
	}
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
