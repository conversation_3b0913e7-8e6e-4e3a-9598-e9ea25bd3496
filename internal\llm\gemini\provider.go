/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package gemini

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"arien/internal/config"
	"arien/internal/interfaces"

	"github.com/charmbracelet/log"
	"golang.org/x/time/rate"
)

// GeminiRequest represents a request to Gemini API
type GeminiRequest struct {
	Contents         []GeminiContent         `json:"contents"`
	Tools            []GeminiTool            `json:"tools,omitempty"`
	GenerationConfig *GeminiGenerationConfig `json:"generationConfig,omitempty"`
	SafetySettings   []GeminiSafetySetting   `json:"safetySettings,omitempty"`
}

// GeminiContent represents content in Gemini format
type GeminiContent struct {
	Role  string       `json:"role"`
	Parts []GeminiPart `json:"parts"`
}

// GeminiPart represents a part of content
type GeminiPart struct {
	Text             string                  `json:"text,omitempty"`
	FunctionCall     *GeminiFunctionCall     `json:"functionCall,omitempty"`
	FunctionResponse *GeminiFunctionResponse `json:"functionResponse,omitempty"`
}

// GeminiFunctionCall represents a function call
type GeminiFunctionCall struct {
	Name string                 `json:"name"`
	Args map[string]interface{} `json:"args"`
}

// GeminiFunctionResponse represents a function response
type GeminiFunctionResponse struct {
	Name     string                 `json:"name"`
	Response map[string]interface{} `json:"response"`
}

// GeminiTool represents a tool definition
type GeminiTool struct {
	FunctionDeclarations []GeminiFunctionDeclaration `json:"functionDeclarations"`
}

// GeminiFunctionDeclaration represents a function declaration
type GeminiFunctionDeclaration struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
}

// GeminiGenerationConfig represents generation configuration
type GeminiGenerationConfig struct {
	Temperature     float64  `json:"temperature,omitempty"`
	TopP            float64  `json:"topP,omitempty"`
	TopK            int      `json:"topK,omitempty"`
	MaxOutputTokens int      `json:"maxOutputTokens,omitempty"`
	StopSequences   []string `json:"stopSequences,omitempty"`
}

// GeminiSafetySetting represents safety settings
type GeminiSafetySetting struct {
	Category  string `json:"category"`
	Threshold string `json:"threshold"`
}

// GeminiResponse represents a response from Gemini API
type GeminiResponse struct {
	Candidates     []GeminiCandidate     `json:"candidates"`
	PromptFeedback *GeminiPromptFeedback `json:"promptFeedback,omitempty"`
	UsageMetadata  *GeminiUsageMetadata  `json:"usageMetadata,omitempty"`
}

// GeminiCandidate represents a candidate response
type GeminiCandidate struct {
	Content       GeminiContent        `json:"content"`
	FinishReason  string               `json:"finishReason"`
	Index         int                  `json:"index"`
	SafetyRatings []GeminiSafetyRating `json:"safetyRatings"`
}

// GeminiSafetyRating represents a safety rating
type GeminiSafetyRating struct {
	Category    string `json:"category"`
	Probability string `json:"probability"`
}

// GeminiPromptFeedback represents prompt feedback
type GeminiPromptFeedback struct {
	SafetyRatings []GeminiSafetyRating `json:"safetyRatings"`
	BlockReason   string               `json:"blockReason,omitempty"`
}

// GeminiUsageMetadata represents usage metadata
type GeminiUsageMetadata struct {
	PromptTokenCount     int `json:"promptTokenCount"`
	CandidatesTokenCount int `json:"candidatesTokenCount"`
	TotalTokenCount      int `json:"totalTokenCount"`
}

// Provider implements the Google Gemini LLM provider
type Provider struct {
	config     config.ProviderConfig
	logger     *log.Logger
	httpClient *http.Client
	limiter    *rate.Limiter
	baseURL    string
}

// NewProvider creates a new Gemini provider
func NewProvider(providerConfig config.ProviderConfig, logger *log.Logger) (*Provider, error) {
	if providerConfig.APIKey == "" {
		return nil, fmt.Errorf("Gemini API key is required")
	}

	return &Provider{
		config:  providerConfig,
		logger:  logger,
		baseURL: "https://generativelanguage.googleapis.com/v1beta",
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		limiter: rate.NewLimiter(rate.Every(time.Minute/60), 60), // 60 requests per minute
	}, nil
}

// Name returns the provider name
func (p *Provider) Name() string {
	return "gemini"
}

// Models returns available models for Gemini
func (p *Provider) Models() []string {
	return []string{
		"gemini-pro",
		"gemini-pro-vision",
		"gemini-1.5-pro",
		"gemini-1.5-flash",
	}
}

// ProcessMessage processes a message using Gemini API
func (p *Provider) ProcessMessage(ctx context.Context, request *interfaces.Request) (*interfaces.Response, error) {
	// Rate limiting
	if err := p.limiter.Wait(ctx); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	// Convert request to Gemini format
	geminiReq := p.convertRequest(request)

	// Make API call
	geminiResp, err := p.makeAPICall(ctx, geminiReq, request.Model)
	if err != nil {
		return nil, fmt.Errorf("Gemini API call failed: %w", err)
	}

	// Convert response to standard format
	response := p.convertResponse(geminiResp, request.Model)
	return response, nil
}

// SupportsToolCalling returns true if Gemini supports function calling
func (p *Provider) SupportsToolCalling() bool {
	return true
}

// SupportsStreaming returns true if Gemini supports streaming
func (p *Provider) SupportsStreaming() bool {
	return true
}

// convertRequest converts standard request to Gemini format
func (p *Provider) convertRequest(request *interfaces.Request) *GeminiRequest {
	geminiReq := &GeminiRequest{
		Contents: []GeminiContent{},
		GenerationConfig: &GeminiGenerationConfig{
			Temperature:     request.Temperature,
			MaxOutputTokens: request.MaxTokens,
		},
		SafetySettings: []GeminiSafetySetting{
			{Category: "HARM_CATEGORY_HARASSMENT", Threshold: "BLOCK_MEDIUM_AND_ABOVE"},
			{Category: "HARM_CATEGORY_HATE_SPEECH", Threshold: "BLOCK_MEDIUM_AND_ABOVE"},
			{Category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", Threshold: "BLOCK_MEDIUM_AND_ABOVE"},
			{Category: "HARM_CATEGORY_DANGEROUS_CONTENT", Threshold: "BLOCK_MEDIUM_AND_ABOVE"},
		},
	}

	// Convert messages
	for _, msg := range request.Messages {
		role := msg.Role
		if role == "assistant" {
			role = "model"
		}

		content := GeminiContent{
			Role:  role,
			Parts: []GeminiPart{},
		}

		// Add text content
		if msg.Content != "" {
			content.Parts = append(content.Parts, GeminiPart{
				Text: msg.Content,
			})
		}

		// Add tool calls
		for _, toolCall := range msg.ToolCalls {
			content.Parts = append(content.Parts, GeminiPart{
				FunctionCall: &GeminiFunctionCall{
					Name: toolCall.Name,
					Args: toolCall.Arguments,
				},
			})
		}

		geminiReq.Contents = append(geminiReq.Contents, content)
	}

	// Convert tools
	if len(request.Tools) > 0 {
		tool := GeminiTool{
			FunctionDeclarations: []GeminiFunctionDeclaration{},
		}

		for _, t := range request.Tools {
			if t.Type == "function" {
				tool.FunctionDeclarations = append(tool.FunctionDeclarations, GeminiFunctionDeclaration{
					Name:        t.Function.Name,
					Description: t.Function.Description,
					Parameters:  t.Function.Parameters,
				})
			}
		}

		geminiReq.Tools = []GeminiTool{tool}
	}

	return geminiReq
}

// makeAPICall makes an API call to Gemini
func (p *Provider) makeAPICall(ctx context.Context, request *GeminiRequest, model string) (*GeminiResponse, error) {
	// Prepare request body
	reqBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	url := fmt.Sprintf("%s/models/%s:generateContent?key=%s", p.baseURL, model, p.config.APIKey)
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")

	// Make request
	resp, err := p.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Check status code
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	// Parse response
	var geminiResp GeminiResponse
	if err := json.Unmarshal(respBody, &geminiResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &geminiResp, nil
}

// convertResponse converts Gemini response to standard format
func (p *Provider) convertResponse(geminiResp *GeminiResponse, model string) *interfaces.Response {
	response := &interfaces.Response{
		Model:     model,
		Provider:  p.Name(),
		Timestamp: time.Now(),
		Usage: interfaces.Usage{
			PromptTokens:     0,
			CompletionTokens: 0,
			TotalTokens:      0,
		},
	}

	// Extract usage metadata
	if geminiResp.UsageMetadata != nil {
		response.Usage.PromptTokens = geminiResp.UsageMetadata.PromptTokenCount
		response.Usage.CompletionTokens = geminiResp.UsageMetadata.CandidatesTokenCount
		response.Usage.TotalTokens = geminiResp.UsageMetadata.TotalTokenCount
	}

	// Extract content from first candidate
	if len(geminiResp.Candidates) > 0 {
		candidate := geminiResp.Candidates[0]

		// Extract text content
		for _, part := range candidate.Content.Parts {
			if part.Text != "" {
				response.Content += part.Text
			}
		}

		// Extract tool calls
		for _, part := range candidate.Content.Parts {
			if part.FunctionCall != nil {
				toolCall := interfaces.ToolCall{
					ID:        fmt.Sprintf("call_%d", time.Now().UnixNano()),
					Type:      "function",
					Name:      part.FunctionCall.Name,
					Arguments: part.FunctionCall.Args,
				}
				response.ToolCalls = append(response.ToolCalls, toolCall)
			}
		}
	}

	return response
}

// Shutdown gracefully shuts down the provider
func (p *Provider) Shutdown(ctx context.Context) error {
	p.logger.Info("Shutting down Gemini provider...")
	return nil
}
