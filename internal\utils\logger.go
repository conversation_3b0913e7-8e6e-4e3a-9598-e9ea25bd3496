/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package utils

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"time"

	"github.com/charmbracelet/log"
)

// NewLogger creates a new structured logger instance
func NewLogger() *log.Logger {
	logger := log.New(os.Stderr)
	
	// Set log level based on environment
	level := log.InfoLevel
	if os.Getenv("ARIEN_DEBUG") == "true" {
		level = log.DebugLevel
	}
	logger.SetLevel(level)

	// Configure logger format
	logger.SetReportTimestamp(true)
	logger.SetReportCaller(false)

	// Create log directory and setup file logging
	homeDir, err := os.UserHomeDir()
	if err == nil {
		logDir := filepath.Join(homeDir, ".arien", "logs")
		if err := os.MkdirAll(logDir, 0755); err == nil {
			// Create log file with timestamp
			logFileName := fmt.Sprintf("arien-%s.log", time.Now().Format("2006-01-02"))
			logFilePath := filepath.Join(logDir, logFileName)

			// Open log file
			logFile, err := os.OpenFile(logFilePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
			if err == nil {
				// Create multi-writer to write to both stderr and file
				multiWriter := io.MultiWriter(os.Stderr, logFile)
				logger.SetOutput(multiWriter)
			}
		}
	}

	return logger
}

// MaskString masks a string showing only the last n characters
func MaskString(s string, showLast int) string {
	if len(s) <= showLast {
		return "***"
	}
	
	masked := ""
	for i := 0; i < len(s)-showLast; i++ {
		masked += "*"
	}
	
	return masked + s[len(s)-showLast:]
}

// TruncateString truncates a string to maxLength with ellipsis
func TruncateString(s string, maxLength int) string {
	if len(s) <= maxLength {
		return s
	}
	
	if maxLength <= 3 {
		return "..."
	}
	
	return s[:maxLength-3] + "..."
}
