/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"fmt"
	"strings"
)

// generateTemplate generates a template for the specified diagram type
func (t *MermaidTool) generateTemplate(diagramType, title string) string {
	switch diagramType {
	case "flowchart":
		return fmt.Sprintf(`flowchart TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Process 1]
    B -->|No| D[Process 2]
    C --> E[End]
    D --> E
    
    %% Title: %s`, title)

	case "sequence":
		return fmt.Sprintf(`sequenceDiagram
    participant Client
    participant Server
    participant Database
    
    Client->>Server: Request
    Server->>Database: Query
    Database-->>Server: Result
    Server-->>Client: Response
    
    %% Title: %s`, title)

	case "class":
		return fmt.Sprintf(`classDiagram
    class User {
        +String name
        +String email
        +login()
        +logout()
    }
    
    class Admin {
        +String permissions
        +manageUsers()
    }
    
    User <|-- Admin
    
    %% Title: %s`, title)

	case "state":
		return fmt.Sprintf(`stateDiagram-v2
    [*] --> Idle
    Idle --> Processing : start
    Processing --> Success : complete
    Processing --> Error : fail
    Success --> [*]
    Error --> Idle : retry
    
    %% Title: %s`, title)

	case "entity_relationship":
		return fmt.Sprintf(`erDiagram
    USER {
        int id PK
        string name
        string email
    }
    
    ORDER {
        int id PK
        int user_id FK
        date created_at
    }
    
    USER ||--o{ ORDER : places
    
    %% Title: %s`, title)

	case "gantt":
		return fmt.Sprintf(`gantt
    title %s
    dateFormat  YYYY-MM-DD
    section Planning
    Research    :done, research, 2024-01-01, 2024-01-05
    Design      :active, design, 2024-01-06, 2024-01-12
    section Development
    Backend     :backend, 2024-01-13, 2024-01-26
    Frontend    :frontend, 2024-01-20, 2024-02-02
    section Testing
    Unit Tests  :testing, 2024-01-27, 2024-02-03
    Integration :integration, 2024-02-04, 2024-02-10`, title)

	case "pie":
		return fmt.Sprintf(`pie title %s
    "Category A" : 42
    "Category B" : 30
    "Category C" : 20
    "Category D" : 8`, title)

	case "user_journey":
		return fmt.Sprintf(`journey
    title %s
    section Discovery
      Visit Website: 5: User
      Browse Products: 4: User
    section Purchase
      Add to Cart: 3: User
      Checkout: 2: User
      Payment: 1: User
    section Post-Purchase
      Receive Product: 5: User
      Leave Review: 4: User`, title)

	case "gitgraph":
		return `gitGraph
    commit
    branch develop
    checkout develop
    commit
    commit
    checkout main
    merge develop
    commit`

	case "mindmap":
		return fmt.Sprintf(`mindmap
  root)%s(
    Topic A
      Subtopic 1
      Subtopic 2
    Topic B
      Subtopic 3
        Detail 1
        Detail 2
    Topic C`, title)

	default:
		return fmt.Sprintf(`flowchart TD
    A[%s] --> B[Add your content here]
    B --> C[Customize as needed]`, title)
	}
}

// generateHTMLWithMermaid creates an HTML file with embedded Mermaid diagram
func (t *MermaidTool) generateHTMLWithMermaid(content, theme string, width, height int, background string) string {
	if background == "" {
		background = "#ffffff"
	}

	return fmt.Sprintf(`<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mermaid Diagram</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: %s;
        }
        .diagram-container {
            max-width: %dpx;
            max-height: %dpx;
            margin: 0 auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .controls {
            margin-bottom: 20px;
            text-align: center;
        }
        button {
            margin: 0 5px;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .info {
            margin-top: 20px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="diagram-container">
        <div class="controls">
            <button onclick="downloadSVG()">Download SVG</button>
            <button onclick="downloadPNG()">Download PNG</button>
            <button onclick="toggleTheme()">Toggle Theme</button>
        </div>
        
        <div class="mermaid" id="diagram">
%s
        </div>
        
        <div class="info">
            <strong>Generated by Arien Mermaid Tool</strong><br>
            Theme: %s | Size: %dx%d<br>
            Right-click on diagram to save image or copy
        </div>
    </div>

    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: '%s',
            themeVariables: {
                primaryColor: '#ff6b6b',
                primaryTextColor: '#333',
                primaryBorderColor: '#ff6b6b',
                lineColor: '#333',
                secondaryColor: '#4ecdc4',
                tertiaryColor: '#ffe66d'
            }
        });

        function downloadSVG() {
            const svg = document.querySelector('.mermaid svg');
            if (svg) {
                const svgData = new XMLSerializer().serializeToString(svg);
                const blob = new Blob([svgData], {type: 'image/svg+xml'});
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'diagram.svg';
                a.click();
                URL.revokeObjectURL(url);
            }
        }

        function downloadPNG() {
            const svg = document.querySelector('.mermaid svg');
            if (svg) {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();
                
                img.onload = function() {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);
                    
                    canvas.toBlob(function(blob) {
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = 'diagram.png';
                        a.click();
                        URL.revokeObjectURL(url);
                    });
                };
                
                const svgData = new XMLSerializer().serializeToString(svg);
                const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
                const url = URL.createObjectURL(svgBlob);
                img.src = url;
            }
        }

        let currentTheme = '%s';
        function toggleTheme() {
            currentTheme = currentTheme === 'default' ? 'dark' : 'default';
            location.reload();
        }
    </script>
</body>
</html>`, background, width, height, content, theme, width, height, theme, theme)
}

// detectDiagramType detects the type of Mermaid diagram from content
func (t *MermaidTool) detectDiagramType(content string) string {
	content = strings.ToLower(content)
	
	if strings.Contains(content, "flowchart") || strings.Contains(content, "graph") {
		return "flowchart"
	} else if strings.Contains(content, "sequencediagram") {
		return "sequence"
	} else if strings.Contains(content, "classdiagram") {
		return "class"
	} else if strings.Contains(content, "statediagram") {
		return "state"
	} else if strings.Contains(content, "erdiagram") {
		return "entity_relationship"
	} else if strings.Contains(content, "gantt") {
		return "gantt"
	} else if strings.Contains(content, "pie") {
		return "pie"
	} else if strings.Contains(content, "journey") {
		return "user_journey"
	} else if strings.Contains(content, "gitgraph") {
		return "gitgraph"
	} else if strings.Contains(content, "mindmap") {
		return "mindmap"
	} else if strings.Contains(content, "requirementdiagram") {
		return "requirement"
	}
	
	return "unknown"
}

// countNonEmptyLines counts non-empty lines in a slice
func countNonEmptyLines(lines []string) int {
	count := 0
	for _, line := range lines {
		if strings.TrimSpace(line) != "" {
			count++
		}
	}
	return count
}
