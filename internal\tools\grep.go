/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"bufio"
	"context"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"
)

// GrepTool implements text search with regex support, context lines, and output formatting
type GrepTool struct {
	maxResults int
	maxFileSize int64
}

// NewGrepTool creates a new grep tool
func NewGrepTool() *GrepTool {
	return &GrepTool{
		maxResults:  1000,
		maxFileSize: 50 * 1024 * 1024, // 50MB
	}
}

// Name returns the tool name
func (t *GrepTool) Name() string {
	return "grep"
}

// Description returns the tool description
func (t *GrepTool) Description() string {
	return "Text search with regex support, context lines, and output formatting"
}

// Parameters returns the tool parameter schema
func (t *GrepTool) Parameters() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"pattern": StringParameter("Search pattern (regex supported)", true),
			"path": StringParameter("File or directory path to search", true),
			"recursive": BoolParameter("Search recursively in directories", false),
			"case_sensitive": BoolParameter("Case sensitive search", false),
			"regex": BoolParameter("Treat pattern as regular expression", true),
			"context": IntParameter("Number of context lines to show before and after matches", 0, 10),
			"context_before": IntParameter("Number of context lines to show before matches", 0, 10),
			"context_after": IntParameter("Number of context lines to show after matches", 0, 10),
			"max_results": IntParameter("Maximum number of results to return", 1, 1000),
			"include_line_numbers": BoolParameter("Include line numbers in output", true),
			"include_filenames": BoolParameter("Include filenames in output", true),
			"whole_word": BoolParameter("Match whole words only", false),
			"invert_match": BoolParameter("Invert match (show non-matching lines)", false),
			"file_pattern": StringParameter("File pattern to filter (e.g., '*.go', '*.txt')", false),
			"exclude_pattern": StringParameter("File pattern to exclude", false),
		},
		"required": []string{"pattern", "path"},
	}
}

// GrepMatch represents a single match result
type GrepMatch struct {
	File        string   `json:"file"`
	LineNumber  int      `json:"line_number"`
	Line        string   `json:"line"`
	Match       string   `json:"match"`
	ContextBefore []string `json:"context_before,omitempty"`
	ContextAfter  []string `json:"context_after,omitempty"`
	Column      int      `json:"column"`
}

// GrepResult represents the complete grep operation result
type GrepResult struct {
	Pattern     string      `json:"pattern"`
	Path        string      `json:"path"`
	Matches     []GrepMatch `json:"matches"`
	TotalMatches int        `json:"total_matches"`
	FilesSearched int       `json:"files_searched"`
	FilesWithMatches int    `json:"files_with_matches"`
	Duration    time.Duration `json:"duration"`
	Truncated   bool        `json:"truncated"`
}

// Execute performs the grep operation
func (t *GrepTool) Execute(ctx context.Context, args map[string]interface{}) Result {
	start := time.Now()
	
	// Parse arguments
	pattern := getStringArg(args, "pattern", "")
	path := getStringArg(args, "path", "")
	recursive := getBoolArg(args, "recursive", false)
	caseSensitive := getBoolArg(args, "case_sensitive", false)
	useRegex := getBoolArg(args, "regex", true)
	context := getIntArg(args, "context", 0)
	contextBefore := getIntArg(args, "context_before", context)
	contextAfter := getIntArg(args, "context_after", context)
	maxResults := getIntArg(args, "max_results", 100)
	includeLineNumbers := getBoolArg(args, "include_line_numbers", true)
	includeFilenames := getBoolArg(args, "include_filenames", true)
	wholeWord := getBoolArg(args, "whole_word", false)
	invertMatch := getBoolArg(args, "invert_match", false)
	filePattern := getStringArg(args, "file_pattern", "")
	excludePattern := getStringArg(args, "exclude_pattern", "")
	
	if maxResults > t.maxResults {
		maxResults = t.maxResults
	}
	
	// Compile regex pattern
	var regex *regexp.Regexp
	var err error
	
	if useRegex {
		flags := ""
		if !caseSensitive {
			flags = "(?i)"
		}
		if wholeWord {
			pattern = `\b` + pattern + `\b`
		}
		regex, err = regexp.Compile(flags + pattern)
		if err != nil {
			return Result{Error: fmt.Errorf("invalid regex pattern: %w", err)}
		}
	} else {
		// Escape special regex characters for literal search
		escapedPattern := regexp.QuoteMeta(pattern)
		if wholeWord {
			escapedPattern = `\b` + escapedPattern + `\b`
		}
		flags := ""
		if !caseSensitive {
			flags = "(?i)"
		}
		regex, err = regexp.Compile(flags + escapedPattern)
		if err != nil {
			return Result{Error: fmt.Errorf("failed to compile pattern: %w", err)}
		}
	}
	
	// Perform search
	result, err := t.performSearch(ctx, regex, path, recursive, contextBefore, contextAfter, 
		maxResults, includeLineNumbers, includeFilenames, invertMatch, filePattern, excludePattern)
	if err != nil {
		return Result{Error: err}
	}
	
	result.Duration = time.Since(start)
	
	// Format output
	output := t.formatGrepResult(result)
	
	return Result{
		Output: output,
		Data:   result,
	}
}

// performSearch performs the actual search operation
func (t *GrepTool) performSearch(ctx context.Context, regex *regexp.Regexp, path string, recursive bool,
	contextBefore, contextAfter, maxResults int, includeLineNumbers, includeFilenames, invertMatch bool,
	filePattern, excludePattern string) (*GrepResult, error) {
	
	result := &GrepResult{
		Pattern: regex.String(),
		Path:    path,
		Matches: make([]GrepMatch, 0),
	}
	
	// Get list of files to search
	files, err := t.getFilesToSearch(path, recursive, filePattern, excludePattern)
	if err != nil {
		return nil, fmt.Errorf("failed to get files to search: %w", err)
	}
	
	result.FilesSearched = len(files)
	
	// Search each file
	for _, file := range files {
		select {
		case <-ctx.Done():
			return result, ctx.Err()
		default:
		}
		
		matches, err := t.searchFile(file, regex, contextBefore, contextAfter, 
			includeLineNumbers, includeFilenames, invertMatch)
		if err != nil {
			// Log error but continue with other files
			continue
		}
		
		if len(matches) > 0 {
			result.FilesWithMatches++
			result.Matches = append(result.Matches, matches...)
			
			// Check if we've reached max results
			if len(result.Matches) >= maxResults {
				result.Matches = result.Matches[:maxResults]
				result.Truncated = true
				break
			}
		}
	}
	
	result.TotalMatches = len(result.Matches)
	return result, nil
}

// searchFile searches a single file for matches
func (t *GrepTool) searchFile(filePath string, regex *regexp.Regexp, contextBefore, contextAfter int,
	includeLineNumbers, includeFilenames, invertMatch bool) ([]GrepMatch, error) {
	
	// Check file size
	info, err := os.Stat(filePath)
	if err != nil {
		return nil, err
	}
	
	if info.Size() > t.maxFileSize {
		return nil, fmt.Errorf("file too large: %d bytes", info.Size())
	}
	
	file, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()
	
	var matches []GrepMatch
	var lines []string
	scanner := bufio.NewScanner(file)
	lineNumber := 0
	
	// Read all lines for context support
	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}
	
	if err := scanner.Err(); err != nil {
		return nil, err
	}
	
	// Search through lines
	for i, line := range lines {
		lineNumber = i + 1
		
		isMatch := regex.MatchString(line)
		if invertMatch {
			isMatch = !isMatch
		}
		
		if isMatch {
			match := GrepMatch{
				File:       filePath,
				LineNumber: lineNumber,
				Line:       line,
				Column:     0,
			}
			
			if !invertMatch {
				// Find the actual match text and column
				loc := regex.FindStringIndex(line)
				if loc != nil {
					match.Match = line[loc[0]:loc[1]]
					match.Column = loc[0] + 1
				}
			}
			
			// Add context lines
			if contextBefore > 0 {
				start := i - contextBefore
				if start < 0 {
					start = 0
				}
				for j := start; j < i; j++ {
					match.ContextBefore = append(match.ContextBefore, lines[j])
				}
			}
			
			if contextAfter > 0 {
				end := i + contextAfter + 1
				if end > len(lines) {
					end = len(lines)
				}
				for j := i + 1; j < end; j++ {
					match.ContextAfter = append(match.ContextAfter, lines[j])
				}
			}
			
			matches = append(matches, match)
		}
	}
	
	return matches, nil
}

// getFilesToSearch returns a list of files to search based on the path and filters
func (t *GrepTool) getFilesToSearch(path string, recursive bool, filePattern, excludePattern string) ([]string, error) {
	var files []string
	
	info, err := os.Stat(path)
	if err != nil {
		return nil, err
	}
	
	if !info.IsDir() {
		// Single file
		if t.shouldIncludeFile(path, filePattern, excludePattern) {
			files = append(files, path)
		}
		return files, nil
	}
	
	// Directory
	if recursive {
		err = filepath.Walk(path, func(filePath string, info os.FileInfo, err error) error {
			if err != nil {
				return nil // Skip files with errors
			}
			
			if !info.IsDir() && t.shouldIncludeFile(filePath, filePattern, excludePattern) {
				files = append(files, filePath)
			}
			return nil
		})
	} else {
		entries, err := os.ReadDir(path)
		if err != nil {
			return nil, err
		}
		
		for _, entry := range entries {
			if !entry.IsDir() {
				filePath := filepath.Join(path, entry.Name())
				if t.shouldIncludeFile(filePath, filePattern, excludePattern) {
					files = append(files, filePath)
				}
			}
		}
	}
	
	return files, err
}

// shouldIncludeFile determines if a file should be included based on patterns
func (t *GrepTool) shouldIncludeFile(filePath, filePattern, excludePattern string) bool {
	fileName := filepath.Base(filePath)
	
	// Check exclude pattern first
	if excludePattern != "" {
		if matched, _ := filepath.Match(excludePattern, fileName); matched {
			return false
		}
	}
	
	// Check include pattern
	if filePattern != "" {
		if matched, _ := filepath.Match(filePattern, fileName); !matched {
			return false
		}
	}
	
	return true
}

// formatGrepResult formats the grep result for display
func (t *GrepTool) formatGrepResult(result *GrepResult) string {
	var output strings.Builder
	
	output.WriteString(fmt.Sprintf("🔍 Grep Search Results\n"))
	output.WriteString(fmt.Sprintf("Pattern: %s\n", result.Pattern))
	output.WriteString(fmt.Sprintf("Path: %s\n", result.Path))
	output.WriteString(fmt.Sprintf("Files Searched: %d | Files with Matches: %d | Total Matches: %d\n",
		result.FilesSearched, result.FilesWithMatches, result.TotalMatches))
	output.WriteString(fmt.Sprintf("Duration: %v\n", result.Duration))
	
	if result.Truncated {
		output.WriteString("⚠️  Results truncated to maximum limit\n")
	}
	
	output.WriteString("\n")
	
	if len(result.Matches) == 0 {
		output.WriteString("No matches found.\n")
		return output.String()
	}
	
	// Group matches by file
	fileMatches := make(map[string][]GrepMatch)
	for _, match := range result.Matches {
		fileMatches[match.File] = append(fileMatches[match.File], match)
	}
	
	// Display matches grouped by file
	for file, matches := range fileMatches {
		output.WriteString(fmt.Sprintf("📄 %s (%d matches)\n", file, len(matches)))
		
		for _, match := range matches {
			// Show context before
			for _, contextLine := range match.ContextBefore {
				output.WriteString(fmt.Sprintf("  %d-  %s\n", match.LineNumber-len(match.ContextBefore), contextLine))
			}
			
			// Show the match line
			if match.Column > 0 {
				output.WriteString(fmt.Sprintf("  %d:  %s (col %d)\n", match.LineNumber, match.Line, match.Column))
			} else {
				output.WriteString(fmt.Sprintf("  %d:  %s\n", match.LineNumber, match.Line))
			}
			
			// Show context after
			for i, contextLine := range match.ContextAfter {
				output.WriteString(fmt.Sprintf("  %d+  %s\n", match.LineNumber+i+1, contextLine))
			}
			
			if len(match.ContextBefore) > 0 || len(match.ContextAfter) > 0 {
				output.WriteString("  --\n")
			}
		}
		output.WriteString("\n")
	}
	
	return output.String()
}

// Validate validates the tool arguments
func (t *GrepTool) Validate(args map[string]interface{}) error {
	pattern := getStringArg(args, "pattern", "")
	if pattern == "" {
		return fmt.Errorf("pattern is required")
	}
	
	path := getStringArg(args, "path", "")
	if path == "" {
		return fmt.Errorf("path is required")
	}
	
	// Validate regex if regex mode is enabled
	useRegex := getBoolArg(args, "regex", true)
	if useRegex {
		_, err := regexp.Compile(pattern)
		if err != nil {
			return fmt.Errorf("invalid regex pattern: %w", err)
		}
	}
	
	return nil
}

// SupportsParallel returns whether this tool supports parallel execution
func (t *GrepTool) SupportsParallel() bool {
	return true
}
