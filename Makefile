# Arien - Elite AI-Powered Software Engineering Assistant
# Copyright 2025 Arien LLC - MIT License

.PHONY: build clean test run install deps fmt lint help

# Build variables
BINARY_NAME=arien
VERSION?=1.0.0
COMMIT?=$(shell git rev-parse --short HEAD 2>/dev/null || echo "dev")
DATE?=$(shell date -u +"%Y-%m-%dT%H:%M:%SZ")
LDFLAGS=-ldflags "-X main.version=$(VERSION) -X main.commit=$(COMMIT) -X main.date=$(DATE)"

# Go variables
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
GOFMT=gofmt

# Default target
all: deps fmt lint test build

# Install dependencies
deps:
	@echo "Installing dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy

# Format code
fmt:
	@echo "Formatting code..."
	$(GOFMT) -s -w .

# Lint code
lint:
	@echo "Linting code..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "golangci-lint not installed, skipping..."; \
	fi

# Run tests
test:
	@echo "Running tests..."
	$(GOTEST) -v ./...

# Build the application
build:
	@echo "Building $(BINARY_NAME)..."
	$(GOBUILD) $(LDFLAGS) -o $(BINARY_NAME) cmd/arien/main.go

# Build for multiple platforms
build-all:
	@echo "Building for multiple platforms..."
	GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BINARY_NAME)-linux-amd64 cmd/arien/main.go
	GOOS=darwin GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BINARY_NAME)-darwin-amd64 cmd/arien/main.go
	GOOS=darwin GOARCH=arm64 $(GOBUILD) $(LDFLAGS) -o $(BINARY_NAME)-darwin-arm64 cmd/arien/main.go
	GOOS=windows GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BINARY_NAME)-windows-amd64.exe cmd/arien/main.go

# Install the application
install: build
	@echo "Installing $(BINARY_NAME)..."
	cp $(BINARY_NAME) $(GOPATH)/bin/

# Run the application
run: build
	@echo "Running $(BINARY_NAME)..."
	./$(BINARY_NAME)

# Run in development mode with debug logging
dev: build
	@echo "Running $(BINARY_NAME) in development mode..."
	ARIEN_DEBUG=true ./$(BINARY_NAME)

# Clean build artifacts
clean:
	@echo "Cleaning..."
	$(GOCLEAN)
	rm -f $(BINARY_NAME)
	rm -f $(BINARY_NAME)-*

# Initialize Go module (for first-time setup)
init:
	@echo "Initializing Go module..."
	$(GOMOD) init arien

# Update dependencies
update:
	@echo "Updating dependencies..."
	$(GOMOD) get -u ./...
	$(GOMOD) tidy

# Generate documentation
docs:
	@echo "Generating documentation..."
	@if command -v godoc >/dev/null 2>&1; then \
		echo "Starting godoc server at http://localhost:6060"; \
		godoc -http=:6060; \
	else \
		echo "godoc not installed, install with: go install golang.org/x/tools/cmd/godoc@latest"; \
	fi

# Check for security vulnerabilities
security:
	@echo "Checking for security vulnerabilities..."
	@if command -v govulncheck >/dev/null 2>&1; then \
		govulncheck ./...; \
	else \
		echo "govulncheck not installed, install with: go install golang.org/x/vuln/cmd/govulncheck@latest"; \
	fi

# Run benchmarks
bench:
	@echo "Running benchmarks..."
	$(GOTEST) -bench=. -benchmem ./...

# Generate coverage report
coverage:
	@echo "Generating coverage report..."
	$(GOTEST) -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# Setup development environment
setup-dev:
	@echo "Setting up development environment..."
	@echo "Installing development tools..."
	$(GOGET) -u github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	$(GOGET) -u golang.org/x/tools/cmd/godoc@latest
	$(GOGET) -u golang.org/x/vuln/cmd/govulncheck@latest
	@echo "Development environment setup complete!"

# Release build (optimized)
release:
	@echo "Building release version..."
	CGO_ENABLED=0 $(GOBUILD) $(LDFLAGS) -a -installsuffix cgo -o $(BINARY_NAME) cmd/arien/main.go
	@echo "Release build complete: $(BINARY_NAME)"

# Docker build
docker:
	@echo "Building Docker image..."
	docker build -t arien:$(VERSION) .

# Show help
help:
	@echo "Arien - Elite AI-Powered Software Engineering Assistant"
	@echo ""
	@echo "Available targets:"
	@echo "  build      - Build the application"
	@echo "  build-all  - Build for multiple platforms"
	@echo "  clean      - Clean build artifacts"
	@echo "  deps       - Install dependencies"
	@echo "  dev        - Run in development mode"
	@echo "  docs       - Generate documentation"
	@echo "  fmt        - Format code"
	@echo "  help       - Show this help"
	@echo "  install    - Install the application"
	@echo "  lint       - Lint code"
	@echo "  release    - Build optimized release version"
	@echo "  run        - Build and run the application"
	@echo "  security   - Check for security vulnerabilities"
	@echo "  setup-dev  - Setup development environment"
	@echo "  test       - Run tests"
	@echo "  update     - Update dependencies"
	@echo ""
	@echo "Examples:"
	@echo "  make build          # Build the application"
	@echo "  make dev            # Run in development mode"
	@echo "  make test           # Run all tests"
	@echo "  make setup-dev      # Setup development tools"
